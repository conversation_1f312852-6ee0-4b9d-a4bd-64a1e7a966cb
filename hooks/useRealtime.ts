import { useEffect, useState, useCallback } from 'react';
import { RealtimeService } from '@/lib/realtime/realtime-service';
import { NotificationService } from '@/lib/notifications/notification-service';
import { useSupabase } from '@/lib/supabase/supabase-provider';

export interface RealtimeState {
  isConnected: boolean;
  isReconnecting: boolean;
  typingUsers: Map<string, { userId: string; userName: string; timestamp: number }>;
  onlineUsers: Map<string, { isOnline: boolean; lastSeen: Date }>;
  connectionInfo: any;
}

export function useRealtime() {
  const { supabase, user } = useSupabase();
  const [state, setState] = useState<RealtimeState>({
    isConnected: false,
    isReconnecting: false,
    typingUsers: new Map(),
    onlineUsers: new Map(),
    connectionInfo: null,
  });

  const realtimeService = RealtimeService.getInstance();
  const notificationService = NotificationService.getInstance();

  useEffect(() => {
    if (user && supabase) {
      initializeServices();
    }

    return () => {
      cleanup();
    };
  }, [user, supabase]);

  const initializeServices = async () => {
    try {
      // Initialize notification service
      await notificationService.initialize();

      // Initialize realtime service
      await realtimeService.initialize(supabase, user.id);

      // Set up event listeners
      setupEventListeners();

    } catch (error) {
      console.error('Error initializing realtime services:', error);
    }
  };

  const setupEventListeners = () => {
    // Connection status
    realtimeService.on('connection_status', (data: any) => {
      setState(prev => ({
        ...prev,
        isConnected: data.connected,
        isReconnecting: data.reconnected || false,
        connectionInfo: realtimeService.getConnectionInfo(),
      }));
    });

    // Connection error
    realtimeService.on('connection_error', (data: any) => {
      console.error('Realtime connection error:', data);
      setState(prev => ({
        ...prev,
        isConnected: false,
        isReconnecting: false,
      }));
    });

    // Typing indicators
    realtimeService.on('typing_indicator', (data: any) => {
      setState(prev => {
        const newTypingUsers = new Map(prev.typingUsers);
        
        if (data.isTyping) {
          newTypingUsers.set(data.conversationId, {
            userId: data.userId,
            userName: data.userName,
            timestamp: Date.now(),
          });
        } else {
          newTypingUsers.delete(data.conversationId);
        }

        return {
          ...prev,
          typingUsers: newTypingUsers,
        };
      });

      // Auto-remove typing indicator after 5 seconds
      setTimeout(() => {
        setState(prev => {
          const newTypingUsers = new Map(prev.typingUsers);
          const typingUser = newTypingUsers.get(data.conversationId);
          
          if (typingUser && Date.now() - typingUser.timestamp > 4000) {
            newTypingUsers.delete(data.conversationId);
          }

          return {
            ...prev,
            typingUsers: newTypingUsers,
          };
        });
      }, 5000);
    });

    // Online status
    realtimeService.on('user_online_status', (data: any) => {
      setState(prev => {
        const newOnlineUsers = new Map(prev.onlineUsers);
        newOnlineUsers.set(data.userId, {
          isOnline: data.isOnline,
          lastSeen: new Date(data.lastSeen),
        });

        return {
          ...prev,
          onlineUsers: newOnlineUsers,
        };
      });
    });

    // New messages
    realtimeService.on('new_message', (message: any) => {
      // Show notification if app is in background or message is not from current user
      if (message.sender_id !== user.id) {
        notificationService.sendLocalNotification({
          type: 'message',
          title: 'New Message',
          body: message.content,
          data: {
            conversationId: message.conversation_id,
            messageId: message.id,
          },
        });
      }
    });

    // Booking updates
    realtimeService.on('booking_realtime_update', (data: any) => {
      notificationService.sendLocalNotification({
        type: 'booking',
        title: 'Booking Update',
        body: `Your booking status has been updated to ${data.new.status}`,
        data: {
          bookingId: data.new.id,
        },
      });
    });

    // Emergency alerts
    realtimeService.on('emergency_update', (data: any) => {
      notificationService.sendLocalNotification({
        type: 'emergency',
        title: 'Emergency Alert',
        body: 'Emergency incident reported',
        priority: 'max',
        data: {
          incidentId: data.new.id,
        },
      });
    });

    // Location updates
    realtimeService.on('location_update', (data: any) => {
      // Handle location updates from companions during bookings
      console.log('Location update received:', data);
    });
  };

  const cleanup = () => {
    realtimeService.disconnect();
  };

  // Helper functions
  const startTyping = useCallback((conversationId: string, userName: string) => {
    if (user) {
      realtimeService.startTyping(conversationId, user.id, userName);
    }
  }, [user]);

  const stopTyping = useCallback((conversationId: string) => {
    if (user) {
      realtimeService.stopTyping(conversationId, user.id);
    }
  }, [user]);

  const markMessageAsRead = useCallback((messageId: string, conversationId: string) => {
    if (user) {
      realtimeService.markMessageAsRead(messageId, conversationId, user.id);
    }
  }, [user]);

  const shareLocation = useCallback((location: any) => {
    if (user) {
      realtimeService.shareLocation(user.id, location);
    }
  }, [user]);

  const joinRoom = useCallback((roomId: string) => {
    realtimeService.joinRoom(roomId);
  }, []);

  const leaveRoom = useCallback((roomId: string) => {
    realtimeService.leaveRoom(roomId);
  }, []);

  const triggerEmergencyAlert = useCallback((incidentData: any) => {
    if (user) {
      realtimeService.triggerEmergencyAlert(user.id, incidentData);
    }
  }, [user]);

  const retry = useCallback(() => {
    if (user && supabase) {
      initializeServices();
    }
  }, [user, supabase]);

  return {
    ...state,
    startTyping,
    stopTyping,
    markMessageAsRead,
    shareLocation,
    joinRoom,
    leaveRoom,
    triggerEmergencyAlert,
    retry,
  };
}