# EzyBackend Setup Guide for HourlyGF

This guide will help you set up EzyBackend as the backend service for the HourlyGF application, replacing Supabase.

## Prerequisites

1. **Node.js 20+** installed on your system
2. **MongoDB 4.4+** running locally or accessible remotely
3. **Redis 6.0+** for caching and real-time features
4. **Git** for cloning the EzyBackend repository

## Step 1: <PERSON>lone and Setup EzyBackend

```bash
# Clone your EzyBackend repository
git clone https://github.com/Jitenderkumar2030/ezybackend.git
cd ezybackend

# Install dependencies
npm install

# Create configuration files
mkdir -p config
```

## Step 2: Configure EzyBackend

Create the following configuration files in the `config` directory:

### config/ezybackend.json
```json
{
  "mongo": [{
    "host": "localhost",
    "port": "27017",
    "database": "hourlygf"
  }],
  "redis": [{
    "host": "127.0.0.1",
    "port": 6379
  }],
  "server": {
    "port": 4730,
    "host": "0.0.0.0"
  },
  "cors": {
    "origin": ["http://localhost:8081", "http://localhost:19006"],
    "credentials": true
  }
}
```

### config/smtp.json (for email notifications)
```json
{
  "provider": "mailgun",
  "apiKey": "YOUR_MAILGUN_API_KEY",
  "domain": "yourdomain.com",
  "fromEmail": "<EMAIL>",
  "fromName": "HourlyGF"
}
```

## Step 3: Start EzyBackend Services

```bash
# Start MongoDB (if not running as service)
mongod --dbpath /path/to/your/mongodb/data

# Start Redis (if not running as service)
redis-server

# Start EzyBackend
node server.js
```

EzyBackend should now be running on `http://localhost:4730`

## Step 4: Create HourlyGF Application

```bash
# Create the app using EzyBackend API
curl -H "Content-Type: application/json" -X POST \
-d '{"secureKey":"YOUR_SECURE_KEY"}' \
http://localhost:4730/app/hourlyGF
```

This will return your App ID and App Key. Save these for the next step.

## Step 5: Configure HourlyGF App

Create a `.env` file in your HourlyGF project root:

```env
# EzyBackend Configuration
EXPO_PUBLIC_EZYBACKEND_URL=http://localhost:4730
EXPO_PUBLIC_EZYBACKEND_APP_ID=hourlyGF
EXPO_PUBLIC_EZYBACKEND_APP_KEY=your-app-key-from-step-4

# Stripe Configuration (for payments)
EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key

# Other API Keys
EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_key
```

## Step 6: Run Database Migrations

The app includes migration scripts for EzyBackend. Run them to set up the database schema:

```bash
# In your HourlyGF project directory
node ezybackend-migrations/run-migrations.js
```

## Step 7: Start the HourlyGF App

```bash
# Install dependencies (if not already done)
npm install

# Start the development server
npm run dev
```

## Features Migrated from Supabase to EzyBackend

### ✅ Completed Migrations

1. **Authentication System**
   - User registration and login
   - Session management
   - Password reset functionality

2. **Database Operations**
   - CRUD operations for all entities
   - Query building and filtering
   - Relationships and joins

3. **Real-time Features**
   - Live chat messaging
   - Booking status updates
   - Notification system

4. **File Storage**
   - Profile photo uploads
   - Document storage for verification
   - Companion photo galleries

### 🔄 Compatibility Layer

The app includes a Supabase compatibility layer (`SupabaseAdapter`) that translates Supabase API calls to EzyBackend operations. This means:

- Existing code using `supabase.from('table')` will continue to work
- Authentication methods remain the same
- Real-time subscriptions are automatically converted

### 🆕 New Features Added

1. **Advanced Matching Algorithm**
   - Behavioral pattern analysis
   - Compatibility scoring
   - Personalized recommendations

2. **Customization Options**
   - Activity preferences
   - Date type selection
   - Anonymous browsing mode

3. **Video Calling**
   - WebRTC-based video calls
   - Audio-only calls
   - Call history tracking

4. **Loyalty Program**
   - Points system
   - Tier-based benefits
   - Reward redemption

5. **Social Media Integration**
   - Profile verification via social accounts
   - Social sharing capabilities
   - Enhanced trust scoring

6. **In-App Agreements**
   - Digital consent management
   - Community guidelines enforcement
   - Booking-specific terms

## API Differences

### Supabase vs EzyBackend

| Feature | Supabase | EzyBackend |
|---------|----------|------------|
| Authentication | `supabase.auth.signUp()` | `ezyBackend.signUp()` |
| Database Query | `supabase.from('table').select()` | `ezyBackend.find('table', query)` |
| Real-time | `supabase.channel().on()` | `ezyBackend.subscribe()` |
| File Upload | `supabase.storage.upload()` | `ezyBackend.uploadFile()` |

The compatibility layer handles these differences automatically.

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Ensure EzyBackend is running on the correct port
   - Check firewall settings
   - Verify MongoDB and Redis are accessible

2. **Authentication Errors**
   - Verify App ID and App Key are correct
   - Check that the app was created successfully in EzyBackend
   - Ensure session tokens are being stored properly

3. **Database Errors**
   - Run the migration scripts
   - Check MongoDB connection
   - Verify collection permissions

### Getting Help

1. Check EzyBackend logs: `tail -f logs/ezybackend.log`
2. Monitor MongoDB logs: `tail -f /var/log/mongodb/mongod.log`
3. Check Redis status: `redis-cli ping`

## Production Deployment

For production deployment:

1. Use environment variables for all configuration
2. Set up SSL certificates for HTTPS
3. Configure proper MongoDB replica sets
4. Set up Redis clustering for high availability
5. Use a process manager like PM2 for EzyBackend
6. Set up proper logging and monitoring

## Migration Checklist

- [ ] EzyBackend server running
- [ ] MongoDB and Redis configured
- [ ] HourlyGF app created in EzyBackend
- [ ] Environment variables set
- [ ] Database migrations run
- [ ] Authentication working
- [ ] Basic CRUD operations tested
- [ ] Real-time features tested
- [ ] File uploads working
- [ ] Payment integration tested

## Next Steps

After successful setup:

1. Test all existing functionality
2. Implement new features (matching algorithm, video calls, etc.)
3. Set up monitoring and analytics
4. Configure backup strategies
5. Plan for scaling and performance optimization

The migration provides a solid foundation for the enhanced HourlyGF application with improved features and better performance.
