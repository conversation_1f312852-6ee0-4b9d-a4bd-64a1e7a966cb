/**
 * EzyBackend Migration: Initial Schema for HourlyGF
 * This creates the basic tables needed for the HourlyGF application
 */

const migration = {
  up: async (db) => {
    // Users/Profiles table
    await db.createCollection('profiles', {
      validator: {
        $jsonSchema: {
          bsonType: 'object',
          required: ['email', 'full_name'],
          properties: {
            email: { bsonType: 'string' },
            full_name: { bsonType: 'string' },
            avatar_url: { bsonType: 'string' },
            phone: { bsonType: 'string' },
            date_of_birth: { bsonType: 'date' },
            gender: { enum: ['male', 'female', 'other', 'prefer_not_to_say'] },
            location: { bsonType: 'string' },
            bio: { bsonType: 'string' },
            is_companion: { bsonType: 'bool' },
            is_verified: { bsonType: 'bool' },
            verification_level: { enum: ['none', 'basic', 'enhanced', 'premium'] },
            created_at: { bsonType: 'date' },
            updated_at: { bsonType: 'date' }
          }
        }
      }
    });

    // Companions table
    await db.createCollection('companions', {
      validator: {
        $jsonSchema: {
          bsonType: 'object',
          required: ['profile_id', 'hourly_rate'],
          properties: {
            profile_id: { bsonType: 'objectId' },
            hourly_rate: { bsonType: 'number' },
            age: { bsonType: 'number' },
            height: { bsonType: 'string' },
            weight: { bsonType: 'string' },
            hair_color: { bsonType: 'string' },
            eye_color: { bsonType: 'string' },
            languages: { bsonType: 'array' },
            interests_array: { bsonType: 'array' },
            services_offered: { bsonType: 'array' },
            availability: { bsonType: 'object' },
            rating: { bsonType: 'number' },
            total_reviews: { bsonType: 'number' },
            total_bookings: { bsonType: 'number' },
            is_active: { bsonType: 'bool' },
            is_verified: { bsonType: 'bool' },
            verification_status: { enum: ['pending', 'verified', 'rejected'] },
            last_active: { bsonType: 'date' },
            created_at: { bsonType: 'date' },
            updated_at: { bsonType: 'date' }
          }
        }
      }
    });

    // Bookings table
    await db.createCollection('bookings', {
      validator: {
        $jsonSchema: {
          bsonType: 'object',
          required: ['user_id', 'companion_id', 'booking_time', 'duration_hours', 'total_amount'],
          properties: {
            user_id: { bsonType: 'objectId' },
            companion_id: { bsonType: 'objectId' },
            booking_time: { bsonType: 'date' },
            duration_hours: { bsonType: 'number' },
            total_amount: { bsonType: 'number' },
            status: { enum: ['pending', 'confirmed', 'in_progress', 'completed', 'cancelled'] },
            location: { bsonType: 'string' },
            special_requests: { bsonType: 'string' },
            payment_status: { enum: ['pending', 'paid', 'refunded', 'failed'] },
            payment_intent_id: { bsonType: 'string' },
            cancellation_reason: { bsonType: 'string' },
            cancelled_by: { bsonType: 'objectId' },
            cancelled_at: { bsonType: 'date' },
            created_at: { bsonType: 'date' },
            updated_at: { bsonType: 'date' }
          }
        }
      }
    });

    // Messages table
    await db.createCollection('messages', {
      validator: {
        $jsonSchema: {
          bsonType: 'object',
          required: ['conversation_id', 'sender_id', 'content'],
          properties: {
            conversation_id: { bsonType: 'objectId' },
            sender_id: { bsonType: 'objectId' },
            content: { bsonType: 'string' },
            message_type: { enum: ['text', 'image', 'file', 'system'] },
            is_read: { bsonType: 'bool' },
            read_at: { bsonType: 'date' },
            created_at: { bsonType: 'date' }
          }
        }
      }
    });

    // Conversations table
    await db.createCollection('conversations', {
      validator: {
        $jsonSchema: {
          bsonType: 'object',
          required: ['user_id', 'companion_id'],
          properties: {
            user_id: { bsonType: 'objectId' },
            companion_id: { bsonType: 'objectId' },
            booking_id: { bsonType: 'objectId' },
            last_message: { bsonType: 'string' },
            last_message_at: { bsonType: 'date' },
            is_active: { bsonType: 'bool' },
            created_at: { bsonType: 'date' },
            updated_at: { bsonType: 'date' }
          }
        }
      }
    });

    // Reviews table
    await db.createCollection('user_reviews', {
      validator: {
        $jsonSchema: {
          bsonType: 'object',
          required: ['user_id', 'companion_id', 'booking_id', 'overall_rating'],
          properties: {
            user_id: { bsonType: 'objectId' },
            companion_id: { bsonType: 'objectId' },
            booking_id: { bsonType: 'objectId' },
            overall_rating: { bsonType: 'number', minimum: 1, maximum: 5 },
            communication_rating: { bsonType: 'number', minimum: 1, maximum: 5 },
            punctuality_rating: { bsonType: 'number', minimum: 1, maximum: 5 },
            appearance_rating: { bsonType: 'number', minimum: 1, maximum: 5 },
            experience_rating: { bsonType: 'number', minimum: 1, maximum: 5 },
            title: { bsonType: 'string' },
            content: { bsonType: 'string' },
            photos: { bsonType: 'array' },
            is_verified: { bsonType: 'bool' },
            is_featured: { bsonType: 'bool' },
            helpful_count: { bsonType: 'number' },
            not_helpful_count: { bsonType: 'number' },
            response_from_companion: { bsonType: 'string' },
            response_date: { bsonType: 'date' },
            created_at: { bsonType: 'date' },
            updated_at: { bsonType: 'date' }
          }
        }
      }
    });

    // Companion Photos table
    await db.createCollection('companion_photos', {
      validator: {
        $jsonSchema: {
          bsonType: 'object',
          required: ['companion_id', 'photo_url'],
          properties: {
            companion_id: { bsonType: 'objectId' },
            photo_url: { bsonType: 'string' },
            is_primary: { bsonType: 'bool' },
            is_verified: { bsonType: 'bool' },
            order_index: { bsonType: 'number' },
            created_at: { bsonType: 'date' }
          }
        }
      }
    });

    // Create indexes for better performance
    await db.collection('profiles').createIndex({ email: 1 }, { unique: true });
    await db.collection('profiles').createIndex({ is_companion: 1 });
    await db.collection('companions').createIndex({ profile_id: 1 }, { unique: true });
    await db.collection('companions').createIndex({ is_active: 1, rating: -1 });
    await db.collection('companions').createIndex({ hourly_rate: 1 });
    await db.collection('bookings').createIndex({ user_id: 1, created_at: -1 });
    await db.collection('bookings').createIndex({ companion_id: 1, created_at: -1 });
    await db.collection('bookings').createIndex({ status: 1 });
    await db.collection('messages').createIndex({ conversation_id: 1, created_at: 1 });
    await db.collection('conversations').createIndex({ user_id: 1, companion_id: 1 }, { unique: true });
    await db.collection('user_reviews').createIndex({ companion_id: 1, created_at: -1 });
    await db.collection('companion_photos').createIndex({ companion_id: 1, order_index: 1 });

    console.log('Initial schema migration completed successfully');
  },

  down: async (db) => {
    // Drop collections in reverse order
    const collections = [
      'companion_photos',
      'user_reviews',
      'conversations',
      'messages',
      'bookings',
      'companions',
      'profiles'
    ];

    for (const collection of collections) {
      try {
        await db.collection(collection).drop();
        console.log(`Dropped collection: ${collection}`);
      } catch (error) {
        console.log(`Collection ${collection} does not exist or could not be dropped`);
      }
    }

    console.log('Initial schema rollback completed');
  }
};

module.exports = migration;
