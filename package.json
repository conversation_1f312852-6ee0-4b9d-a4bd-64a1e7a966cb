{"name": "hourly-gf-app", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "EXPO_NO_TELEMETRY=1 expo start", "build:web": "expo export --platform web", "lint": "expo lint"}, "dependencies": {"@expo-google-fonts/inter": "^0.2.3", "@expo/vector-icons": "^14.1.0", "@lucide/lab": "^0.1.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@supabase/supabase-js": "^2.39.6", "date-fns": "^3.3.1", "expo": "^53.0.0", "expo-blur": "~14.1.3", "expo-camera": "~16.1.5", "expo-constants": "~17.1.3", "expo-device": "~6.1.0", "expo-font": "~13.2.2", "expo-haptics": "~14.1.3", "expo-image": "~2.0.4", "expo-image-picker": "~15.0.5", "expo-linear-gradient": "~14.1.3", "expo-linking": "~7.1.3", "expo-location": "~18.1.0", "expo-notifications": "~0.30.1", "expo-router": "~5.0.2", "expo-secure-store": "~12.8.1", "expo-splash-screen": "~0.30.6", "expo-status-bar": "~2.2.2", "expo-symbols": "~0.4.3", "expo-system-ui": "~5.0.5", "expo-web-browser": "~14.1.5", "lucide-react-native": "^0.475.0", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.51.0", "react-native": "0.79.1", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.3.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "socket.io-client": "^4.7.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}}