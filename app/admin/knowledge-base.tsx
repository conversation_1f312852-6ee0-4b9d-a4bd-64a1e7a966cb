import { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, Alert, TextInput } from 'react-native';
import { useSupabase } from '@/lib/supabase/supabase-provider';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { 
  Plus, 
  Search, 
  FileText, 
  Eye, 
  ThumbsUp, 
  Edit3, 
  Trash2,
  Filter,
  BookOpen,
  TrendingUp
} from 'lucide-react-native';
import { Button } from '@/components/ui/Button';
import { router } from 'expo-router';

interface Article {
  id: string;
  title: string;
  excerpt: string;
  category: string;
  view_count: number;
  helpful_count: number;
  not_helpful_count: number;
  is_published: boolean;
  is_featured: boolean;
  created_at: string;
  updated_at: string;
}

export default function KnowledgeBaseAdmin() {
  const [articles, setArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showFilters, setShowFilters] = useState(false);
  const { supabase } = useSupabase();

  const categories = [
    { id: 'all', label: 'All Categories' },
    { id: 'getting_started', label: 'Getting Started' },
    { id: 'booking', label: 'Booking' },
    { id: 'payments', label: 'Payments' },
    { id: 'safety', label: 'Safety' },
    { id: 'technical', label: 'Technical' },
    { id: 'account', label: 'Account' },
    { id: 'policies', label: 'Policies' },
  ];

  useEffect(() => {
    fetchArticles();
  }, [selectedCategory, searchQuery]);

  const fetchArticles = async () => {
    try {
      setLoading(true);
      
      let query = supabase
        .from('help_articles')
        .select('*')
        .order('updated_at', { ascending: false });

      if (selectedCategory !== 'all') {
        query = query.eq('category', selectedCategory);
      }

      if (searchQuery.trim()) {
        query = query.or(`title.ilike.%${searchQuery}%,content.ilike.%${searchQuery}%`);
      }

      const { data, error } = await query;
      
      if (error) throw error;
      setArticles(data || []);
    } catch (error) {
      console.error('Error fetching articles:', error);
      Alert.alert('Error', 'Failed to load articles');
    } finally {
      setLoading(false);
    }
  };

  const togglePublishStatus = async (articleId: string, currentStatus: boolean) => {
    try {
      const { error } = await supabase
        .from('help_articles')
        .update({ 
          is_published: !currentStatus,
          published_at: !currentStatus ? new Date().toISOString() : null,
          updated_at: new Date().toISOString()
        })
        .eq('id', articleId);

      if (error) throw error;
      
      fetchArticles();
    } catch (error) {
      console.error('Error updating article:', error);
      Alert.alert('Error', 'Failed to update article status');
    }
  };

  const toggleFeaturedStatus = async (articleId: string, currentStatus: boolean) => {
    try {
      const { error } = await supabase
        .from('help_articles')
        .update({ 
          is_featured: !currentStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', articleId);

      if (error) throw error;
      
      fetchArticles();
    } catch (error) {
      console.error('Error updating article:', error);
      Alert.alert('Error', 'Failed to update featured status');
    }
  };

  const deleteArticle = async (articleId: string) => {
    Alert.alert(
      'Delete Article',
      'Are you sure you want to delete this article? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const { error } = await supabase
                .from('help_articles')
                .delete()
                .eq('id', articleId);

              if (error) throw error;
              
              fetchArticles();
            } catch (error) {
              console.error('Error deleting article:', error);
              Alert.alert('Error', 'Failed to delete article');
            }
          },
        },
      ]
    );
  };

  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      getting_started: '#10B981',
      booking: '#3B82F6',
      payments: '#F59E0B',
      safety: '#EF4444',
      technical: '#8B5CF6',
      account: '#06B6D4',
      policies: '#6B7280',
    };
    return colors[category] || '#6B7280';
  };

  const ArticleCard = ({ article }: { article: Article }) => (
    <View style={styles.articleCard}>
      <View style={styles.articleHeader}>
        <View style={styles.articleTitleRow}>
          <Text style={styles.articleTitle} numberOfLines={2}>
            {article.title}
          </Text>
          <View style={styles.articleBadges}>
            {article.is_featured && (
              <View style={styles.featuredBadge}>
                <Text style={styles.featuredText}>Featured</Text>
              </View>
            )}
            <View style={[
              styles.statusBadge,
              { backgroundColor: article.is_published ? '#D1FAE5' : '#FEE2E2' }
            ]}>
              <Text style={[
                styles.statusText,
                { color: article.is_published ? '#065F46' : '#991B1B' }
              ]}>
                {article.is_published ? 'Published' : 'Draft'}
              </Text>
            </View>
          </View>
        </View>
        
        <View style={[
          styles.categoryBadge,
          { backgroundColor: getCategoryColor(article.category) + '20' }
        ]}>
          <Text style={[
            styles.categoryText,
            { color: getCategoryColor(article.category) }
          ]}>
            {categories.find(c => c.id === article.category)?.label || article.category}
          </Text>
        </View>
      </View>

      <Text style={styles.articleExcerpt} numberOfLines={3}>
        {article.excerpt}
      </Text>

      <View style={styles.articleStats}>
        <View style={styles.statItem}>
          <Eye size={16} color="#6B7280" />
          <Text style={styles.statText}>{article.view_count}</Text>
        </View>
        <View style={styles.statItem}>
          <ThumbsUp size={16} color="#6B7280" />
          <Text style={styles.statText}>{article.helpful_count}</Text>
        </View>
        <View style={styles.statItem}>
          <TrendingUp size={16} color="#6B7280" />
          <Text style={styles.statText}>
            {article.helpful_count > 0 
              ? Math.round((article.helpful_count / (article.helpful_count + article.not_helpful_count)) * 100)
              : 0}%
          </Text>
        </View>
      </View>

      <View style={styles.articleActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => router.push(`/admin/articles/edit/${article.id}`)}
        >
          <Edit3 size={16} color="#B76E79" />
          <Text style={styles.actionText}>Edit</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => togglePublishStatus(article.id, article.is_published)}
        >
          <BookOpen size={16} color="#3B82F6" />
          <Text style={styles.actionText}>
            {article.is_published ? 'Unpublish' : 'Publish'}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => toggleFeaturedStatus(article.id, article.is_featured)}
        >
          <TrendingUp size={16} color="#F59E0B" />
          <Text style={styles.actionText}>
            {article.is_featured ? 'Unfeature' : 'Feature'}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.actionButton, styles.deleteButton]}
          onPress={() => deleteArticle(article.id)}
        >
          <Trash2 size={16} color="#EF4444" />
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style="dark" />
      
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Knowledge Base</Text>
        <TouchableOpacity
          style={styles.createButton}
          onPress={() => router.push('/admin/articles/create')}
        >
          <Plus size={20} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Search size={20} color="#6B7280" />
          <TextInput
            style={styles.searchInput}
            placeholder="Search articles..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#9CA3AF"
          />
        </View>
        <TouchableOpacity
          style={styles.filterButton}
          onPress={() => setShowFilters(!showFilters)}
        >
          <Filter size={20} color="#B76E79" />
        </TouchableOpacity>
      </View>

      {showFilters && (
        <View style={styles.filtersContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {categories.map((category) => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.filterChip,
                  selectedCategory === category.id && styles.filterChipActive
                ]}
                onPress={() => setSelectedCategory(category.id)}
              >
                <Text style={[
                  styles.filterChipText,
                  selectedCategory === category.id && styles.filterChipTextActive
                ]}>
                  {category.label}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}

      {loading ? (
        <View style={styles.centered}>
          <ActivityIndicator size="large" color="#B76E79" />
        </View>
      ) : (
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <View style={styles.statsRow}>
            <View style={styles.statCard}>
              <FileText size={24} color="#B76E79" />
              <Text style={styles.statValue}>{articles.length}</Text>
              <Text style={styles.statLabel}>Total Articles</Text>
            </View>
            <View style={styles.statCard}>
              <BookOpen size={24} color="#10B981" />
              <Text style={styles.statValue}>
                {articles.filter(a => a.is_published).length}
              </Text>
              <Text style={styles.statLabel}>Published</Text>
            </View>
            <View style={styles.statCard}>
              <TrendingUp size={24} color="#F59E0B" />
              <Text style={styles.statValue}>
                {articles.filter(a => a.is_featured).length}
              </Text>
              <Text style={styles.statLabel}>Featured</Text>
            </View>
          </View>

          <View style={styles.articlesList}>
            {articles.map((article) => (
              <ArticleCard key={article.id} article={article} />
            ))}
          </View>

          {articles.length === 0 && (
            <View style={styles.emptyState}>
              <FileText size={64} color="#D1D5DB" />
              <Text style={styles.emptyTitle}>No articles found</Text>
              <Text style={styles.emptySubtitle}>
                {searchQuery || selectedCategory !== 'all'
                  ? 'Try adjusting your search or filters'
                  : 'Create your first help article to get started'
                }
              </Text>
              {!searchQuery && selectedCategory === 'all' && (
                <Button
                  title="Create Article"
                  onPress={() => router.push('/admin/articles/create')}
                  style={styles.emptyButton}
                />
              )}
            </View>
          )}
        </ScrollView>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F3E9',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1A2E4C',
  },
  createButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#B76E79',
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    alignItems: 'center',
  },
  searchBar: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 44,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    color: '#1A2E4C',
  },
  filterButton: {
    width: 44,
    height: 44,
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  filtersContainer: {
    paddingHorizontal: 16,
    paddingBottom: 12,
  },
  filterChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  filterChipActive: {
    backgroundColor: '#B76E79',
    borderColor: '#B76E79',
  },
  filterChipText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  filterChipTextActive: {
    color: '#FFFFFF',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  statsRow: {
    flexDirection: 'row',
    marginBottom: 24,
    marginHorizontal: -6,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginHorizontal: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  statValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1A2E4C',
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#6B7280',
  },
  articlesList: {
    gap: 16,
  },
  articleCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  articleHeader: {
    marginBottom: 12,
  },
  articleTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  articleTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    flex: 1,
    marginRight: 12,
    lineHeight: 22,
  },
  articleBadges: {
    alignItems: 'flex-end',
    gap: 4,
  },
  featuredBadge: {
    backgroundColor: '#FEF3C7',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  featuredText: {
    fontSize: 12,
    color: '#92400E',
    fontWeight: '500',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  categoryBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  categoryText: {
    fontSize: 12,
    fontWeight: '500',
  },
  articleExcerpt: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
    marginBottom: 12,
  },
  articleStats: {
    flexDirection: 'row',
    marginBottom: 16,
    gap: 16,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    fontSize: 12,
    color: '#6B7280',
    marginLeft: 4,
  },
  articleActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: '#F9FAFB',
  },
  actionText: {
    fontSize: 12,
    color: '#4B5563',
    marginLeft: 4,
    fontWeight: '500',
  },
  deleteButton: {
    backgroundColor: '#FEE2E2',
    paddingHorizontal: 8,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 48,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 24,
  },
  emptyButton: {
    paddingHorizontal: 24,
  },
});