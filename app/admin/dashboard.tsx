import { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, Dimensions } from 'react-native';
import { useSupabase } from '@/lib/supabase/supabase-provider';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { 
  Users, 
  MessageSquare, 
  Video, 
  FileText, 
  Clock, 
  TrendingUp, 
  AlertCircle,
  CheckCircle,
  Phone,
  Star,
  BarChart3
} from 'lucide-react-native';
import { router } from 'expo-router';

const { width } = Dimensions.get('window');

interface DashboardStats {
  activeChats: number;
  pendingTickets: number;
  activeCalls: number;
  onlineAgents: number;
  avgResponseTime: string;
  customerSatisfaction: number;
  todayTickets: number;
  resolvedToday: number;
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [recentActivity, setRecentActivity] = useState<any[]>([]);
  const { supabase } = useSupabase();

  useEffect(() => {
    fetchDashboardData();
    
    // Set up real-time subscriptions
    const statsChannel = supabase
      .channel('dashboard_stats')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'live_chat_sessions' }, fetchDashboardData)
      .on('postgres_changes', { event: '*', schema: 'public', table: 'support_tickets' }, fetchDashboardData)
      .on('postgres_changes', { event: '*', schema: 'public', table: 'video_calls' }, fetchDashboardData)
      .subscribe();

    return () => {
      supabase.removeChannel(statsChannel);
    };
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch active chats
      const { data: activeChats } = await supabase
        .from('live_chat_sessions')
        .select('id')
        .in('status', ['waiting', 'active']);

      // Fetch pending tickets
      const { data: pendingTickets } = await supabase
        .from('support_tickets')
        .select('id')
        .in('status', ['open', 'in_progress']);

      // Fetch active calls
      const { data: activeCalls } = await supabase
        .from('video_calls')
        .select('id')
        .eq('status', 'active');

      // Fetch online agents
      const { data: onlineAgents } = await supabase
        .from('support_agents')
        .select('id')
        .eq('is_available', true);

      // Fetch today's tickets
      const today = new Date().toISOString().split('T')[0];
      const { data: todayTickets } = await supabase
        .from('support_tickets')
        .select('id, status')
        .gte('created_at', today);

      // Fetch recent activity
      const { data: recentTickets } = await supabase
        .from('support_tickets')
        .select(`
          id,
          title,
          status,
          priority,
          created_at,
          profiles(full_name)
        `)
        .order('created_at', { ascending: false })
        .limit(10);

      const resolvedToday = todayTickets?.filter(t => t.status === 'resolved').length || 0;

      setStats({
        activeChats: activeChats?.length || 0,
        pendingTickets: pendingTickets?.length || 0,
        activeCalls: activeCalls?.length || 0,
        onlineAgents: onlineAgents?.length || 0,
        avgResponseTime: '2.5 min',
        customerSatisfaction: 4.8,
        todayTickets: todayTickets?.length || 0,
        resolvedToday,
      });

      setRecentActivity(recentTickets || []);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const StatCard = ({ icon, title, value, subtitle, color = '#B76E79', onPress }: any) => (
    <TouchableOpacity style={[styles.statCard, { borderLeftColor: color }]} onPress={onPress}>
      <View style={styles.statHeader}>
        {icon}
        <Text style={styles.statTitle}>{title}</Text>
      </View>
      <Text style={styles.statValue}>{value}</Text>
      {subtitle && <Text style={styles.statSubtitle}>{subtitle}</Text>}
    </TouchableOpacity>
  );

  const ActivityItem = ({ item }: { item: any }) => {
    const getStatusColor = (status: string) => {
      switch (status) {
        case 'open': return '#3B82F6';
        case 'in_progress': return '#F59E0B';
        case 'resolved': return '#10B981';
        case 'closed': return '#6B7280';
        default: return '#6B7280';
      }
    };

    const getPriorityIcon = (priority: string) => {
      switch (priority) {
        case 'urgent': return <AlertCircle size={16} color="#EF4444" />;
        case 'high': return <AlertCircle size={16} color="#F59E0B" />;
        default: return <Clock size={16} color="#6B7280" />;
      }
    };

    return (
      <TouchableOpacity 
        style={styles.activityItem}
        onPress={() => router.push(`/admin/tickets/${item.id}`)}
      >
        <View style={styles.activityHeader}>
          {getPriorityIcon(item.priority)}
          <Text style={styles.activityTitle} numberOfLines={1}>
            {item.title}
          </Text>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) + '20' }]}>
            <Text style={[styles.statusText, { color: getStatusColor(item.status) }]}>
              {item.status}
            </Text>
          </View>
        </View>
        <Text style={styles.activityUser}>
          {item.profiles?.full_name || 'Unknown User'}
        </Text>
        <Text style={styles.activityTime}>
          {new Date(item.created_at).toLocaleString()}
        </Text>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color="#B76E79" />
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style="dark" />
      
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Support Dashboard</Text>
        <TouchableOpacity style={styles.refreshButton} onPress={fetchDashboardData}>
          <TrendingUp size={24} color="#B76E79" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Key Metrics */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Live Metrics</Text>
          <View style={styles.statsGrid}>
            <StatCard
              icon={<MessageSquare size={24} color="#3B82F6" />}
              title="Active Chats"
              value={stats?.activeChats}
              color="#3B82F6"
              onPress={() => router.push('/admin/chats')}
            />
            <StatCard
              icon={<FileText size={24} color="#F59E0B" />}
              title="Pending Tickets"
              value={stats?.pendingTickets}
              color="#F59E0B"
              onPress={() => router.push('/admin/tickets')}
            />
            <StatCard
              icon={<Video size={24} color="#10B981" />}
              title="Active Calls"
              value={stats?.activeCalls}
              color="#10B981"
              onPress={() => router.push('/admin/calls')}
            />
            <StatCard
              icon={<Users size={24} color="#8B5CF6" />}
              title="Online Agents"
              value={stats?.onlineAgents}
              color="#8B5CF6"
              onPress={() => router.push('/admin/agents')}
            />
          </View>
        </View>

        {/* Performance Metrics */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Performance</Text>
          <View style={styles.performanceGrid}>
            <View style={styles.performanceCard}>
              <Clock size={20} color="#B76E79" />
              <Text style={styles.performanceLabel}>Avg Response</Text>
              <Text style={styles.performanceValue}>{stats?.avgResponseTime}</Text>
            </View>
            <View style={styles.performanceCard}>
              <Star size={20} color="#FFB800" />
              <Text style={styles.performanceLabel}>Satisfaction</Text>
              <Text style={styles.performanceValue}>{stats?.customerSatisfaction}/5</Text>
            </View>
            <View style={styles.performanceCard}>
              <CheckCircle size={20} color="#10B981" />
              <Text style={styles.performanceLabel}>Resolved Today</Text>
              <Text style={styles.performanceValue}>{stats?.resolvedToday}/{stats?.todayTickets}</Text>
            </View>
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.actionsGrid}>
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={() => router.push('/admin/knowledge-base')}
            >
              <FileText size={24} color="#FFFFFF" />
              <Text style={styles.actionText}>Knowledge Base</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={() => router.push('/admin/analytics')}
            >
              <BarChart3 size={24} color="#FFFFFF" />
              <Text style={styles.actionText}>Analytics</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={() => router.push('/admin/agents')}
            >
              <Users size={24} color="#FFFFFF" />
              <Text style={styles.actionText}>Manage Agents</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={() => router.push('/admin/settings')}
            >
              <AlertCircle size={24} color="#FFFFFF" />
              <Text style={styles.actionText}>Settings</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Recent Activity */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Activity</Text>
            <TouchableOpacity onPress={() => router.push('/admin/tickets')}>
              <Text style={styles.seeAllText}>View All</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.activityList}>
            {recentActivity.map((item) => (
              <ActivityItem key={item.id} item={item} />
            ))}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F3E9',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1A2E4C',
  },
  refreshButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 16,
  },
  seeAllText: {
    fontSize: 14,
    color: '#B76E79',
    fontWeight: '500',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -6,
  },
  statCard: {
    width: (width - 44) / 2,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    margin: 6,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statTitle: {
    fontSize: 14,
    color: '#6B7280',
    marginLeft: 8,
    fontWeight: '500',
  },
  statValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1A2E4C',
    marginBottom: 4,
  },
  statSubtitle: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  performanceGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  performanceCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 4,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  performanceLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 8,
    marginBottom: 4,
  },
  performanceValue: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1A2E4C',
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -6,
  },
  actionButton: {
    width: (width - 44) / 2,
    backgroundColor: '#B76E79',
    borderRadius: 12,
    padding: 20,
    margin: 6,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  actionText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    marginTop: 8,
  },
  activityList: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  activityItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  activityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  activityTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1A2E4C',
    flex: 1,
    marginLeft: 8,
    marginRight: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  activityUser: {
    fontSize: 12,
    color: '#6B7280',
    marginBottom: 2,
  },
  activityTime: {
    fontSize: 12,
    color: '#9CA3AF',
  },
});