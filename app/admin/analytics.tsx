import { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, Dimensions } from 'react-native';
import { useSupabase } from '@/lib/supabase/supabase-provider';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Users, 
  MessageSquare, 
  Clock, 
  Star,
  Calendar,
  Filter,
  Download
} from 'lucide-react-native';

const { width } = Dimensions.get('window');

interface AnalyticsData {
  totalTickets: number;
  resolvedTickets: number;
  avgResponseTime: number;
  customerSatisfaction: number;
  activeChats: number;
  totalAgents: number;
  ticketTrends: any[];
  categoryBreakdown: any[];
  agentPerformance: any[];
  satisfactionTrends: any[];
}

export default function AnalyticsDashboard() {
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('7d');
  const [selectedMetric, setSelectedMetric] = useState('tickets');
  const { supabase } = useSupabase();

  const timeRanges = [
    { id: '24h', label: '24 Hours' },
    { id: '7d', label: '7 Days' },
    { id: '30d', label: '30 Days' },
    { id: '90d', label: '90 Days' },
  ];

  const metrics = [
    { id: 'tickets', label: 'Tickets', icon: BarChart3 },
    { id: 'chats', label: 'Chats', icon: MessageSquare },
    { id: 'satisfaction', label: 'Satisfaction', icon: Star },
    { id: 'response_time', label: 'Response Time', icon: Clock },
  ];

  useEffect(() => {
    fetchAnalyticsData();
  }, [timeRange]);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      
      const endDate = new Date();
      const startDate = new Date();
      
      switch (timeRange) {
        case '24h':
          startDate.setHours(startDate.getHours() - 24);
          break;
        case '7d':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(startDate.getDate() - 30);
          break;
        case '90d':
          startDate.setDate(startDate.getDate() - 90);
          break;
      }

      // Fetch tickets data
      const { data: tickets } = await supabase
        .from('support_tickets')
        .select('*')
        .gte('created_at', startDate.toISOString());

      // Fetch chats data
      const { data: chats } = await supabase
        .from('live_chat_sessions')
        .select('*')
        .gte('created_at', startDate.toISOString());

      // Fetch agents data
      const { data: agents } = await supabase
        .from('support_agents')
        .select('*');

      // Calculate metrics
      const totalTickets = tickets?.length || 0;
      const resolvedTickets = tickets?.filter(t => t.status === 'resolved').length || 0;
      const activeChats = chats?.filter(c => c.status === 'active').length || 0;
      
      // Calculate average response time (mock data for now)
      const avgResponseTime = 2.5; // minutes
      
      // Calculate customer satisfaction (mock data for now)
      const customerSatisfaction = 4.8;

      // Generate category breakdown
      const categoryBreakdown = [
        { category: 'Technical', count: Math.floor(totalTickets * 0.3), color: '#3B82F6' },
        { category: 'Billing', count: Math.floor(totalTickets * 0.25), color: '#10B981' },
        { category: 'Account', count: Math.floor(totalTickets * 0.2), color: '#F59E0B' },
        { category: 'Safety', count: Math.floor(totalTickets * 0.15), color: '#EF4444' },
        { category: 'General', count: Math.floor(totalTickets * 0.1), color: '#8B5CF6' },
      ];

      // Generate ticket trends (mock data)
      const ticketTrends = Array.from({ length: 7 }, (_, i) => ({
        date: new Date(Date.now() - (6 - i) * 24 * 60 * 60 * 1000).toLocaleDateString(),
        tickets: Math.floor(Math.random() * 20) + 10,
        resolved: Math.floor(Math.random() * 15) + 5,
      }));

      setData({
        totalTickets,
        resolvedTickets,
        avgResponseTime,
        customerSatisfaction,
        activeChats,
        totalAgents: agents?.length || 0,
        ticketTrends,
        categoryBreakdown,
        agentPerformance: [],
        satisfactionTrends: [],
      });
    } catch (error) {
      console.error('Error fetching analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const MetricCard = ({ title, value, subtitle, trend, icon: Icon, color = '#B76E79' }: any) => (
    <View style={[styles.metricCard, { borderLeftColor: color }]}>
      <View style={styles.metricHeader}>
        <Icon size={24} color={color} />
        <View style={styles.trendContainer}>
          {trend > 0 ? (
            <TrendingUp size={16} color="#10B981" />
          ) : (
            <TrendingDown size={16} color="#EF4444" />
          )}
          <Text style={[
            styles.trendText,
            { color: trend > 0 ? '#10B981' : '#EF4444' }
          ]}>
            {Math.abs(trend)}%
          </Text>
        </View>
      </View>
      <Text style={styles.metricValue}>{value}</Text>
      <Text style={styles.metricTitle}>{title}</Text>
      {subtitle && <Text style={styles.metricSubtitle}>{subtitle}</Text>}
    </View>
  );

  const CategoryChart = ({ data: categoryData }: { data: any[] }) => {
    const total = categoryData.reduce((sum, item) => sum + item.count, 0);
    
    return (
      <View style={styles.chartContainer}>
        <Text style={styles.chartTitle}>Tickets by Category</Text>
        <View style={styles.categoryChart}>
          {categoryData.map((item, index) => (
            <View key={index} style={styles.categoryItem}>
              <View style={styles.categoryRow}>
                <View style={[styles.categoryDot, { backgroundColor: item.color }]} />
                <Text style={styles.categoryLabel}>{item.category}</Text>
                <Text style={styles.categoryCount}>{item.count}</Text>
              </View>
              <View style={styles.categoryBar}>
                <View 
                  style={[
                    styles.categoryBarFill,
                    { 
                      backgroundColor: item.color,
                      width: `${(item.count / total) * 100}%`
                    }
                  ]} 
                />
              </View>
            </View>
          ))}
        </View>
      </View>
    );
  };

  const TrendChart = ({ data: trendData }: { data: any[] }) => (
    <View style={styles.chartContainer}>
      <Text style={styles.chartTitle}>Ticket Trends</Text>
      <View style={styles.trendChart}>
        {trendData.map((item, index) => (
          <View key={index} style={styles.trendItem}>
            <View style={styles.trendBars}>
              <View 
                style={[
                  styles.trendBar,
                  { 
                    height: (item.tickets / 30) * 60,
                    backgroundColor: '#3B82F6'
                  }
                ]} 
              />
              <View 
                style={[
                  styles.trendBar,
                  { 
                    height: (item.resolved / 30) * 60,
                    backgroundColor: '#10B981'
                  }
                ]} 
              />
            </View>
            <Text style={styles.trendLabel}>
              {item.date.split('/')[1]}/{item.date.split('/')[2]}
            </Text>
          </View>
        ))}
      </View>
      <View style={styles.trendLegend}>
        <View style={styles.legendItem}>
          <View style={[styles.legendDot, { backgroundColor: '#3B82F6' }]} />
          <Text style={styles.legendText}>Created</Text>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.legendDot, { backgroundColor: '#10B981' }]} />
          <Text style={styles.legendText}>Resolved</Text>
        </View>
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color="#B76E79" />
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style="dark" />
      
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Analytics</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity style={styles.headerButton}>
            <Filter size={20} color="#B76E79" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.headerButton}>
            <Download size={20} color="#B76E79" />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.timeRangeContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {timeRanges.map((range) => (
            <TouchableOpacity
              key={range.id}
              style={[
                styles.timeRangeButton,
                timeRange === range.id && styles.timeRangeButtonActive
              ]}
              onPress={() => setTimeRange(range.id)}
            >
              <Text style={[
                styles.timeRangeText,
                timeRange === range.id && styles.timeRangeTextActive
              ]}>
                {range.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Key Metrics */}
        <View style={styles.metricsGrid}>
          <MetricCard
            title="Total Tickets"
            value={data?.totalTickets}
            subtitle={`${data?.resolvedTickets} resolved`}
            trend={12}
            icon={BarChart3}
            color="#3B82F6"
          />
          <MetricCard
            title="Active Chats"
            value={data?.activeChats}
            trend={-5}
            icon={MessageSquare}
            color="#10B981"
          />
          <MetricCard
            title="Avg Response"
            value={`${data?.avgResponseTime}m`}
            trend={8}
            icon={Clock}
            color="#F59E0B"
          />
          <MetricCard
            title="Satisfaction"
            value={`${data?.customerSatisfaction}/5`}
            trend={3}
            icon={Star}
            color="#8B5CF6"
          />
        </View>

        {/* Charts */}
        <View style={styles.chartsContainer}>
          {data?.ticketTrends && <TrendChart data={data.ticketTrends} />}
          {data?.categoryBreakdown && <CategoryChart data={data.categoryBreakdown} />}
        </View>

        {/* Agent Performance */}
        <View style={styles.chartContainer}>
          <Text style={styles.chartTitle}>Agent Performance</Text>
          <View style={styles.agentList}>
            {[
              { name: 'Sarah Johnson', tickets: 45, satisfaction: 4.9, responseTime: '1.2m' },
              { name: 'Mike Chen', tickets: 38, satisfaction: 4.7, responseTime: '2.1m' },
              { name: 'Emily Davis', tickets: 42, satisfaction: 4.8, responseTime: '1.8m' },
              { name: 'Alex Rodriguez', tickets: 35, satisfaction: 4.6, responseTime: '2.5m' },
            ].map((agent, index) => (
              <View key={index} style={styles.agentItem}>
                <View style={styles.agentInfo}>
                  <Text style={styles.agentName}>{agent.name}</Text>
                  <Text style={styles.agentStats}>
                    {agent.tickets} tickets • {agent.satisfaction}/5 ⭐ • {agent.responseTime} avg
                  </Text>
                </View>
                <View style={styles.agentScore}>
                  <Text style={styles.agentScoreText}>
                    {Math.round(agent.satisfaction * 20)}%
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F3E9',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1A2E4C',
  },
  headerActions: {
    flexDirection: 'row',
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  timeRangeContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  timeRangeButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  timeRangeButtonActive: {
    backgroundColor: '#B76E79',
    borderColor: '#B76E79',
  },
  timeRangeText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  timeRangeTextActive: {
    color: '#FFFFFF',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -6,
    marginBottom: 24,
  },
  metricCard: {
    width: (width - 44) / 2,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    margin: 6,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  metricHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  trendText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 2,
  },
  metricValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1A2E4C',
    marginBottom: 4,
  },
  metricTitle: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  metricSubtitle: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 2,
  },
  chartsContainer: {
    gap: 24,
    marginBottom: 24,
  },
  chartContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    marginBottom: 16,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 16,
  },
  categoryChart: {
    gap: 12,
  },
  categoryItem: {
    gap: 8,
  },
  categoryRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  categoryLabel: {
    flex: 1,
    fontSize: 14,
    color: '#4B5563',
  },
  categoryCount: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1A2E4C',
  },
  categoryBar: {
    height: 6,
    backgroundColor: '#F3F4F6',
    borderRadius: 3,
    overflow: 'hidden',
  },
  categoryBarFill: {
    height: '100%',
    borderRadius: 3,
  },
  trendChart: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    height: 80,
    marginBottom: 16,
  },
  trendItem: {
    alignItems: 'center',
    flex: 1,
  },
  trendBars: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 2,
    marginBottom: 8,
  },
  trendBar: {
    width: 8,
    borderRadius: 4,
    minHeight: 4,
  },
  trendLabel: {
    fontSize: 10,
    color: '#6B7280',
  },
  trendLegend: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 16,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  legendDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 4,
  },
  legendText: {
    fontSize: 12,
    color: '#6B7280',
  },
  agentList: {
    gap: 12,
  },
  agentItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  agentInfo: {
    flex: 1,
  },
  agentName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 2,
  },
  agentStats: {
    fontSize: 12,
    color: '#6B7280',
  },
  agentScore: {
    backgroundColor: '#F0FDF4',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  agentScoreText: {
    fontSize: 12,
    color: '#166534',
    fontWeight: '600',
  },
});