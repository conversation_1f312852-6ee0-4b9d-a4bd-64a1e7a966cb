import { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, TextInput, FlatList, KeyboardAvoidingView, Platform, ActivityIndicator, Image } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useSupabase } from '@/lib/supabase/supabase-provider';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { ChevronLeft, Send, Phone, Video as VideoIcon } from 'lucide-react-native';
import { format } from 'date-fns';
import { Message } from '@/components/chat/Message';
import { TypingIndicator } from '@/components/realtime/TypingIndicator';
import { OnlineStatusIndicator } from '@/components/realtime/OnlineStatusIndicator';
import { ConnectionStatus } from '@/components/realtime/ConnectionStatus';
import { useRealtime } from '@/hooks/useRealtime';

export default function ChatScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const [conversation, setConversation] = useState<any>(null);
  const [messages, setMessages] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [sendingMessage, setSendingMessage] = useState(false);
  const [messageText, setMessageText] = useState('');
  const { supabase, user } = useSupabase();
  const router = useRouter();
  const flatListRef = useRef<FlatList>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  const {
    isConnected,
    isReconnecting,
    typingUsers,
    onlineUsers,
    startTyping,
    stopTyping,
    markMessageAsRead,
    joinRoom,
    leaveRoom,
    retry,
  } = useRealtime();

  useEffect(() => {
    if (id) {
      fetchConversation();
      fetchMessages();
      joinRoom(`conversation_${id}`);
      
      // Subscribe to new messages
      const channel = supabase
        .channel('new_messages')
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'messages',
            filter: `conversation_id=eq.${id}`,
          },
          (payload) => {
            const newMessage = payload.new;
            setMessages((prevMessages) => [...prevMessages, newMessage]);
            
            // Mark as read if not from current user
            if (newMessage.sender_id !== user?.id) {
              markMessageAsRead(newMessage.id, id);
            }
            
            setTimeout(() => {
              flatListRef.current?.scrollToEnd({ animated: true });
            }, 100);
          }
        )
        .subscribe();
        
      return () => {
        supabase.removeChannel(channel);
        leaveRoom(`conversation_${id}`);
        if (typingTimeoutRef.current) {
          clearTimeout(typingTimeoutRef.current);
        }
      };
    }
  }, [id]);

  const fetchConversation = async () => {
    try {
      const { data, error } = await supabase
        .from('conversations')
        .select(`
          *,
          companion:companions!companion_id(
            id,
            profiles(
              id,
              full_name,
              avatar_url
            )
          ),
          user:profiles!user_id(
            id,
            full_name,
            avatar_url
          )
        `)
        .eq('id', id)
        .single();
        
      if (error) {
        throw error;
      }
      
      setConversation(data);
    } catch (err) {
      console.error('Error fetching conversation:', err);
    }
  };

  const fetchMessages = async () => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('messages')
        .select('*')
        .eq('conversation_id', id)
        .order('created_at', { ascending: true });
        
      if (error) {
        throw error;
      }
      
      setMessages(data || []);
      
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: false });
      }, 200);
    } catch (err) {
      console.error('Error fetching messages:', err);
    } finally {
      setLoading(false);
    }
  };

  const sendMessage = async () => {
    if (!messageText.trim() || sendingMessage) return;
    
    try {
      setSendingMessage(true);
      
      const { data: userSession } = await supabase.auth.getSession();
      const userId = userSession.session?.user?.id;
      
      if (!userId) {
        throw new Error('User not authenticated');
      }
      
      const { error } = await supabase
        .from('messages')
        .insert([
          {
            conversation_id: id,
            sender_id: userId,
            content: messageText.trim(),
          },
        ]);
        
      if (error) {
        throw error;
      }
      
      // Update conversation timestamp
      await supabase
        .from('conversations')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', id);
        
      setMessageText('');
      stopTyping(id!);
    } catch (err) {
      console.error('Error sending message:', err);
    } finally {
      setSendingMessage(false);
    }
  };

  const handleTextChange = (text: string) => {
    setMessageText(text);
    
    // Handle typing indicators
    if (text.trim() && conversation) {
      const userName = user?.user_metadata?.full_name || 'User';
      startTyping(id!, userName);
      
      // Clear existing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      
      // Stop typing after 3 seconds of inactivity
      typingTimeoutRef.current = setTimeout(() => {
        stopTyping(id!);
      }, 3000);
    } else {
      stopTyping(id!);
    }
  };

  if (loading || !conversation) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color="#B76E79" />
      </View>
    );
  }

  const otherPerson = conversation.companion.profiles;
  const isOtherUserOnline = onlineUsers.get(otherPerson.id)?.isOnline || false;
  const isOtherUserTyping = typingUsers.has(id!) && typingUsers.get(id!)?.userId !== user?.id;

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style="dark" />
      
      <ConnectionStatus 
        isConnected={isConnected}
        isReconnecting={isReconnecting}
        onRetry={retry}
      />
      
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => router.back()}
        >
          <ChevronLeft size={24} color="#1A2E4C" />
        </TouchableOpacity>
        
        <View style={styles.headerProfile}>
          <View style={styles.avatarContainer}>
            {otherPerson.avatar_url ? (
              <Image source={{ uri: otherPerson.avatar_url }} style={styles.avatar} />
            ) : (
              <View style={styles.avatarPlaceholder}>
                <Text style={styles.avatarText}>
                  {otherPerson.full_name?.charAt(0) || '?'}
                </Text>
              </View>
            )}
            <OnlineStatusIndicator 
              isOnline={isOtherUserOnline}
              size="small"
              style={styles.onlineIndicator}
            />
          </View>
          <View style={styles.headerInfo}>
            <Text style={styles.headerName}>{otherPerson.full_name}</Text>
            <Text style={styles.headerStatus}>
              {isOtherUserOnline ? 'Online' : 'Offline'}
            </Text>
          </View>
        </View>
        
        <View style={styles.headerActions}>
          <TouchableOpacity style={styles.headerIcon}>
            <Phone size={20} color="#1A2E4C" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.headerIcon}>
            <VideoIcon size={20} color="#1A2E4C" />
          </TouchableOpacity>
        </View>
      </View>
      
      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
      >
        <FlatList
          ref={flatListRef}
          data={messages}
          keyExtractor={(item) => item.id.toString()}
          renderItem={({ item }) => (
            <Message
              message={item}
              isCurrentUser={item.sender_id === user?.id}
            />
          )}
          contentContainerStyle={styles.messageList}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={
            <View style={styles.emptyChat}>
              <Text style={styles.emptyChatText}>Start a conversation with {otherPerson.full_name}</Text>
            </View>
          }
          ListFooterComponent={
            isOtherUserTyping ? (
              <TypingIndicator 
                isVisible={true}
                userName={typingUsers.get(id!)?.userName}
              />
            ) : null
          }
        />
        
        <View style={styles.inputContainer}>
          <TextInput
            style={styles.input}
            placeholder="Type a message..."
            value={messageText}
            onChangeText={handleTextChange}
            multiline
            maxLength={500}
          />
          <TouchableOpacity 
            style={[
              styles.sendButton, 
              !messageText.trim() && styles.sendButtonDisabled
            ]}
            onPress={sendMessage}
            disabled={!messageText.trim() || sendingMessage}
          >
            {sendingMessage ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <Send size={20} color="#FFFFFF" />
            )}
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F3E9',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    backgroundColor: '#FFFFFF',
  },
  backButton: {
    marginRight: 12,
  },
  headerProfile: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  avatarPlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#B76E79',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: -2,
    right: -2,
  },
  headerInfo: {
    marginLeft: 12,
  },
  headerName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
  },
  headerStatus: {
    fontSize: 12,
    color: '#10B981',
  },
  headerActions: {
    flexDirection: 'row',
  },
  headerIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  messageList: {
    padding: 16,
    paddingBottom: 8,
  },
  emptyChat: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: 300,
  },
  emptyChatText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  input: {
    flex: 1,
    backgroundColor: '#F3F4F6',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    maxHeight: 100,
    fontSize: 16,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#B76E79',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  sendButtonDisabled: {
    backgroundColor: '#D1D5DB',
  },
});