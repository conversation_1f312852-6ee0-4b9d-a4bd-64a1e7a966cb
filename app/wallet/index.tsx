import { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, Alert } from 'react-native';
import { useSupabase } from '@/lib/supabase/supabase-provider';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { Wallet, Plus, ArrowUpRight, Gift, CreditCard, TrendingUp } from 'lucide-react-native';
import { WalletCard } from '@/components/payments/WalletCard';
import { TransactionItem } from '@/components/payments/TransactionItem';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { WalletService } from '@/lib/payments/wallet-service';
import { formatCurrency } from '@/lib/utils/formatters';
import { router } from 'expo-router';

export default function WalletScreen() {
  const [walletBalance, setWalletBalance] = useState<any>(null);
  const [transactions, setTransactions] = useState<any[]>([]);
  const [promoCodes, setPromoCodes] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [showBalance, setShowBalance] = useState(true);
  const [showAddFunds, setShowAddFunds] = useState(false);
  const [addAmount, setAddAmount] = useState('');
  const [promoCode, setPromoCode] = useState('');
  const { supabase, user } = useSupabase();
  
  const walletService = WalletService.getInstance();

  useEffect(() => {
    walletService.initialize(supabase);
    fetchWalletData();
  }, []);

  const fetchWalletData = async () => {
    try {
      setLoading(true);
      
      if (!user) return;
      
      const [balance, transactionHistory, availableCodes] = await Promise.all([
        walletService.getWalletBalance(user.id),
        walletService.getWalletTransactions(user.id, 20),
        walletService.getAvailablePromoCodes(user.id),
      ]);
      
      setWalletBalance(balance);
      setTransactions(transactionHistory);
      setPromoCodes(availableCodes);
    } catch (error) {
      console.error('Error fetching wallet data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddFunds = async () => {
    const amount = parseFloat(addAmount);
    if (!amount || amount <= 0) {
      Alert.alert('Error', 'Please enter a valid amount');
      return;
    }

    try {
      await walletService.addWalletCredit(
        user!.id,
        amount,
        'Wallet top-up',
        undefined,
        'manual_topup'
      );
      
      setAddAmount('');
      setShowAddFunds(false);
      fetchWalletData();
      
      Alert.alert('Success', `${formatCurrency(amount)} added to your wallet`);
    } catch (error) {
      console.error('Error adding funds:', error);
      Alert.alert('Error', 'Failed to add funds to wallet');
    }
  };

  const handleApplyPromoCode = async () => {
    if (!promoCode.trim()) {
      Alert.alert('Error', 'Please enter a promo code');
      return;
    }

    try {
      const result = await walletService.validatePromoCode(promoCode, user!.id, 100);
      
      if (result.valid && result.discountAmount) {
        await walletService.addWalletCredit(
          user!.id,
          result.discountAmount,
          `Promo code: ${promoCode}`,
          result.promoId,
          'promo_code'
        );
        
        setPromoCode('');
        fetchWalletData();
        
        Alert.alert('Success', `${formatCurrency(result.discountAmount)} added to your wallet!`);
      } else {
        Alert.alert('Error', result.error || 'Invalid promo code');
      }
    } catch (error) {
      console.error('Error applying promo code:', error);
      Alert.alert('Error', 'Failed to apply promo code');
    }
  };

  if (loading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color="#B76E79" />
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style="dark" />
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Wallet</Text>
          <Text style={styles.headerSubtitle}>
            Manage your credits and transactions
          </Text>
        </View>
        
        {/* Wallet Card */}
        {walletBalance && (
          <WalletCard
            balance={walletBalance.balance}
            pendingBalance={walletBalance.pendingBalance}
            currency={walletBalance.currency}
            onAddFunds={() => setShowAddFunds(true)}
            onViewTransactions={() => router.push('/wallet/transactions')}
            showBalance={showBalance}
            onToggleBalance={() => setShowBalance(!showBalance)}
          />
        )}
        
        {/* Add Funds Modal */}
        {showAddFunds && (
          <View style={styles.addFundsModal}>
            <Text style={styles.modalTitle}>Add Funds</Text>
            
            <Input
              label="Amount"
              value={addAmount}
              onChangeText={setAddAmount}
              placeholder="Enter amount"
              keyboardType="numeric"
            />
            
            <View style={styles.quickAmounts}>
              {[25, 50, 100, 200].map((amount) => (
                <TouchableOpacity
                  key={amount}
                  style={styles.quickAmountButton}
                  onPress={() => setAddAmount(amount.toString())}
                >
                  <Text style={styles.quickAmountText}>${amount}</Text>
                </TouchableOpacity>
              ))}
            </View>
            
            <View style={styles.modalActions}>
              <Button
                title="Cancel"
                variant="outline"
                onPress={() => setShowAddFunds(false)}
                style={styles.modalButton}
              />
              <Button
                title="Add Funds"
                onPress={handleAddFunds}
                style={styles.modalButton}
              />
            </View>
          </View>
        )}
        
        {/* Promo Codes */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Promo Codes</Text>
          
          <View style={styles.promoCodeInput}>
            <Input
              value={promoCode}
              onChangeText={setPromoCode}
              placeholder="Enter promo code"
              style={styles.promoInput}
            />
            <Button
              title="Apply"
              onPress={handleApplyPromoCode}
              size="small"
              style={styles.applyButton}
            />
          </View>
          
          {promoCodes.length > 0 && (
            <View style={styles.availableCodes}>
              <Text style={styles.availableCodesTitle}>Available Codes</Text>
              {promoCodes.slice(0, 3).map((code) => (
                <TouchableOpacity
                  key={code.id}
                  style={styles.promoCodeCard}
                  onPress={() => setPromoCode(code.code)}
                >
                  <Gift size={20} color="#10B981" />
                  <View style={styles.promoCodeInfo}>
                    <Text style={styles.promoCodeName}>{code.name}</Text>
                    <Text style={styles.promoCodeDescription}>{code.description}</Text>
                  </View>
                  <Text style={styles.promoCodeValue}>
                    {code.discountType === 'percentage' 
                      ? `${code.discountValue}% OFF`
                      : `${formatCurrency(code.discountValue)} OFF`
                    }
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>
        
        {/* Recent Transactions */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Transactions</Text>
            <TouchableOpacity onPress={() => router.push('/wallet/transactions')}>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.transactionsList}>
            {transactions.slice(0, 5).map((transaction) => (
              <TransactionItem
                key={transaction.id}
                transaction={{
                  id: transaction.id,
                  type: transaction.type,
                  status: 'completed',
                  amount: transaction.amount,
                  currency: walletBalance?.currency || 'USD',
                  description: transaction.description,
                  created_at: transaction.createdAt.toISOString(),
                }}
              />
            ))}
          </View>
          
          {transactions.length === 0 && (
            <View style={styles.emptyTransactions}>
              <Text style={styles.emptyTransactionsText}>No transactions yet</Text>
            </View>
          )}
        </View>
        
        {/* Spending Insights */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Spending Insights</Text>
          
          <View style={styles.insightsList}>
            <View style={styles.insightItem}>
              <TrendingUp size={20} color="#10B981" />
              <View style={styles.insightContent}>
                <Text style={styles.insightLabel}>This Month</Text>
                <Text style={styles.insightValue}>
                  {showBalance ? formatCurrency(245) : '•••'}
                </Text>
              </View>
            </View>
            
            <View style={styles.insightItem}>
              <CreditCard size={20} color="#3B82F6" />
              <View style={styles.insightContent}>
                <Text style={styles.insightLabel}>Average Booking</Text>
                <Text style={styles.insightValue}>
                  {showBalance ? formatCurrency(85) : '•••'}
                </Text>
              </View>
            </View>
          </View>
          
          <TouchableOpacity 
            style={styles.viewInsightsButton}
            onPress={() => router.push('/wallet/insights')}
          >
            <Text style={styles.viewInsightsText}>View Detailed Insights</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F3E9',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  header: {
    padding: 16,
    paddingBottom: 8,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1A2E4C',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 16,
  },
  section: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
  },
  seeAllText: {
    fontSize: 14,
    color: '#B76E79',
    fontWeight: '500',
  },
  addFundsModal: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    marginHorizontal: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 16,
  },
  quickAmounts: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 16,
  },
  quickAmountButton: {
    flex: 1,
    paddingVertical: 8,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    alignItems: 'center',
  },
  quickAmountText: {
    fontSize: 14,
    color: '#1A2E4C',
    fontWeight: '500',
  },
  modalActions: {
    flexDirection: 'row',
    gap: 12,
  },
  modalButton: {
    flex: 1,
  },
  promoCodeInput: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 12,
    marginBottom: 16,
  },
  promoInput: {
    flex: 1,
    marginBottom: 0,
  },
  applyButton: {
    marginBottom: 16,
  },
  availableCodes: {
    marginTop: 8,
  },
  availableCodesTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 12,
  },
  promoCodeCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F0FDF4',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  promoCodeInfo: {
    flex: 1,
    marginLeft: 12,
  },
  promoCodeName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#166534',
    marginBottom: 2,
  },
  promoCodeDescription: {
    fontSize: 12,
    color: '#166534',
  },
  promoCodeValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#10B981',
  },
  transactionsList: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    overflow: 'hidden',
  },
  emptyTransactions: {
    padding: 24,
    alignItems: 'center',
  },
  emptyTransactionsText: {
    fontSize: 14,
    color: '#6B7280',
  },
  insightsList: {
    gap: 16,
    marginBottom: 16,
  },
  insightItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  insightContent: {
    marginLeft: 12,
  },
  insightLabel: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 2,
  },
  insightValue: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
  },
  viewInsightsButton: {
    backgroundColor: '#F7F3E9',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  viewInsightsText: {
    fontSize: 14,
    color: '#B76E79',
    fontWeight: '500',
  },
});