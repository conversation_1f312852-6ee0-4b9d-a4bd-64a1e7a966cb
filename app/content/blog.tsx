import { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useSupabase } from '@/lib/supabase/supabase-provider';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { BookOpen, Filter, Search } from 'lucide-react-native';
import { BlogCard } from '@/components/content/BlogCard';
import { SearchBar } from '@/components/ui/SearchBar';
import { ContentService } from '@/lib/content/content-service';
import { router } from 'expo-router';

export default function BlogScreen() {
  const [articles, setArticles] = useState<any[]>([]);
  const [featuredArticles, setFeaturedArticles] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showFilters, setShowFilters] = useState(false);
  const { supabase, user } = useSupabase();
  
  const contentService = ContentService.getInstance();

  const categories = [
    { id: 'all', label: 'All' },
    { id: 'dating_tips', label: 'Dating Tips' },
    { id: 'safety', label: 'Safety' },
    { id: 'company_news', label: 'News' },
    { id: 'success_stories', label: 'Success Stories' },
    { id: 'guides', label: 'Guides' },
  ];

  useEffect(() => {
    contentService.initialize(supabase);
    fetchContent();
  }, [selectedCategory]);

  const fetchContent = async () => {
    try {
      setLoading(true);
      
      const [articlesData, featuredData] = await Promise.all([
        contentService.getPublishedArticles(
          selectedCategory === 'all' ? undefined : selectedCategory,
          20
        ),
        contentService.getFeaturedArticles(3),
      ]);
      
      setArticles(articlesData);
      setFeaturedArticles(featuredData);
    } catch (error) {
      console.error('Error fetching content:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleArticlePress = (article: any) => {
    contentService.trackContentView('article', article.id, user?.id);
    router.push(`/content/article/${article.slug}`);
  };

  const handleLike = (article: any) => {
    if (user) {
      contentService.likeArticle(article.id, user.id);
    }
  };

  const handleShare = (article: any) => {
    if (user) {
      contentService.shareArticle(article.id, 'native_share', user.id);
    }
  };

  const filteredArticles = articles.filter(article => {
    if (!searchQuery) return true;
    return article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
           article.excerpt?.toLowerCase().includes(searchQuery.toLowerCase());
  });

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <Text style={styles.headerTitle}>Blog & News</Text>
      <Text style={styles.headerSubtitle}>
        Dating tips, safety guides, and company updates
      </Text>
      
      <View style={styles.searchContainer}>
        <SearchBar
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholder="Search articles..."
        />
        <TouchableOpacity 
          style={styles.filterButton}
          onPress={() => setShowFilters(!showFilters)}
        >
          <Filter size={20} color="#B76E79" />
        </TouchableOpacity>
      </View>
      
      {showFilters && (
        <View style={styles.categoriesContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {categories.map((category) => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.categoryChip,
                  selectedCategory === category.id && styles.categoryChipActive
                ]}
                onPress={() => setSelectedCategory(category.id)}
              >
                <Text style={[
                  styles.categoryChipText,
                  selectedCategory === category.id && styles.categoryChipTextActive
                ]}>
                  {category.label}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}
      
      {/* Featured Articles */}
      {featuredArticles.length > 0 && (
        <View style={styles.featuredSection}>
          <Text style={styles.sectionTitle}>Featured Articles</Text>
          {featuredArticles.map((article) => (
            <BlogCard
              key={article.id}
              article={article}
              onPress={() => handleArticlePress(article)}
              onLike={() => handleLike(article)}
              onShare={() => handleShare(article)}
            />
          ))}
        </View>
      )}
      
      <Text style={styles.sectionTitle}>All Articles</Text>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color="#B76E79" />
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style="dark" />
      
      <FlatList
        data={filteredArticles}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <BlogCard
            article={item}
            onPress={() => handleArticlePress(item)}
            onLike={() => handleLike(item)}
            onShare={() => handleShare(item)}
          />
        )}
        ListHeaderComponent={renderHeader}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <BookOpen size={64} color="#D1D5DB" />
            <Text style={styles.emptyTitle}>No articles found</Text>
            <Text style={styles.emptySubtitle}>
              {searchQuery ? 'Try adjusting your search' : 'Check back later for new content'}
            </Text>
          </View>
        }
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F3E9',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerContainer: {
    padding: 16,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1A2E4C',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 20,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  filterButton: {
    width: 48,
    height: 48,
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  categoriesContainer: {
    marginBottom: 20,
  },
  categoryChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  categoryChipActive: {
    backgroundColor: '#B76E79',
    borderColor: '#B76E79',
  },
  categoryChipText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  categoryChipTextActive: {
    color: '#FFFFFF',
  },
  featuredSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 16,
  },
  listContent: {
    paddingBottom: 24,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 48,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
  },
});