import { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useSupabase } from '@/lib/supabase/supabase-provider';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { CircleHelp as HelpCircle, Search, Filter } from 'lucide-react-native';
import { FAQCard } from '@/components/content/FAQCard';
import { SearchBar } from '@/components/ui/SearchBar';
import { ContentService } from '@/lib/content/content-service';

export default function FAQScreen() {
  const [faqs, setFaqs] = useState<any[]>([]);
  const [featuredFaqs, setFeaturedFaqs] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showFilters, setShowFilters] = useState(false);
  const { supabase, user } = useSupabase();
  
  const contentService = ContentService.getInstance();

  const categories = contentService.getFAQCategories();

  useEffect(() => {
    contentService.initialize(supabase);
    fetchFAQs();
  }, [selectedCategory]);

  useEffect(() => {
    if (searchQuery) {
      searchFAQs();
    } else {
      fetchFAQs();
    }
  }, [searchQuery]);

  const fetchFAQs = async () => {
    try {
      setLoading(true);
      
      const [faqsData, featuredData] = await Promise.all([
        contentService.getFAQItems(selectedCategory === 'all' ? undefined : selectedCategory),
        contentService.getFeaturedFAQs(5),
      ]);
      
      setFaqs(faqsData);
      setFeaturedFaqs(featuredData);
    } catch (error) {
      console.error('Error fetching FAQs:', error);
    } finally {
      setLoading(false);
    }
  };

  const searchFAQs = async () => {
    try {
      setLoading(true);
      const results = await contentService.searchFAQs(searchQuery);
      setFaqs(results);
    } catch (error) {
      console.error('Error searching FAQs:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleMarkHelpful = (faqId: string, isHelpful: boolean) => {
    contentService.markFAQHelpful(faqId, isHelpful);
    
    // Update local state
    setFaqs(prev => prev.map(faq => 
      faq.id === faqId 
        ? {
            ...faq,
            helpfulCount: isHelpful ? faq.helpfulCount + 1 : faq.helpfulCount,
            notHelpfulCount: !isHelpful ? faq.notHelpfulCount + 1 : faq.notHelpfulCount,
          }
        : faq
    ));
  };

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <Text style={styles.headerTitle}>Frequently Asked Questions</Text>
      <Text style={styles.headerSubtitle}>
        Find answers to common questions about HourlyGF
      </Text>
      
      <View style={styles.searchContainer}>
        <SearchBar
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholder="Search FAQs..."
        />
        <TouchableOpacity 
          style={styles.filterButton}
          onPress={() => setShowFilters(!showFilters)}
        >
          <Filter size={20} color="#B76E79" />
        </TouchableOpacity>
      </View>
      
      {showFilters && (
        <View style={styles.categoriesContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <TouchableOpacity
              style={[
                styles.categoryChip,
                selectedCategory === 'all' && styles.categoryChipActive
              ]}
              onPress={() => setSelectedCategory('all')}
            >
              <Text style={[
                styles.categoryChipText,
                selectedCategory === 'all' && styles.categoryChipTextActive
              ]}>
                All
              </Text>
            </TouchableOpacity>
            {categories.map((category) => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.categoryChip,
                  selectedCategory === category.id && styles.categoryChipActive
                ]}
                onPress={() => setSelectedCategory(category.id)}
              >
                <Text style={[
                  styles.categoryChipText,
                  selectedCategory === category.id && styles.categoryChipTextActive
                ]}>
                  {category.label}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}
      
      {/* Featured FAQs */}
      {!searchQuery && featuredFaqs.length > 0 && (
        <View style={styles.featuredSection}>
          <Text style={styles.sectionTitle}>Popular Questions</Text>
          {featuredFaqs.map((faq) => (
            <FAQCard
              key={faq.id}
              faq={faq}
              onMarkHelpful={handleMarkHelpful}
            />
          ))}
        </View>
      )}
      
      {!searchQuery && <Text style={styles.sectionTitle}>All Questions</Text>}
    </View>
  );

  if (loading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color="#B76E79" />
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style="dark" />
      
      <FlatList
        data={faqs}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <FAQCard
            faq={item}
            onMarkHelpful={handleMarkHelpful}
          />
        )}
        ListHeaderComponent={renderHeader}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <HelpCircle size={64} color="#D1D5DB" />
            <Text style={styles.emptyTitle}>No FAQs found</Text>
            <Text style={styles.emptySubtitle}>
              {searchQuery ? 'Try different search terms' : 'Check back later for new content'}
            </Text>
          </View>
        }
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F3E9',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerContainer: {
    padding: 16,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1A2E4C',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 20,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  filterButton: {
    width: 48,
    height: 48,
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  categoriesContainer: {
    marginBottom: 20,
  },
  categoryChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  categoryChipActive: {
    backgroundColor: '#B76E79',
    borderColor: '#B76E79',
  },
  categoryChipText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  categoryChipTextActive: {
    color: '#FFFFFF',
  },
  featuredSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 16,
  },
  listContent: {
    paddingHorizontal: 16,
    paddingBottom: 24,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 48,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
  },
});