import { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, Image, Dimensions } from 'react-native';
import { useLocalSearchParams, useRouter, <PERSON> } from 'expo-router';
import { useSupabase } from '@/lib/supabase/supabase-provider';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { ChevronLeft, Star, Calendar, Clock, Heart, MessageCircle, Share } from 'lucide-react-native';
import Animated, { FadeIn, useAnimatedScrollHandler, useSharedValue } from 'react-native-reanimated';
import { Button } from '@/components/ui/Button';
import { formatCurrency } from '@/lib/utils/formatters';
import { OnlineStatusIndicator } from '@/components/realtime/OnlineStatusIndicator';
import { LiveLocationTracker } from '@/components/realtime/LiveLocationTracker';
import { useRealtime } from '@/hooks/useRealtime';

const AnimatedScrollView = Animated.createAnimatedComponent(ScrollView);
const { width: SCREEN_WIDTH } = Dimensions.get('window');

export default function CompanionDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const [companion, setCompanion] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isLocationSharing, setIsLocationSharing] = useState(false);
  const { supabase } = useSupabase();
  const router = useRouter();
  const scrollY = useSharedValue(0);

  const { onlineUsers } = useRealtime();

  useEffect(() => {
    fetchCompanion();
  }, [id]);

  const fetchCompanion = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('companions')
        .select(`
          *,
          profiles(*)
        `)
        .eq('id', id)
        .single();
        
      if (error) {
        throw error;
      }
      
      setCompanion(data);
    } catch (err: any) {
      console.error('Error fetching companion:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const scrollHandler = useAnimatedScrollHandler((event) => {
    scrollY.value = event.contentOffset.y;
  });

  const renderReviewStars = (rating: number) => {
    return Array(5)
      .fill(0)
      .map((_, i) => (
        <Star
          key={i}
          size={16}
          color={i < Math.floor(rating) ? '#FFB800' : '#D1D5DB'}
          fill={i < Math.floor(rating) ? '#FFB800' : 'none'}
        />
      ));
  };

  if (loading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color="#B76E79" />
      </View>
    );
  }

  if (error || !companion) {
    return (
      <View style={styles.centered}>
        <Text style={styles.errorText}>Failed to load companion details</Text>
        <TouchableOpacity style={styles.retryButton} onPress={fetchCompanion}>
          <Text style={styles.retryText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const profile = companion.profiles;
  const isOnline = onlineUsers.get(profile.id)?.isOnline || false;

  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => router.back()}
        >
          <ChevronLeft size={24} color="#FFFFFF" />
        </TouchableOpacity>
        
        <View style={styles.headerActions}>
          <TouchableOpacity style={styles.iconButton}>
            <Heart size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.iconButton}>
            <Share size={24} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </View>
      
      <View style={styles.imageContainer}>
        <Image
          source={{ uri: profile.avatar_url || 'https://images.pexels.com/photos/1065084/pexels-photo-1065084.jpeg' }}
          style={styles.coverImage}
        />
        <OnlineStatusIndicator 
          isOnline={isOnline}
          size="large"
          style={styles.onlineIndicator}
        />
      </View>
      
      <AnimatedScrollView
        onScroll={scrollHandler}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        <Animated.View 
          style={styles.profileCard}
          entering={FadeIn.duration(300).delay(200)}
        >
          <View style={styles.profileHeader}>
            <View>
              <View style={styles.nameRow}>
                <Text style={styles.name}>{profile.full_name}</Text>
                {isOnline && (
                  <View style={styles.onlineBadge}>
                    <Text style={styles.onlineText}>Online</Text>
                  </View>
                )}
              </View>
              <Text style={styles.location}>{companion.location || 'New York, NY'}</Text>
            </View>
            
            <View style={styles.ratingContainer}>
              <View style={styles.stars}>
                {renderReviewStars(companion.rating || 4.7)}
              </View>
              <Text style={styles.ratingText}>{companion.rating || 4.7} ({companion.reviews_count || 48})</Text>
            </View>
          </View>
          
          <View style={styles.infoRow}>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Age</Text>
              <Text style={styles.infoValue}>{companion.age || 24}</Text>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Height</Text>
              <Text style={styles.infoValue}>{companion.height || "5'7\""}</Text>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Languages</Text>
              <Text style={styles.infoValue}>{companion.languages || 'English, Spanish'}</Text>
            </View>
          </View>
          
          <View style={styles.interestsContainer}>
            <Text style={styles.sectionTitle}>Interests</Text>
            <View style={styles.interestTags}>
              {(companion.interests_array || ['Movies', 'Music', 'Hiking', 'Art', 'Cooking']).map((interest: string, index: number) => (
                <View key={index} style={styles.interestTag}>
                  <Text style={styles.interestText}>{interest}</Text>
                </View>
              ))}
            </View>
          </View>
          
          <View style={styles.aboutContainer}>
            <Text style={styles.sectionTitle}>About</Text>
            <Text style={styles.aboutText}>
              {companion.bio || 'Hi there! I\'m an outgoing and friendly companion who loves to create memorable experiences. I enjoy deep conversations, exploring new places, and trying new cuisines. Whether you're looking for someone to accompany you to an event, enjoy a nice dinner, or just spend quality time together, I\'m here to make your day special.'}
            </Text>
          </View>
          
          <LiveLocationTracker
            companionId={id}
            isSharing={isLocationSharing}
            onSharingChange={setIsLocationSharing}
          />
          
          <View style={styles.availabilityContainer}>
            <Text style={styles.sectionTitle}>Availability</Text>
            <View style={styles.calendarRow}>
              {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day, index) => (
                <View key={index} style={[
                  styles.dayItem,
                  index < 5 && styles.availableDay
                ]}>
                  <Text style={[
                    styles.dayText,
                    index < 5 && styles.availableDayText
                  ]}>{day}</Text>
                </View>
              ))}
            </View>
            <View style={styles.hoursContainer}>
              <View style={styles.hourItem}>
                <Clock size={16} color="#B76E79" />
                <Text style={styles.hourText}>Available 5:00 PM - 11:00 PM</Text>
              </View>
            </View>
          </View>
          
          <View style={styles.reviewsContainer}>
            <Text style={styles.sectionTitle}>Reviews</Text>
            <TouchableOpacity style={styles.reviewsLink}>
              <Text style={styles.reviewsLinkText}>See all reviews ({companion.reviews_count || 48})</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </AnimatedScrollView>
      
      <SafeAreaView edges={['bottom']} style={styles.bottomBar}>
        <View style={styles.priceContainer}>
          <Text style={styles.priceLabel}>Hourly Rate</Text>
          <Text style={styles.price}>{formatCurrency(companion.hourly_rate || 60)}</Text>
        </View>
        
        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.chatButton}>
            <MessageCircle size={20} color="#B76E79" />
          </TouchableOpacity>
          
          <Link href={`/(companion)/book?id=${id}`} asChild style={{ flex: 1 }}>
            <Button title="Book Now" />
          </Link>
        </View>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F3E9',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#B91C1C',
    marginBottom: 12,
  },
  retryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#B76E79',
    borderRadius: 8,
  },
  retryText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    paddingTop: 50,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerActions: {
    flexDirection: 'row',
  },
  iconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  imageContainer: {
    position: 'relative',
  },
  coverImage: {
    width: SCREEN_WIDTH,
    height: SCREEN_WIDTH * 1.1,
    resizeMode: 'cover',
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 20,
    right: 20,
  },
  scrollContent: {
    paddingBottom: 100,
  },
  profileCard: {
    marginTop: -40,
    backgroundColor: '#F7F3E9',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    padding: 24,
  },
  profileHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  name: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1A2E4C',
    marginRight: 12,
  },
  onlineBadge: {
    backgroundColor: '#D1FAE5',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  onlineText: {
    fontSize: 12,
    color: '#065F46',
    fontWeight: '500',
  },
  location: {
    fontSize: 14,
    color: '#6B7280',
  },
  ratingContainer: {
    alignItems: 'flex-end',
  },
  stars: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  ratingText: {
    fontSize: 14,
    color: '#6B7280',
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  infoItem: {
    alignItems: 'center',
  },
  infoLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 12,
  },
  interestsContainer: {
    marginBottom: 24,
  },
  interestTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  interestTag: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    marginBottom: 8,
  },
  interestText: {
    fontSize: 14,
    color: '#1A2E4C',
  },
  aboutContainer: {
    marginBottom: 24,
  },
  aboutText: {
    fontSize: 15,
    lineHeight: 22,
    color: '#4B5563',
  },
  availabilityContainer: {
    marginBottom: 24,
  },
  calendarRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  dayItem: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
  },
  availableDay: {
    backgroundColor: '#B76E79',
  },
  dayText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6B7280',
  },
  availableDayText: {
    color: '#FFFFFF',
  },
  hoursContainer: {
    marginTop: 8,
  },
  hourItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  hourText: {
    fontSize: 14,
    color: '#4B5563',
    marginLeft: 8,
  },
  reviewsContainer: {
    marginBottom: 24,
  },
  reviewsLink: {
    alignSelf: 'flex-start',
  },
  reviewsLinkText: {
    fontSize: 14,
    color: '#B76E79',
    fontWeight: '500',
  },
  bottomBar: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    paddingHorizontal: 24,
    paddingTop: 16,
    paddingBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  priceContainer: {
    flex: 1,
  },
  priceLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginBottom: 2,
  },
  price: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1A2E4C',
  },
  actionButtons: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  chatButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    borderWidth: 1,
    borderColor: '#B76E79',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
});