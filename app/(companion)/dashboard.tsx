import { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, Dimensions } from 'react-native';
import { useSupabase } from '@/lib/supabase/supabase-provider';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { 
  DollarSign, 
  Calendar, 
  Star, 
  TrendingUp, 
  Users, 
  Clock,
  Eye,
  MessageCircle,
  Award,
  Settings
} from 'lucide-react-native';
import { CompanionService } from '@/lib/companion/companion-service';
import { formatCurrency } from '@/lib/utils/formatters';
import { router } from 'expo-router';

const { width } = Dimensions.get('window');

export default function CompanionDashboard() {
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [companion, setCompanion] = useState<any>(null);
  const { supabase, user } = useSupabase();
  
  const companionService = CompanionService.getInstance();

  useEffect(() => {
    companionService.initialize(supabase);
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Get companion profile
      const { data: companionData } = await supabase
        .from('companions')
        .select('*')
        .eq('profile_id', user?.id)
        .single();

      if (!companionData) return;
      
      setCompanion(companionData);
      
      // Get dashboard data
      const data = await companionService.getDashboardData(companionData.id);
      setDashboardData(data);
      
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const MetricCard = ({ title, value, subtitle, icon: Icon, color = '#B76E79', onPress }: any) => (
    <TouchableOpacity 
      style={[styles.metricCard, { borderLeftColor: color }]}
      onPress={onPress}
    >
      <View style={styles.metricHeader}>
        <Icon size={24} color={color} />
      </View>
      <Text style={styles.metricValue}>{value}</Text>
      <Text style={styles.metricTitle}>{title}</Text>
      {subtitle && <Text style={styles.metricSubtitle}>{subtitle}</Text>}
    </TouchableOpacity>
  );

  const QuickAction = ({ title, icon: Icon, color, onPress }: any) => (
    <TouchableOpacity style={styles.quickAction} onPress={onPress}>
      <Icon size={24} color={color} />
      <Text style={styles.quickActionText}>{title}</Text>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color="#B76E79" />
      </View>
    );
  }

  if (!companion) {
    return (
      <View style={styles.centered}>
        <Text style={styles.errorText}>Companion profile not found</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style="dark" />
      
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Dashboard</Text>
        <TouchableOpacity 
          style={styles.settingsButton}
          onPress={() => router.push('/(companion)/settings')}
        >
          <Settings size={24} color="#1A2E4C" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Key Metrics */}
        <View style={styles.metricsGrid}>
          <MetricCard
            title="This Month"
            value={formatCurrency(dashboardData?.thisMonthEarnings || 0)}
            subtitle="Earnings"
            icon={DollarSign}
            color="#10B981"
            onPress={() => router.push('/(companion)/earnings')}
          />
          <MetricCard
            title="Available"
            value={formatCurrency(dashboardData?.availableEarnings || 0)}
            subtitle="For payout"
            icon={TrendingUp}
            color="#3B82F6"
            onPress={() => router.push('/(companion)/payouts')}
          />
          <MetricCard
            title="Bookings"
            value={dashboardData?.thisMonthBookings || 0}
            subtitle="This month"
            icon={Calendar}
            color="#F59E0B"
            onPress={() => router.push('/(tabs)/bookings')}
          />
          <MetricCard
            title="Rating"
            value={`${dashboardData?.averageRating?.toFixed(1) || 0}/5`}
            subtitle={`${companion.reviews_count || 0} reviews`}
            icon={Star}
            color="#8B5CF6"
            onPress={() => router.push('/(companion)/reviews')}
          />
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActionsGrid}>
            <QuickAction
              title="Set Availability"
              icon={Calendar}
              color="#3B82F6"
              onPress={() => router.push('/(companion)/availability')}
            />
            <QuickAction
              title="View Analytics"
              icon={Eye}
              color="#10B981"
              onPress={() => router.push('/(companion)/analytics')}
            />
            <QuickAction
              title="Messages"
              icon={MessageCircle}
              color="#F59E0B"
              onPress={() => router.push('/(tabs)/messages')}
            />
            <QuickAction
              title="Verification"
              icon={Award}
              color="#8B5CF6"
              onPress={() => router.push('/(companion)/verification')}
            />
          </View>
        </View>

        {/* Performance Overview */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Performance Overview</Text>
          
          <View style={styles.performanceList}>
            <View style={styles.performanceItem}>
              <Text style={styles.performanceLabel}>Response Rate</Text>
              <Text style={styles.performanceValue}>{dashboardData?.responseRate || 95}%</Text>
            </View>
            <View style={styles.performanceItem}>
              <Text style={styles.performanceLabel}>Completion Rate</Text>
              <Text style={styles.performanceValue}>{dashboardData?.completionRate?.toFixed(1) || 0}%</Text>
            </View>
            <View style={styles.performanceItem}>
              <Text style={styles.performanceLabel}>Profile Views</Text>
              <Text style={styles.performanceValue}>1,234</Text>
            </View>
          </View>
        </View>

        {/* Upcoming Bookings */}
        {dashboardData?.upcomingBookings?.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Upcoming Bookings</Text>
              <TouchableOpacity onPress={() => router.push('/(tabs)/bookings')}>
                <Text style={styles.seeAllText}>See All</Text>
              </TouchableOpacity>
            </View>
            
            {dashboardData.upcomingBookings.slice(0, 3).map((booking: any) => (
              <View key={booking.id} style={styles.bookingCard}>
                <View style={styles.bookingInfo}>
                  <Text style={styles.bookingUser}>{booking.profiles.full_name}</Text>
                  <Text style={styles.bookingTime}>
                    {new Date(booking.booking_time).toLocaleDateString()} at{' '}
                    {new Date(booking.booking_time).toLocaleTimeString([], { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </Text>
                  <Text style={styles.bookingDuration}>
                    {booking.duration_hours} hours • {formatCurrency(booking.total_amount)}
                  </Text>
                </View>
                <TouchableOpacity style={styles.bookingAction}>
                  <Text style={styles.bookingActionText}>View</Text>
                </TouchableOpacity>
              </View>
            ))}
          </View>
        )}

        {/* Recent Reviews */}
        {dashboardData?.recentReviews?.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Recent Reviews</Text>
              <TouchableOpacity onPress={() => router.push('/(companion)/reviews')}>
                <Text style={styles.seeAllText}>See All</Text>
              </TouchableOpacity>
            </View>
            
            {dashboardData.recentReviews.slice(0, 2).map((review: any) => (
              <View key={review.id} style={styles.reviewCard}>
                <View style={styles.reviewHeader}>
                  <Text style={styles.reviewUser}>{review.profiles.full_name}</Text>
                  <View style={styles.reviewRating}>
                    <Star size={14} color="#FFB800" fill="#FFB800" />
                    <Text style={styles.reviewRatingText}>{review.overall_rating}/5</Text>
                  </View>
                </View>
                <Text style={styles.reviewContent} numberOfLines={2}>
                  {review.content}
                </Text>
                <Text style={styles.reviewDate}>
                  {new Date(review.created_at).toLocaleDateString()}
                </Text>
              </View>
            ))}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F3E9',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#B91C1C',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1A2E4C',
  },
  settingsButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -6,
    marginBottom: 24,
  },
  metricCard: {
    width: (width - 44) / 2,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    margin: 6,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  metricHeader: {
    marginBottom: 8,
  },
  metricValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1A2E4C',
    marginBottom: 4,
  },
  metricTitle: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  metricSubtitle: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 2,
  },
  section: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
  },
  seeAllText: {
    fontSize: 14,
    color: '#B76E79',
    fontWeight: '500',
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -6,
  },
  quickAction: {
    width: (width - 44) / 2,
    backgroundColor: '#F7F3E9',
    borderRadius: 12,
    padding: 16,
    margin: 6,
    alignItems: 'center',
  },
  quickActionText: {
    fontSize: 14,
    color: '#1A2E4C',
    fontWeight: '500',
    marginTop: 8,
  },
  performanceList: {
    gap: 12,
  },
  performanceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  performanceLabel: {
    fontSize: 14,
    color: '#6B7280',
  },
  performanceValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
  },
  bookingCard: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  bookingInfo: {
    flex: 1,
  },
  bookingUser: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 2,
  },
  bookingTime: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 2,
  },
  bookingDuration: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  bookingAction: {
    backgroundColor: '#B76E79',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  bookingActionText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
  },
  reviewCard: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  reviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  reviewUser: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1A2E4C',
  },
  reviewRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  reviewRatingText: {
    fontSize: 12,
    color: '#6B7280',
    marginLeft: 4,
  },
  reviewContent: {
    fontSize: 14,
    color: '#4B5563',
    lineHeight: 18,
    marginBottom: 4,
  },
  reviewDate: {
    fontSize: 12,
    color: '#9CA3AF',
  },
});