import { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, Dimensions } from 'react-native';
import { useSupabase } from '@/lib/supabase/supabase-provider';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Calendar, 
  Star, 
  Users,
  Eye,
  MessageCircle,
  Clock,
  Award
} from 'lucide-react-native';
import { CompanionService } from '@/lib/companion/companion-service';
import { AnalyticsService } from '@/lib/analytics/analytics-service';
import { formatCurrency } from '@/lib/utils/formatters';

const { width } = Dimensions.get('window');

export default function CompanionAnalyticsScreen() {
  const [analytics, setAnalytics] = useState<any>(null);
  const [earnings, setEarnings] = useState<any[]>([]);
  const [timeRange, setTimeRange] = useState('30d');
  const [loading, setLoading] = useState(true);
  const { supabase, user } = useSupabase();
  
  const companionService = CompanionService.getInstance();
  const analyticsService = AnalyticsService.getInstance();

  const timeRanges = [
    { id: '7d', label: '7 Days', days: 7 },
    { id: '30d', label: '30 Days', days: 30 },
    { id: '90d', label: '90 Days', days: 90 },
    { id: '1y', label: '1 Year', days: 365 },
  ];

  useEffect(() => {
    companionService.initialize(supabase);
    analyticsService.initialize(supabase, user?.id);
    fetchAnalytics();
  }, [timeRange]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      
      // Get companion ID
      const { data: companion } = await supabase
        .from('companions')
        .select('id')
        .eq('profile_id', user?.id)
        .single();

      if (!companion) return;

      const selectedRange = timeRanges.find(r => r.id === timeRange);
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - (selectedRange?.days || 30));

      // Fetch analytics data
      const [analyticsData, earningsData, dashboardData] = await Promise.all([
        companionService.getCompanionAnalytics(companion.id, startDate, endDate),
        companionService.getEarnings(companion.id, undefined, 100),
        companionService.getDashboardData(companion.id),
      ]);

      setAnalytics(analyticsData);
      setEarnings(earningsData);
      
    } catch (error) {
      console.error('Error fetching analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const MetricCard = ({ title, value, subtitle, trend, icon: Icon, color = '#B76E79' }: any) => (
    <View style={[styles.metricCard, { borderLeftColor: color }]}>
      <View style={styles.metricHeader}>
        <Icon size={24} color={color} />
        <View style={styles.trendContainer}>
          {trend > 0 ? (
            <TrendingUp size={16} color="#10B981" />
          ) : (
            <TrendingDown size={16} color="#EF4444" />
          )}
          <Text style={[
            styles.trendText,
            { color: trend > 0 ? '#10B981' : '#EF4444' }
          ]}>
            {Math.abs(trend)}%
          </Text>
        </View>
      </View>
      <Text style={styles.metricValue}>{value}</Text>
      <Text style={styles.metricTitle}>{title}</Text>
      {subtitle && <Text style={styles.metricSubtitle}>{subtitle}</Text>}
    </View>
  );

  const EarningsChart = ({ data }: { data: any[] }) => {
    if (!data.length) return null;

    const maxEarning = Math.max(...data.map(d => d.netAmount));
    
    return (
      <View style={styles.chartContainer}>
        <Text style={styles.chartTitle}>Earnings Trend</Text>
        <View style={styles.chart}>
          {data.slice(-7).map((earning, index) => (
            <View key={index} style={styles.chartBar}>
              <View 
                style={[
                  styles.bar,
                  { 
                    height: (earning.netAmount / maxEarning) * 60,
                    backgroundColor: '#10B981'
                  }
                ]} 
              />
              <Text style={styles.chartLabel}>
                {new Date(earning.createdAt).getDate()}
              </Text>
            </View>
          ))}
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color="#B76E79" />
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style="dark" />
      
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Analytics</Text>
      </View>

      <View style={styles.timeRangeContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {timeRanges.map((range) => (
            <TouchableOpacity
              key={range.id}
              style={[
                styles.timeRangeButton,
                timeRange === range.id && styles.timeRangeButtonActive
              ]}
              onPress={() => setTimeRange(range.id)}
            >
              <Text style={[
                styles.timeRangeText,
                timeRange === range.id && styles.timeRangeTextActive
              ]}>
                {range.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Key Metrics */}
        <View style={styles.metricsGrid}>
          <MetricCard
            title="Total Earnings"
            value={formatCurrency(analytics?.totalEarnings || 0)}
            trend={12}
            icon={DollarSign}
            color="#10B981"
          />
          <MetricCard
            title="Total Bookings"
            value={analytics?.totalBookings || 0}
            trend={8}
            icon={Calendar}
            color="#3B82F6"
          />
          <MetricCard
            title="Average Rating"
            value={`${analytics?.averageRating?.toFixed(1) || 0}/5`}
            trend={5}
            icon={Star}
            color="#F59E0B"
          />
          <MetricCard
            title="Completion Rate"
            value={`${analytics?.completionRate?.toFixed(1) || 0}%`}
            trend={-2}
            icon={Award}
            color="#8B5CF6"
          />
        </View>

        {/* Earnings Chart */}
        <EarningsChart data={earnings} />

        {/* Performance Insights */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Performance Insights</Text>
          
          <View style={styles.insightsList}>
            <View style={styles.insightItem}>
              <Eye size={20} color="#3B82F6" />
              <View style={styles.insightContent}>
                <Text style={styles.insightLabel}>Profile Views</Text>
                <Text style={styles.insightValue}>1,234 this month</Text>
                <Text style={styles.insightTrend}>+15% from last month</Text>
              </View>
            </View>
            
            <View style={styles.insightItem}>
              <MessageCircle size={20} color="#10B981" />
              <View style={styles.insightContent}>
                <Text style={styles.insightLabel}>Response Rate</Text>
                <Text style={styles.insightValue}>{analytics?.responseRate || 95}%</Text>
                <Text style={styles.insightTrend}>Excellent response time</Text>
              </View>
            </View>
            
            <View style={styles.insightItem}>
              <Users size={20} color="#F59E0B" />
              <View style={styles.insightContent}>
                <Text style={styles.insightLabel}>Repeat Customers</Text>
                <Text style={styles.insightValue}>{analytics?.repeatCustomerRate?.toFixed(1) || 0}%</Text>
                <Text style={styles.insightTrend}>Customer loyalty score</Text>
              </View>
            </View>
            
            <View style={styles.insightItem}>
              <Clock size={20} color="#8B5CF6" />
              <View style={styles.insightContent}>
                <Text style={styles.insightLabel}>Avg Booking Duration</Text>
                <Text style={styles.insightValue}>2.5 hours</Text>
                <Text style={styles.insightTrend}>Standard duration</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Top Performing Days */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Top Performing Days</Text>
          <View style={styles.performingDays}>
            {['Friday', 'Saturday', 'Sunday'].map((day, index) => (
              <View key={day} style={styles.dayCard}>
                <Text style={styles.dayName}>{day}</Text>
                <Text style={styles.dayBookings}>{15 - index * 3} bookings</Text>
                <View style={styles.dayBar}>
                  <View 
                    style={[
                      styles.dayBarFill,
                      { width: `${100 - index * 20}%` }
                    ]} 
                  />
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* Recent Earnings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Earnings</Text>
          <View style={styles.earningsList}>
            {earnings.slice(0, 5).map((earning) => (
              <View key={earning.id} style={styles.earningItem}>
                <View style={styles.earningInfo}>
                  <Text style={styles.earningDate}>
                    {new Date(earning.createdAt).toLocaleDateString()}
                  </Text>
                  <Text style={styles.earningStatus}>
                    Status: {earning.status}
                  </Text>
                </View>
                <View style={styles.earningAmounts}>
                  <Text style={styles.earningGross}>
                    {formatCurrency(earning.grossAmount)}
                  </Text>
                  <Text style={styles.earningNet}>
                    Net: {formatCurrency(earning.netAmount)}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* Recommendations */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recommendations</Text>
          <View style={styles.recommendationsList}>
            <View style={styles.recommendation}>
              <TrendingUp size={16} color="#10B981" />
              <Text style={styles.recommendationText}>
                Your response rate is excellent! Keep up the quick replies.
              </Text>
            </View>
            <View style={styles.recommendation}>
              <Calendar size={16} color="#3B82F6" />
              <Text style={styles.recommendationText}>
                Consider adding more weekend availability for higher earnings.
              </Text>
            </View>
            <View style={styles.recommendation}>
              <Star size={16} color="#F59E0B" />
              <Text style={styles.recommendationText}>
                Upload more photos to increase profile views by up to 40%.
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F3E9',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    padding: 16,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1A2E4C',
  },
  timeRangeContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  timeRangeButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  timeRangeButtonActive: {
    backgroundColor: '#B76E79',
    borderColor: '#B76E79',
  },
  timeRangeText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  timeRangeTextActive: {
    color: '#FFFFFF',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -6,
    marginBottom: 24,
  },
  metricCard: {
    width: (width - 44) / 2,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    margin: 6,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  metricHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  trendText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 2,
  },
  metricValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1A2E4C',
    marginBottom: 4,
  },
  metricTitle: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  metricSubtitle: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 2,
  },
  chartContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 16,
  },
  chart: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    height: 80,
  },
  chartBar: {
    alignItems: 'center',
    flex: 1,
  },
  bar: {
    width: 20,
    borderRadius: 4,
    minHeight: 4,
    marginBottom: 8,
  },
  chartLabel: {
    fontSize: 12,
    color: '#6B7280',
  },
  section: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 16,
  },
  insightsList: {
    gap: 16,
  },
  insightItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  insightContent: {
    marginLeft: 12,
    flex: 1,
  },
  insightLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1A2E4C',
    marginBottom: 2,
  },
  insightValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 2,
  },
  insightTrend: {
    fontSize: 12,
    color: '#6B7280',
  },
  performingDays: {
    gap: 12,
  },
  dayCard: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 12,
  },
  dayName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 4,
  },
  dayBookings: {
    fontSize: 12,
    color: '#6B7280',
    marginBottom: 8,
  },
  dayBar: {
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
    overflow: 'hidden',
  },
  dayBarFill: {
    height: '100%',
    backgroundColor: '#B76E79',
    borderRadius: 2,
  },
  earningsList: {
    gap: 12,
  },
  earningItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  earningInfo: {
    flex: 1,
  },
  earningDate: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1A2E4C',
    marginBottom: 2,
  },
  earningStatus: {
    fontSize: 12,
    color: '#6B7280',
    textTransform: 'capitalize',
  },
  earningAmounts: {
    alignItems: 'flex-end',
  },
  earningGross: {
    fontSize: 14,
    color: '#6B7280',
    textDecorationLine: 'line-through',
  },
  earningNet: {
    fontSize: 16,
    fontWeight: '600',
    color: '#10B981',
  },
  recommendationsList: {
    gap: 12,
  },
  recommendation: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#F0FDF4',
    borderRadius: 8,
    padding: 12,
  },
  recommendationText: {
    fontSize: 14,
    color: '#166534',
    marginLeft: 8,
    flex: 1,
    lineHeight: 20,
  },
});