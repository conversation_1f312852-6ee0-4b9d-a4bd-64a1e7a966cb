import { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, ActivityIndicator, ScrollView, Switch } from 'react-native';
import { useSupabase } from '@/lib/supabase/supabase-provider';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { 
  MapPin, 
  Navigation, 
  Users, 
  Shield, 
  Settings, 
  Eye, 
  EyeOff,
  AlertTriangle,
  Heart,
  Clock,
  Zap
} from 'lucide-react-native';
import { LocationService, LocationData } from '@/lib/location/location-service';
import { Button } from '@/components/ui/Button';

interface NearbyCompanion {
  id: string;
  name: string;
  distance: number;
  rating: number;
  hourlyRate: number;
  isVerified: boolean;
  avatarUrl?: string;
}

interface SafetyZone {
  id: string;
  name: string;
  type: 'safe' | 'caution' | 'danger';
  distance: number;
  message: string;
}

export default function LocationScreen() {
  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(null);
  const [isLocationSharing, setIsLocationSharing] = useState(false);
  const [isTracking, setIsTracking] = useState(false);
  const [nearbyCompanions, setNearbyCompanions] = useState<NearbyCompanion[]>([]);
  const [safetyZones, setSafetyZones] = useState<SafetyZone[]>([]);
  const [loading, setLoading] = useState(false);
  const [permissionStatus, setPermissionStatus] = useState<string>('unknown');
  const [address, setAddress] = useState<string>('');
  const { supabase, user } = useSupabase();
  const locationService = LocationService.getInstance();
  const trackingIntervalRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    checkLocationPermissions();
    fetchLocationSettings();
    
    return () => {
      if (trackingIntervalRef.current) {
        clearInterval(trackingIntervalRef.current);
      }
      locationService.stopLocationTracking();
    };
  }, []);

  const checkLocationPermissions = async () => {
    try {
      const permissions = await locationService.requestPermissions();
      setPermissionStatus(permissions.granted ? 'granted' : 'denied');
      
      if (permissions.granted) {
        await getCurrentLocation();
      }
    } catch (error) {
      console.error('Error checking location permissions:', error);
    }
  };

  const fetchLocationSettings = async () => {
    try {
      const { data, error } = await supabase
        .from('user_locations')
        .select('*')
        .eq('user_id', user?.id)
        .eq('location_type', 'current')
        .order('created_at', { ascending: false })
        .limit(1)
        .maybeSingle();

      if (error && error.code !== 'PGRST116') throw error;
      
      if (data) {
        setCurrentLocation({
          latitude: parseFloat(data.latitude),
          longitude: parseFloat(data.longitude),
          accuracy: data.accuracy,
          timestamp: new Date(data.created_at).getTime(),
        });
        setIsLocationSharing(data.is_sharing_location);
        setAddress(data.address || '');
      }
    } catch (error) {
      console.error('Error fetching location settings:', error);
    }
  };

  const getCurrentLocation = async () => {
    try {
      setLoading(true);
      const location = await locationService.getCurrentLocation();
      
      if (location) {
        setCurrentLocation(location);
        
        // Reverse geocode to get address
        const addressResult = await locationService.reverseGeocode(
          location.latitude,
          location.longitude
        );
        if (addressResult) {
          setAddress(addressResult);
        }
        
        // Save to database
        await saveLocationToDatabase(location, addressResult);
        
        // Find nearby companions and safety zones
        await findNearbyCompanions(location);
        await checkSafetyZones(location);
      }
    } catch (error) {
      console.error('Error getting current location:', error);
      Alert.alert('Error', 'Failed to get current location');
    } finally {
      setLoading(false);
    }
  };

  const saveLocationToDatabase = async (location: LocationData, address?: string | null) => {
    try {
      const { error } = await supabase
        .from('user_locations')
        .upsert({
          user_id: user?.id,
          latitude: location.latitude,
          longitude: location.longitude,
          accuracy: location.accuracy,
          altitude: location.altitude,
          heading: location.heading,
          speed: location.speed,
          address: address || null,
          location_type: 'current',
          is_sharing_location: isLocationSharing,
          updated_at: new Date().toISOString(),
        });

      if (error) throw error;
    } catch (error) {
      console.error('Error saving location to database:', error);
    }
  };

  const findNearbyCompanions = async (location: LocationData) => {
    try {
      const { data, error } = await supabase.rpc('find_nearby_companions', {
        user_lat: location.latitude,
        user_lon: location.longitude,
        radius_km: 25,
      });

      if (error) throw error;

      const companions: NearbyCompanion[] = data?.map((item: any) => ({
        id: item.companion_id,
        name: item.profile_data.full_name,
        distance: parseFloat(item.distance_km),
        rating: item.profile_data.rating || 0,
        hourlyRate: item.profile_data.hourly_rate || 0,
        isVerified: item.profile_data.is_verified || false,
        avatarUrl: item.profile_data.avatar_url,
      })) || [];

      setNearbyCompanions(companions);
    } catch (error) {
      console.error('Error finding nearby companions:', error);
    }
  };

  const checkSafetyZones = async (location: LocationData) => {
    try {
      const { data, error } = await supabase.rpc('check_safety_zones', {
        user_id: user?.id,
        lat: location.latitude,
        lon: location.longitude,
      });

      if (error) throw error;

      const zones: SafetyZone[] = data?.map((zone: any) => ({
        id: zone.zone_id,
        name: zone.zone_name,
        type: zone.zone_type,
        distance: parseFloat(zone.distance_meters),
        message: zone.alert_message,
      })) || [];

      setSafetyZones(zones);
    } catch (error) {
      console.error('Error checking safety zones:', error);
    }
  };

  const toggleLocationSharing = async () => {
    try {
      const newSharingStatus = !isLocationSharing;
      setIsLocationSharing(newSharingStatus);

      if (currentLocation) {
        await saveLocationToDatabase(currentLocation, address);
      }

      Alert.alert(
        'Location Sharing',
        newSharingStatus 
          ? 'Your location is now being shared with matched companions'
          : 'Location sharing has been disabled'
      );
    } catch (error) {
      console.error('Error toggling location sharing:', error);
    }
  };

  const startLocationTracking = async () => {
    try {
      const success = await locationService.startLocationTracking(
        async (location) => {
          setCurrentLocation(location);
          
          // Update address
          const addressResult = await locationService.reverseGeocode(
            location.latitude,
            location.longitude
          );
          if (addressResult) {
            setAddress(addressResult);
          }
          
          // Save to database
          await saveLocationToDatabase(location, addressResult);
          
          // Update nearby companions periodically
          if (Math.random() < 0.1) { // 10% chance to update
            await findNearbyCompanions(location);
            await checkSafetyZones(location);
          }
        },
        {
          timeInterval: 30000, // 30 seconds
          distanceInterval: 50, // 50 meters
        }
      );

      if (success) {
        setIsTracking(true);
        Alert.alert('Location Tracking', 'Real-time location tracking started');
      } else {
        Alert.alert('Error', 'Failed to start location tracking');
      }
    } catch (error) {
      console.error('Error starting location tracking:', error);
    }
  };

  const stopLocationTracking = async () => {
    try {
      await locationService.stopLocationTracking();
      setIsTracking(false);
      Alert.alert('Location Tracking', 'Location tracking stopped');
    } catch (error) {
      console.error('Error stopping location tracking:', error);
    }
  };

  const getSafetyZoneIcon = (type: string) => {
    switch (type) {
      case 'safe':
        return <Shield size={16} color="#10B981" />;
      case 'caution':
        return <AlertTriangle size={16} color="#F59E0B" />;
      case 'danger':
        return <AlertTriangle size={16} color="#EF4444" />;
      default:
        return <MapPin size={16} color="#6B7280" />;
    }
  };

  const getSafetyZoneColor = (type: string) => {
    switch (type) {
      case 'safe':
        return '#D1FAE5';
      case 'caution':
        return '#FEF3C7';
      case 'danger':
        return '#FEE2E2';
      default:
        return '#F3F4F6';
    }
  };

  const CompanionCard = ({ companion }: { companion: NearbyCompanion }) => (
    <View style={styles.companionCard}>
      <View style={styles.companionInfo}>
        <Text style={styles.companionName}>{companion.name}</Text>
        <View style={styles.companionMeta}>
          <Text style={styles.companionDistance}>
            {companion.distance.toFixed(1)} km away
          </Text>
          <Text style={styles.companionRate}>
            ${companion.hourlyRate}/hr
          </Text>
        </View>
        <View style={styles.companionRating}>
          <Heart size={12} color="#FFB800" fill="#FFB800" />
          <Text style={styles.ratingText}>{companion.rating.toFixed(1)}</Text>
          {companion.isVerified && (
            <Shield size={12} color="#10B981" style={{ marginLeft: 8 }} />
          )}
        </View>
      </View>
      <TouchableOpacity style={styles.viewButton}>
        <Text style={styles.viewButtonText}>View</Text>
      </TouchableOpacity>
    </View>
  );

  const SafetyZoneCard = ({ zone }: { zone: SafetyZone }) => (
    <View style={[styles.safetyZoneCard, { backgroundColor: getSafetyZoneColor(zone.type) }]}>
      <View style={styles.safetyZoneHeader}>
        {getSafetyZoneIcon(zone.type)}
        <Text style={styles.safetyZoneName}>{zone.name}</Text>
      </View>
      <Text style={styles.safetyZoneMessage}>{zone.message}</Text>
      <Text style={styles.safetyZoneDistance}>
        {zone.distance.toFixed(0)}m away
      </Text>
    </View>
  );

  if (permissionStatus === 'denied') {
    return (
      <SafeAreaView style={styles.container} edges={['top']}>
        <StatusBar style="dark" />
        <View style={styles.permissionContainer}>
          <MapPin size={64} color="#B76E79" style={styles.permissionIcon} />
          <Text style={styles.permissionTitle}>Location Access Required</Text>
          <Text style={styles.permissionDescription}>
            To provide location-based features and safety services, we need access to your location.
          </Text>
          <Button title="Grant Location Access" onPress={checkLocationPermissions} />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style="dark" />
      
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Location & Safety</Text>
        <TouchableOpacity style={styles.settingsButton}>
          <Settings size={24} color="#1A2E4C" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Current Location Card */}
        <View style={styles.locationCard}>
          <View style={styles.locationHeader}>
            <MapPin size={24} color="#B76E79" />
            <Text style={styles.locationTitle}>Current Location</Text>
            <TouchableOpacity onPress={getCurrentLocation} disabled={loading}>
              {loading ? (
                <ActivityIndicator size="small" color="#B76E79" />
              ) : (
                <Navigation size={20} color="#B76E79" />
              )}
            </TouchableOpacity>
          </View>
          
          {currentLocation && (
            <View style={styles.locationDetails}>
              <Text style={styles.locationAddress}>{address || 'Address not available'}</Text>
              <Text style={styles.locationCoords}>
                {currentLocation.latitude.toFixed(6)}, {currentLocation.longitude.toFixed(6)}
              </Text>
              {currentLocation.accuracy && (
                <Text style={styles.locationAccuracy}>
                  Accuracy: ±{currentLocation.accuracy.toFixed(0)}m
                </Text>
              )}
            </View>
          )}
          
          <View style={styles.locationControls}>
            <View style={styles.controlRow}>
              <Text style={styles.controlLabel}>Share Location</Text>
              <Switch
                value={isLocationSharing}
                onValueChange={toggleLocationSharing}
                trackColor={{ false: '#D1D5DB', true: '#B76E79' }}
                thumbColor={isLocationSharing ? '#FFFFFF' : '#F3F4F6'}
              />
            </View>
            
            <View style={styles.controlRow}>
              <Text style={styles.controlLabel}>Real-time Tracking</Text>
              <Switch
                value={isTracking}
                onValueChange={isTracking ? stopLocationTracking : startLocationTracking}
                trackColor={{ false: '#D1D5DB', true: '#10B981' }}
                thumbColor={isTracking ? '#FFFFFF' : '#F3F4F6'}
              />
            </View>
          </View>
        </View>

        {/* Nearby Companions */}
        {nearbyCompanions.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Users size={20} color="#B76E79" />
              <Text style={styles.sectionTitle}>Nearby Companions</Text>
              <Text style={styles.sectionCount}>({nearbyCompanions.length})</Text>
            </View>
            
            {nearbyCompanions.slice(0, 3).map((companion) => (
              <CompanionCard key={companion.id} companion={companion} />
            ))}
            
            {nearbyCompanions.length > 3 && (
              <TouchableOpacity style={styles.viewAllButton}>
                <Text style={styles.viewAllText}>View All {nearbyCompanions.length} Companions</Text>
              </TouchableOpacity>
            )}
          </View>
        )}

        {/* Safety Zones */}
        {safetyZones.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Shield size={20} color="#10B981" />
              <Text style={styles.sectionTitle}>Safety Zones</Text>
            </View>
            
            {safetyZones.map((zone) => (
              <SafetyZoneCard key={zone.id} zone={zone} />
            ))}
          </View>
        )}

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          
          <View style={styles.quickActions}>
            <TouchableOpacity style={styles.actionButton}>
              <Eye size={24} color="#3B82F6" />
              <Text style={styles.actionText}>Share Live Location</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.actionButton}>
              <Clock size={24} color="#F59E0B" />
              <Text style={styles.actionText}>Location History</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.actionButton}>
              <Zap size={24} color="#EF4444" />
              <Text style={styles.actionText}>Emergency SOS</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Privacy Notice */}
        <View style={styles.privacyNotice}>
          <EyeOff size={16} color="#6B7280" />
          <Text style={styles.privacyText}>
            Your location data is encrypted and only shared with your consent. 
            You can disable location sharing at any time.
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F3E9',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1A2E4C',
  },
  settingsButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  permissionIcon: {
    marginBottom: 24,
  },
  permissionTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1A2E4C',
    marginBottom: 12,
    textAlign: 'center',
  },
  permissionDescription: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 32,
  },
  locationCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  locationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  locationTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
    flex: 1,
    marginLeft: 8,
  },
  locationDetails: {
    marginBottom: 16,
  },
  locationAddress: {
    fontSize: 16,
    color: '#1A2E4C',
    marginBottom: 4,
  },
  locationCoords: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 2,
  },
  locationAccuracy: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  locationControls: {
    gap: 12,
  },
  controlRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  controlLabel: {
    fontSize: 16,
    color: '#1A2E4C',
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
    marginLeft: 8,
  },
  sectionCount: {
    fontSize: 14,
    color: '#6B7280',
    marginLeft: 4,
  },
  companionCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  companionInfo: {
    flex: 1,
  },
  companionName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 4,
  },
  companionMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  companionDistance: {
    fontSize: 14,
    color: '#6B7280',
  },
  companionRate: {
    fontSize: 14,
    fontWeight: '500',
    color: '#B76E79',
  },
  companionRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 12,
    color: '#6B7280',
    marginLeft: 4,
  },
  viewButton: {
    backgroundColor: '#B76E79',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  viewButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  viewAllButton: {
    backgroundColor: '#F3F4F6',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  viewAllText: {
    color: '#B76E79',
    fontSize: 14,
    fontWeight: '500',
  },
  safetyZoneCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  safetyZoneHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  safetyZoneName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    marginLeft: 8,
  },
  safetyZoneMessage: {
    fontSize: 14,
    color: '#4B5563',
    marginBottom: 4,
  },
  safetyZoneDistance: {
    fontSize: 12,
    color: '#6B7280',
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  actionText: {
    fontSize: 12,
    color: '#1A2E4C',
    marginTop: 8,
    textAlign: 'center',
  },
  privacyNotice: {
    flexDirection: 'row',
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    padding: 12,
    marginTop: 16,
  },
  privacyText: {
    fontSize: 12,
    color: '#6B7280',
    marginLeft: 8,
    flex: 1,
    lineHeight: 16,
  },
});