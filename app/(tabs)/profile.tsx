import { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, Image, ActivityIndicator, Share } from 'react-native';
import { router, Link } from 'expo-router';
import { useSupabase } from '@/lib/supabase/supabase-provider';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { User, Settings, CreditCard, Shield, CircleHelp as HelpCircle, LogOut, CircleCheck as CheckCircle, Heart, Gift, BarChart3 } from 'lucide-react-native';
import { VerificationBadge } from '@/components/verification/VerificationBadge';
import { UserService } from '@/lib/user/user-service';
import { AnalyticsService } from '@/lib/analytics/analytics-service';
import * as ImagePicker from 'expo-image-picker';

export default function ProfileScreen() {
  const [profile, setProfile] = useState<any>(null);
  const [companion, setCompanion] = useState<any>(null);
  const [referralCode, setReferralCode] = useState<string>('');
  const [referralStats, setReferralStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [uploadingImage, setUploadingImage] = useState(false);
  const { supabase } = useSupabase();
  
  const userService = UserService.getInstance();
  const analyticsService = AnalyticsService.getInstance();

  useEffect(() => {
    userService.initialize(supabase);
    analyticsService.initialize(supabase, profile?.id);
    fetchProfile();
    if (profile?.id) {
      fetchReferralData();
    }
  }, []);

  const fetchProfile = async () => {
    try {
      setLoading(true);
      
      const { data: userSession } = await supabase.auth.getSession();
      const userId = userSession.session?.user?.id;
      
      if (!userId) {
        throw new Error('User not authenticated');
      }
      
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();
        
      if (error) {
        throw error;
      }
      
      setProfile(data);
      
      // Check if user is a companion
      if (data.is_companion) {
        const { data: companionData, error: companionError } = await supabase
          .from('companions')
          .select('*')
          .eq('profile_id', userId)
          .single();
          
        if (!companionError) {
          setCompanion(companionData);
        }
      }
      
    } catch (err) {
      console.error('Error fetching profile:', err);
    } finally {
      setLoading(false);
    }
  };
  
  const fetchReferralData = async () => {
    if (!profile?.id) return;
    
    try {
      // Get or create referral code
      const referrals = await userService.getUserReferrals(profile.id);
      if (referrals.length === 0) {
        const newCode = await userService.createReferral(profile.id);
        setReferralCode(newCode);
      } else {
        setReferralCode(referrals[0].referral_code);
      }
      
      // Get referral stats
      const stats = {
        totalReferrals: referrals.length,
        successfulReferrals: referrals.filter(r => r.status === 'completed').length,
        pendingReferrals: referrals.filter(r => r.status === 'pending').length,
        totalRewards: referrals.reduce((sum, r) => sum + (r.reward_claimed ? r.reward_amount : 0), 0),
      };
      setReferralStats(stats);
    } catch (error) {
      console.error('Error fetching referral data:', error);
    }
  };

  const handleSignOut = async () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            await supabase.auth.signOut();
            router.replace('/(auth)/sign-in');
          },
        },
      ]
    );
  };

  const handleImagePick = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });
      
      if (!result.canceled) {
        await uploadProfileImage(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image');
    }
  };

  const uploadProfileImage = async (uri: string) => {
    try {
      setUploadingImage(true);
      
      const { data: userSession } = await supabase.auth.getSession();
      const userId = userSession.session?.user?.id;
      
      if (!userId) {
        throw new Error('User not authenticated');
      }
      
      // Generate a unique filename
      const fileExt = uri.split('.').pop();
      const fileName = `${userId}-${Date.now()}.${fileExt}`;
      const filePath = `avatars/${fileName}`;
      
      // Convert image to blob
      const response = await fetch(uri);
      const blob = await response.blob();
      
      // Upload to Supabase Storage
      const { error: uploadError } = await supabase.storage
        .from('profiles')
        .upload(filePath, blob);
        
      if (uploadError) {
        throw uploadError;
      }
      
      // Get public URL
      const { data: publicUrl } = supabase.storage
        .from('profiles')
        .getPublicUrl(filePath);
        
      // Update profile
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ avatar_url: publicUrl.publicUrl })
        .eq('id', userId);
        
      if (updateError) {
        throw updateError;
      }
      
      // Refresh profile
      fetchProfile();
    } catch (error) {
      console.error('Error uploading image:', error);
      Alert.alert('Error', 'Failed to upload image');
    } finally {
      setUploadingImage(false);
    }
  };
  
  const shareReferralCode = async () => {
    try {
      const message = `Join HourlyGF with my referral code ${referralCode} and get $25 credit! Download the app: https://hourlygf.com/download`;
      
      await Share.share({
        message,
        title: 'Join HourlyGF',
      });
      
      analyticsService.trackAction('referral_shared', 'social', {
        referral_code: referralCode,
        method: 'native_share',
      });
    } catch (error) {
      console.error('Error sharing referral:', error);
    }
  };

  const ProfileItem = ({ icon, title, onPress, badge }: { 
    icon: React.ReactNode, 
    title: string, 
    onPress: () => void,
    badge?: React.ReactNode 
  }) => (
    <TouchableOpacity style={styles.profileItem} onPress={onPress}>
      {icon}
      <Text style={styles.profileItemText}>{title}</Text>
      {badge}
      <Text style={styles.chevron}>›</Text>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color="#B76E79" />
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style="dark" />
      
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Profile</Text>
        </View>
        
        <View style={styles.profileHeader}>
          <TouchableOpacity
            style={styles.avatarContainer}
            onPress={handleImagePick}
            disabled={uploadingImage}
          >
            {uploadingImage ? (
              <View style={styles.avatarLoading}>
                <ActivityIndicator color="#FFFFFF" />
              </View>
            ) : profile?.avatar_url ? (
              <Image source={{ uri: profile.avatar_url }} style={styles.avatar} />
            ) : (
              <View style={styles.avatarPlaceholder}>
                <User size={30} color="#FFFFFF" />
              </View>
            )}
            <View style={styles.editBadge}>
              <Text style={styles.editBadgeText}>Edit</Text>
            </View>
          </TouchableOpacity>
          
          <Text style={styles.userName}>{profile?.full_name || 'User'}</Text>
          <Text style={styles.userEmail}>{profile?.email || ''}</Text>
          
          {/* Verification Badges */}
          {companion && (
            <View style={styles.verificationContainer}>
              {companion.verification_status === 'verified' && (
                <VerificationBadge type="verified\" size="small" />
              )}
              {companion.is_verified && (
                <VerificationBadge type="background_checked\" size="small" />
              )}
            </View>
          )}
          
          <TouchableOpacity style={styles.editProfileButton}>
            <Text style={styles.editProfileText}>Edit Profile</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Account</Text>
          
          <ProfileItem
            icon={<User size={20} color="#1A2E4C" />}
            title="Personal Information"
            onPress={() => {}}
          />
          <ProfileItem
            icon={<Heart size={20} color="#1A2E4C" />}
            title="Favorites"
            onPress={() => router.push('/profile/favorites')}
          />
          <ProfileItem
            icon={<Settings size={20} color="#1A2E4C" />}
            title="Preferences"
            onPress={() => router.push('/profile/preferences')}
          />
          <ProfileItem
            icon={<CreditCard size={20} color="#1A2E4C" />}
            title="Payment Methods"
            onPress={() => {}}
          />
        </View>
        
        {/* Referral Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Referral Program</Text>
          
          <View style={styles.referralCard}>
            <View style={styles.referralHeader}>
              <Gift size={24} color="#10B981" />
              <Text style={styles.referralTitle}>Invite Friends & Earn</Text>
            </View>
            <Text style={styles.referralDescription}>
              Share your referral code and earn $25 for each friend who joins!
            </Text>
            
            <View style={styles.referralCodeContainer}>
              <Text style={styles.referralCodeLabel}>Your referral code:</Text>
              <View style={styles.referralCodeBox}>
                <Text style={styles.referralCode}>{referralCode}</Text>
                <TouchableOpacity style={styles.shareButton} onPress={shareReferralCode}>
                  <Text style={styles.shareButtonText}>Share</Text>
                </TouchableOpacity>
              </View>
            </View>
            
            {referralStats && (
              <View style={styles.referralStats}>
                <View style={styles.referralStat}>
                  <Text style={styles.referralStatValue}>{referralStats.successfulReferrals}</Text>
                  <Text style={styles.referralStatLabel}>Successful</Text>
                </View>
                <View style={styles.referralStat}>
                  <Text style={styles.referralStatValue}>{referralStats.pendingReferrals}</Text>
                  <Text style={styles.referralStatLabel}>Pending</Text>
                </View>
                <View style={styles.referralStat}>
                  <Text style={styles.referralStatValue}>${referralStats.totalRewards}</Text>
                  <Text style={styles.referralStatLabel}>Earned</Text>
                </View>
              </View>
            )}
          </View>
        </View>
        
        {/* Companion-specific section */}
        {companion && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Companion</Text>
            
            <ProfileItem
              icon={<Shield size={20} color="#1A2E4C" />}
              title="Verification"
              onPress={() => router.push('/(companion)/verification')}
              badge={
                companion.verification_status === 'verified' ? (
                  <CheckCircle size={16} color="#10B981" />
                ) : companion.verification_status === 'pending' ? (
                  <View style={styles.pendingBadge}>
                    <Text style={styles.pendingText}>Pending</Text>
                  </View>
                ) : (
                  <View style={styles.unverifiedBadge}>
                    <Text style={styles.unverifiedText}>Not Verified</Text>
                  </View>
                )
              }
            />
            <ProfileItem
              icon={<BarChart3 size={20} color="#1A2E4C" />}
              title="Analytics"
              onPress={() => router.push('/(companion)/analytics')}
            />
          </View>
        )}
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Support</Text>
          
          <ProfileItem
            icon={<Shield size={20} color="#1A2E4C" />}
            title="Privacy & Safety"
            onPress={() => {}}
          />
          <ProfileItem
            icon={<HelpCircle size={20} color="#1A2E4C" />}
            title="Help & Support"
            onPress={() => {}}
          />
        </View>
        
        <TouchableOpacity style={styles.signOutButton} onPress={handleSignOut}>
          <LogOut size={20} color="#B91C1C" />
          <Text style={styles.signOutText}>Sign Out</Text>
        </TouchableOpacity>
        
        <View style={styles.footer}>
          <Text style={styles.footerText}>HourlyGF v1.0.0</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F3E9',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    padding: 16,
    paddingBottom: 8,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1A2E4C',
  },
  profileHeader: {
    alignItems: 'center',
    paddingVertical: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  avatarContainer: {
    position: 'relative',
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: 16,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  avatarPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#B76E79',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarLoading: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(183, 110, 121, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  editBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: '#1A2E4C',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  editBadgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
  },
  userName: {
    fontSize: 22,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 16,
  },
  verificationContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    gap: 8,
  },
  editProfileButton: {
    borderWidth: 1,
    borderColor: '#B76E79',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  editProfileText: {
    color: '#B76E79',
    fontWeight: '500',
  },
  section: {
    paddingHorizontal: 16,
    paddingTop: 24,
    paddingBottom: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 16,
  },
  profileItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  profileItemText: {
    fontSize: 16,
    color: '#1A2E4C',
    marginLeft: 12,
    flex: 1,
  },
  pendingBadge: {
    backgroundColor: '#FEF3C7',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    marginRight: 8,
  },
  pendingText: {
    fontSize: 12,
    color: '#92400E',
    fontWeight: '500',
  },
  unverifiedBadge: {
    backgroundColor: '#FEE2E2',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    marginRight: 8,
  },
  unverifiedText: {
    fontSize: 12,
    color: '#991B1B',
    fontWeight: '500',
  },
  chevron: {
    fontSize: 18,
    color: '#6B7280',
  },
  signOutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    marginTop: 24,
  },
  signOutText: {
    fontSize: 16,
    color: '#B91C1C',
    marginLeft: 12,
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 24,
  },
  footerText: {
    fontSize: 14,
    color: '#9CA3AF',
  },
  referralCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  referralHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  referralTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
    marginLeft: 8,
  },
  referralDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 16,
  },
  referralCodeContainer: {
    marginBottom: 16,
  },
  referralCodeLabel: {
    fontSize: 14,
    color: '#4B5563',
    marginBottom: 8,
  },
  referralCodeBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    padding: 12,
  },
  referralCode: {
    flex: 1,
    fontSize: 18,
    fontWeight: '700',
    color: '#1A2E4C',
    letterSpacing: 2,
  },
  shareButton: {
    backgroundColor: '#10B981',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  shareButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  referralStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 12,
  },
  referralStat: {
    alignItems: 'center',
  },
  referralStatValue: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1A2E4C',
  },
  referralStatLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 2,
  },
});