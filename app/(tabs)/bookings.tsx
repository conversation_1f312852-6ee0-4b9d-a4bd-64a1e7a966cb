import { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useSupabase } from '@/lib/supabase/supabase-provider';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { format } from 'date-fns';
import { Clock, Calendar, MapPin, ChevronRight, Repeat, Users, Bell, Plus } from 'lucide-react-native';
import { BookingCard } from '@/components/bookings/BookingCard';
import { GroupBookingCard } from '@/components/booking/GroupBookingCard';
import { BookingService } from '@/lib/booking/booking-service';

export default function BookingsScreen() {
  const [bookings, setBookings] = useState<any[]>([]);
  const [recurringBookings, setRecurringBookings] = useState<any[]>([]);
  const [groupBookings, setGroupBookings] = useState<any[]>([]);
  const [waitlistEntries, setWaitlistEntries] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('all');
  const { supabase } = useSupabase();
  
  const bookingService = BookingService.getInstance();

  useEffect(() => {
    bookingService.initialize(supabase);
    fetchBookings();
    fetchRecurringBookings();
    fetchGroupBookings();
    fetchWaitlistEntries();
  }, [activeTab]);

  const fetchBookings = async () => {
    try {
      setLoading(true);
      
      const now = new Date().toISOString();
      const { data: userSession } = await supabase.auth.getSession();
      const userId = userSession.session?.user?.id;
      
      if (!userId) {
        throw new Error('User not authenticated');
      }
      
      let query = supabase
        .from('bookings')
        .select(`
          *,
          companions(
            *,
            profiles(*)
          )
        `)
        .eq('user_id', userId);
        
      if (activeTab === 'upcoming') {
        query = query.gte('booking_time', now);
      } else if (activeTab === 'past') {
        query = query.lt('booking_time', now);
      }
      
      query = query.order('booking_time', { ascending: activeTab === 'upcoming' });
      
      const { data, error } = await query;
        
      if (error) {
        throw error;
      }
      
      setBookings(data || []);
    } catch (err: any) {
      console.error('Error fetching bookings:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  
  const fetchRecurringBookings = async () => {
    try {
      const { data: userSession } = await supabase.auth.getSession();
      const userId = userSession.session?.user?.id;
      
      if (!userId) return;
      
      const { data, error } = await supabase
        .from('recurring_bookings')
        .select(`
          *,
          companions(
            *,
            profiles(*)
          )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });
        
      if (error) throw error;
      setRecurringBookings(data || []);
    } catch (err: any) {
      console.error('Error fetching recurring bookings:', err);
    }
  };
  
  const fetchGroupBookings = async () => {
    try {
      const { data: userSession } = await supabase.auth.getSession();
      const userId = userSession.session?.user?.id;
      
      if (!userId) return;
      
      const groupBookings = await bookingService.getGroupBookings(userId);
      setGroupBookings(groupBookings);
    } catch (err: any) {
      console.error('Error fetching group bookings:', err);
    }
  };
  
  const fetchWaitlistEntries = async () => {
    try {
      const { data: userSession } = await supabase.auth.getSession();
      const userId = userSession.session?.user?.id;
      
      if (!userId) return;
      
      const { data, error } = await supabase
        .from('booking_waitlist')
        .select(`
          *,
          companions(
            *,
            profiles(*)
          )
        `)
        .eq('user_id', userId)
        .eq('status', 'active')
        .order('created_at', { ascending: false });
        
      if (error) throw error;
      setWaitlistEntries(data || []);
    } catch (err: any) {
      console.error('Error fetching waitlist entries:', err);
    }
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  const renderTabs = () => (
    <View style={styles.tabContainer}>
      <TouchableOpacity
        style={[styles.tab, activeTab === 'all' && styles.activeTab]}
        onPress={() => handleTabChange('all')}
      >
        <Text
          style={[
            styles.tabText,
            activeTab === 'all' && styles.activeTabText,
          ]}
        >
          All
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.tab, activeTab === 'upcoming' && styles.activeTab]}
        onPress={() => handleTabChange('upcoming')}
      >
        <Text
          style={[
            styles.tabText,
            activeTab === 'upcoming' && styles.activeTabText,
          ]}
        >
          Upcoming
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.tab, activeTab === 'past' && styles.activeTab]}
        onPress={() => handleTabChange('past')}
      >
        <Text
          style={[
            styles.tabText,
            activeTab === 'past' && styles.activeTabText,
          ]}
        >
          Past
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.tab, activeTab === 'recurring' && styles.activeTab]}
        onPress={() => handleTabChange('recurring')}
      >
        <Text
          style={[
            styles.tabText,
            activeTab === 'recurring' && styles.activeTabText,
          ]}
        >
          Recurring
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.tab, activeTab === 'groups' && styles.activeTab]}
        onPress={() => handleTabChange('groups')}
      >
        <Text
          style={[
            styles.tabText,
            activeTab === 'groups' && styles.activeTabText,
          ]}
        >
          Groups
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.tab, activeTab === 'waitlist' && styles.activeTab]}
        onPress={() => handleTabChange('waitlist')}
      >
        <Text
          style={[
            styles.tabText,
            activeTab === 'waitlist' && styles.activeTabText,
          ]}
        >
          Waitlist
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Calendar size={64} color="#B76E79" style={styles.emptyIcon} />
      <Text style={styles.emptyTitle}>
        No {activeTab} bookings
      </Text>
      <Text style={styles.emptySubtitle}>
        {activeTab === 'upcoming' || activeTab === 'all'
          ? "You don't have any upcoming bookings yet. Discover companions and book your date."
          : "You haven't had any dates yet. Start by booking a companion."}
      </Text>
      <TouchableOpacity style={styles.discoverButton}>
        <Text style={styles.discoverButtonText}>Discover Companions</Text>
      </TouchableOpacity>
    </View>
  );

  const renderBookingList = () => {
    switch (activeTab) {
      case 'recurring':
        return (
          <FlatList
            data={recurringBookings}
            keyExtractor={(item) => item.id.toString()}
            renderItem={({ item }) => (
              <View style={styles.recurringBookingCard}>
                <View style={styles.recurringHeader}>
                  <Repeat size={20} color="#B76E79" />
                  <Text style={styles.recurringTitle}>
                    {item.pattern_type} with {item.companions.profiles.full_name}
                  </Text>
                </View>
                <Text style={styles.recurringDetails}>
                  Every {item.frequency} {item.pattern_type} • {item.duration_hours} hours
                </Text>
                <Text style={styles.recurringStatus}>
                  Status: {item.status} • {item.current_occurrences} bookings created
                </Text>
              </View>
            )}
            contentContainerStyle={styles.listContent}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={renderEmptyState}
          />
        );
      
      case 'groups':
        return (
          <FlatList
            data={groupBookings}
            keyExtractor={(item) => item.id.toString()}
            renderItem={({ item }) => (
              <GroupBookingCard
                groupBooking={item}
                onPress={() => {}}
                userParticipating={true}
              />
            )}
            contentContainerStyle={styles.listContent}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={renderEmptyState}
          />
        );
      
      default:
        return renderStandardBookings();
    }
  };

  const renderStandardBookings = () => (
    <FlatList
      data={bookings}
      keyExtractor={(item) => item.id.toString()}
      renderItem={({ item }) => (
        <BookingCard booking={item} isUpcoming={activeTab === 'upcoming' || activeTab === 'all'} />
      )}
      contentContainerStyle={styles.listContent}
      showsVerticalScrollIndicator={false}
      ListEmptyComponent={renderEmptyState}
    />
  );

  if (error) {
    return (
      <View style={styles.centered}>
        <Text style={styles.errorText}>Failed to load bookings</Text>
        <TouchableOpacity style={styles.retryButton} onPress={fetchBookings}>
          <Text style={styles.retryText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style="dark" />
      
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Your Bookings</Text>
      </View>
      
      {renderTabs()}
      
      {loading ? (
        <View style={styles.centered}>
          <ActivityIndicator size="large" color="#B76E79" />
        </View>
      ) : (
        renderBookingList()
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F3E9',
  },
  header: {
    padding: 16,
    paddingBottom: 8,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1A2E4C',
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    marginBottom: 16,
    paddingHorizontal: 16,
    overflow: 'scroll',
  },
  tab: {
    paddingVertical: 12,
    marginRight: 24,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: '#B76E79',
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#6B7280',
  },
  activeTabText: {
    color: '#B76E79',
  },
  listContent: {
    paddingHorizontal: 16,
    paddingBottom: 24,
    flexGrow: 1,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#B91C1C',
    marginBottom: 12,
  },
  retryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#B76E79',
    borderRadius: 8,
  },
  retryText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    marginTop: 48,
  },
  emptyIcon: {
    marginBottom: 16,
    opacity: 0.8,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 20,
  },
  discoverButton: {
    backgroundColor: '#B76E79',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  discoverButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 16,
  },
  recurringBookingCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    marginHorizontal: 16,
  },
  recurringHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  recurringTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    marginLeft: 8,
  },
  recurringDetails: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
  },
  recurringStatus: {
    fontSize: 12,
    color: '#9CA3AF',
  },
});