import { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, Alert } from 'react-native';
import { useSupabase } from '@/lib/supabase/supabase-provider';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { MessageCircle, Phone, FileText, Video, CircleHelp as HelpCircle, Mail, Clock, CircleCheck as CheckCircle } from 'lucide-react-native';
import { Button } from '@/components/ui/Button';
import { router } from 'expo-router';

export default function SupportScreen() {
  const [tickets, setTickets] = useState<any[]>([]);
  const [helpArticles, setHelpArticles] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [chatSession, setChatSession] = useState<any>(null);
  const { supabase, user } = useSupabase();

  useEffect(() => {
    fetchSupportData();
    checkActiveChatSession();
  }, []);

  const fetchSupportData = async () => {
    try {
      setLoading(true);
      
      // Fetch user's support tickets
      const { data: ticketsData, error: ticketsError } = await supabase
        .from('support_tickets')
        .select('*')
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false })
        .limit(5);
        
      if (ticketsError) throw ticketsError;
      setTickets(ticketsData || []);
      
      // Fetch popular help articles
      const { data: articlesData, error: articlesError } = await supabase
        .from('help_articles')
        .select('*')
        .eq('is_published', true)
        .order('view_count', { ascending: false })
        .limit(6);
        
      if (articlesError) throw articlesError;
      setHelpArticles(articlesData || []);
      
    } catch (error) {
      console.error('Error fetching support data:', error);
    } finally {
      setLoading(false);
    }
  };

  const checkActiveChatSession = async () => {
    try {
      const { data, error } = await supabase
        .from('live_chat_sessions')
        .select('*')
        .eq('user_id', user?.id)
        .in('status', ['waiting', 'active'])
        .maybeSingle();
        
      if (error && error.code !== 'PGRST116') throw error;
      setChatSession(data);
    } catch (error) {
      console.error('Error checking chat session:', error);
    }
  };

  const startLiveChat = async () => {
    try {
      const { data, error } = await supabase
        .from('live_chat_sessions')
        .insert([
          {
            user_id: user?.id,
            status: 'waiting',
            queue_position: 1,
          }
        ])
        .select()
        .single();
        
      if (error) throw error;
      
      setChatSession(data);
      router.push(`/support/chat/${data.id}`);
    } catch (error) {
      console.error('Error starting live chat:', error);
      Alert.alert('Error', 'Failed to start live chat session');
    }
  };

  const createSupportTicket = () => {
    router.push('/support/create-ticket');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
        return '#3B82F6';
      case 'in_progress':
        return '#F59E0B';
      case 'resolved':
        return '#10B981';
      case 'closed':
        return '#6B7280';
      default:
        return '#6B7280';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'open':
        return <Clock size={16} color="#3B82F6" />;
      case 'in_progress':
        return <Clock size={16} color="#F59E0B" />;
      case 'resolved':
        return <CheckCircle size={16} color="#10B981" />;
      case 'closed':
        return <CheckCircle size={16} color="#6B7280" />;
      default:
        return <Clock size={16} color="#6B7280" />;
    }
  };

  if (loading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color="#B76E79" />
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style="dark" />
      
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Support Center</Text>
          <Text style={styles.headerSubtitle}>
            We're here to help you 24/7
          </Text>
        </View>
        
        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Get Help Now</Text>
          
          <View style={styles.quickActions}>
            <TouchableOpacity 
              style={styles.actionCard}
              onPress={chatSession ? () => router.push(`/support/chat/${chatSession.id}`) : startLiveChat}
            >
              <MessageCircle size={24} color="#10B981" />
              <Text style={styles.actionTitle}>Live Chat</Text>
              <Text style={styles.actionSubtitle}>
                {chatSession ? 'Continue chat' : 'Chat with support'}
              </Text>
              {chatSession && (
                <View style={styles.activeBadge}>
                  <Text style={styles.activeBadgeText}>Active</Text>
                </View>
              )}
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.actionCard}
              onPress={() => router.push('/support/video-call')}
            >
              <Video size={24} color="#3B82F6" />
              <Text style={styles.actionTitle}>Video Call</Text>
              <Text style={styles.actionSubtitle}>
                Schedule a video call
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.actionCard}
              onPress={createSupportTicket}
            >
              <Mail size={24} color="#F59E0B" />
              <Text style={styles.actionTitle}>Email Support</Text>
              <Text style={styles.actionSubtitle}>
                Create a support ticket
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.actionCard}
              onPress={() => router.push('/support/emergency')}
            >
              <Phone size={24} color="#EF4444" />
              <Text style={styles.actionTitle}>Emergency</Text>
              <Text style={styles.actionSubtitle}>
                24/7 emergency line
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        
        {/* Recent Tickets */}
        {tickets.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Recent Tickets</Text>
              <TouchableOpacity onPress={() => router.push('/support/tickets')}>
                <Text style={styles.seeAllText}>View All</Text>
              </TouchableOpacity>
            </View>
            
            {tickets.map((ticket) => (
              <TouchableOpacity 
                key={ticket.id}
                style={styles.ticketCard}
                onPress={() => router.push(`/support/ticket/${ticket.id}`)}
              >
                <View style={styles.ticketHeader}>
                  <Text style={styles.ticketTitle} numberOfLines={1}>
                    {ticket.title}
                  </Text>
                  <View style={styles.ticketStatus}>
                    {getStatusIcon(ticket.status)}
                    <Text style={[styles.ticketStatusText, { color: getStatusColor(ticket.status) }]}>
                      {ticket.status.replace('_', ' ')}
                    </Text>
                  </View>
                </View>
                <Text style={styles.ticketDescription} numberOfLines={2}>
                  {ticket.description}
                </Text>
                <Text style={styles.ticketDate}>
                  {new Date(ticket.created_at).toLocaleDateString()}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        )}
        
        {/* Help Articles */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Popular Help Articles</Text>
            <TouchableOpacity onPress={() => router.push('/support/help')}>
              <Text style={styles.seeAllText}>Browse All</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.articlesGrid}>
            {helpArticles.map((article) => (
              <TouchableOpacity 
                key={article.id}
                style={styles.articleCard}
                onPress={() => router.push(`/support/article/${article.id}`)}
              >
                <FileText size={20} color="#B76E79" />
                <Text style={styles.articleTitle} numberOfLines={2}>
                  {article.title}
                </Text>
                <Text style={styles.articleViews}>
                  {article.view_count} views
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
        
        {/* Contact Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Other Ways to Reach Us</Text>
          
          <View style={styles.contactCard}>
            <View style={styles.contactItem}>
              <Mail size={20} color="#6B7280" />
              <View style={styles.contactInfo}>
                <Text style={styles.contactLabel}>Email</Text>
                <Text style={styles.contactValue}><EMAIL></Text>
              </View>
            </View>
            
            <View style={styles.contactItem}>
              <Phone size={20} color="#6B7280" />
              <View style={styles.contactInfo}>
                <Text style={styles.contactLabel}>Phone</Text>
                <Text style={styles.contactValue}>1-800-HOURLY-GF</Text>
              </View>
            </View>
            
            <View style={styles.contactItem}>
              <Clock size={20} color="#6B7280" />
              <View style={styles.contactInfo}>
                <Text style={styles.contactLabel}>Hours</Text>
                <Text style={styles.contactValue}>24/7 Support Available</Text>
              </View>
            </View>
          </View>
        </View>
        
        {/* FAQ Quick Links */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Answers</Text>
          
          <View style={styles.faqList}>
            <TouchableOpacity style={styles.faqItem}>
              <HelpCircle size={16} color="#B76E79" />
              <Text style={styles.faqText}>How do I book a companion?</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.faqItem}>
              <HelpCircle size={16} color="#B76E79" />
              <Text style={styles.faqText}>What payment methods are accepted?</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.faqItem}>
              <HelpCircle size={16} color="#B76E79" />
              <Text style={styles.faqText}>How do I cancel a booking?</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.faqItem}>
              <HelpCircle size={16} color="#B76E79" />
              <Text style={styles.faqText}>Is my personal information safe?</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F3E9',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    padding: 24,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1A2E4C',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  section: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1A2E4C',
  },
  seeAllText: {
    fontSize: 14,
    color: '#B76E79',
    fontWeight: '500',
  },
  quickActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -8,
  },
  actionCard: {
    width: '48%',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    margin: 8,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    position: 'relative',
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    marginTop: 8,
    marginBottom: 4,
  },
  actionSubtitle: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'center',
  },
  activeBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: '#10B981',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  activeBadgeText: {
    fontSize: 10,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  ticketCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  ticketHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  ticketTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    flex: 1,
    marginRight: 12,
  },
  ticketStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ticketStatusText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
    textTransform: 'capitalize',
  },
  ticketDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
    marginBottom: 8,
  },
  ticketDate: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  articlesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -6,
  },
  articleCard: {
    width: '48%',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    margin: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  articleTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1A2E4C',
    marginTop: 8,
    marginBottom: 4,
    lineHeight: 18,
  },
  articleViews: {
    fontSize: 12,
    color: '#6B7280',
  },
  contactCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  contactInfo: {
    marginLeft: 12,
    flex: 1,
  },
  contactLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1A2E4C',
    marginBottom: 2,
  },
  contactValue: {
    fontSize: 14,
    color: '#6B7280',
  },
  faqList: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  faqItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  faqText: {
    fontSize: 14,
    color: '#1A2E4C',
    marginLeft: 12,
    flex: 1,
  },
});