import { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, Alert, Platform } from 'react-native';
import { useSupabase } from '@/lib/supabase/supabase-provider';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { Crown, Check, Star, Zap } from 'lucide-react-native';
import { Button } from '@/components/ui/Button';
import { RevenueCatService } from '@/lib/payments/revenuecat';
import { StripeService } from '@/lib/payments/stripe';

interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price_monthly: number;
  price_yearly: number;
  features: string[];
  revenuecat_product_id: string;
  stripe_price_id: string;
}

export default function SubscriptionScreen() {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [currentSubscription, setCurrentSubscription] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [purchasing, setPurchasing] = useState<string | null>(null);
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'yearly'>('monthly');
  const { supabase, user } = useSupabase();

  useEffect(() => {
    fetchSubscriptionData();
    if (Platform.OS !== 'web') {
      initializeRevenueCat();
    }
  }, []);

  const fetchSubscriptionData = async () => {
    try {
      setLoading(true);
      
      // Fetch available plans
      const { data: plansData, error: plansError } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .order('price_monthly', { ascending: true });
        
      if (plansError) {
        throw plansError;
      }
      
      setPlans(plansData || []);
      
      // Fetch current subscription
      if (user) {
        const { data: subscriptionData, error: subscriptionError } = await supabase
          .from('user_subscriptions')
          .select(`
            *,
            subscription_plans(*)
          `)
          .eq('user_id', user.id)
          .eq('status', 'active')
          .maybeSingle();
          
        if (subscriptionError && subscriptionError.code !== 'PGRST116') {
          throw subscriptionError;
        }
        
        setCurrentSubscription(subscriptionData);
      }
    } catch (error) {
      console.error('Error fetching subscription data:', error);
      Alert.alert('Error', 'Failed to load subscription information');
    } finally {
      setLoading(false);
    }
  };

  const initializeRevenueCat = async () => {
    try {
      if (user) {
        const revenueCat = RevenueCatService.getInstance();
        await revenueCat.configure(user.id);
      }
    } catch (error) {
      console.error('Error initializing RevenueCat:', error);
    }
  };

  const handleSubscribe = async (plan: SubscriptionPlan) => {
    try {
      setPurchasing(plan.id);
      
      if (Platform.OS === 'web') {
        await handleWebSubscription(plan);
      } else {
        await handleMobileSubscription(plan);
      }
    } catch (error: any) {
      console.error('Error subscribing:', error);
      Alert.alert('Error', error.message || 'Failed to process subscription');
    } finally {
      setPurchasing(null);
    }
  };

  const handleWebSubscription = async (plan: SubscriptionPlan) => {
    const stripe = StripeService.getInstance();
    
    // Create or get Stripe customer
    const response = await fetch('/api/payments/create-customer', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: user?.email,
        name: user?.user_metadata?.full_name,
      }),
    });
    
    const { customer_id } = await response.json();
    
    // Create subscription
    const priceId = billingPeriod === 'yearly' 
      ? plan.stripe_price_id.replace('monthly', 'yearly')
      : plan.stripe_price_id;
      
    const subscription = await stripe.createSubscription(customer_id, priceId);
    
    if (subscription.client_secret) {
      // Redirect to Stripe Checkout or handle payment confirmation
      Alert.alert('Success', 'Subscription created successfully!');
      fetchSubscriptionData();
    }
  };

  const handleMobileSubscription = async (plan: SubscriptionPlan) => {
    const revenueCat = RevenueCatService.getInstance();
    
    const productId = billingPeriod === 'yearly'
      ? plan.revenuecat_product_id.replace('monthly', 'yearly')
      : plan.revenuecat_product_id;
      
    const result = await revenueCat.purchaseProduct(productId);
    
    if (result.success) {
      Alert.alert('Success', 'Subscription activated successfully!');
      fetchSubscriptionData();
    }
  };

  const handleCancelSubscription = async () => {
    Alert.alert(
      'Cancel Subscription',
      'Are you sure you want to cancel your subscription? You will continue to have access until the end of your current billing period.',
      [
        { text: 'Keep Subscription', style: 'cancel' },
        {
          text: 'Cancel',
          style: 'destructive',
          onPress: async () => {
            try {
              if (currentSubscription?.stripe_subscription_id) {
                const stripe = StripeService.getInstance();
                await stripe.cancelSubscription(currentSubscription.stripe_subscription_id);
              }
              
              Alert.alert('Success', 'Subscription canceled successfully');
              fetchSubscriptionData();
            } catch (error) {
              console.error('Error canceling subscription:', error);
              Alert.alert('Error', 'Failed to cancel subscription');
            }
          },
        },
      ]
    );
  };

  const getPlanIcon = (planName: string) => {
    switch (planName.toLowerCase()) {
      case 'basic':
        return <Star size={24} color="#3B82F6" />;
      case 'premium':
        return <Zap size={24} color="#8B5CF6" />;
      case 'vip':
        return <Crown size={24} color="#F59E0B" />;
      default:
        return <Star size={24} color="#6B7280" />;
    }
  };

  const getPlanColor = (planName: string) => {
    switch (planName.toLowerCase()) {
      case 'basic':
        return '#3B82F6';
      case 'premium':
        return '#8B5CF6';
      case 'vip':
        return '#F59E0B';
      default:
        return '#6B7280';
    }
  };

  if (loading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color="#B76E79" />
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style="dark" />
      
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Choose Your Plan</Text>
          <Text style={styles.headerSubtitle}>
            Unlock premium features and enhance your experience
          </Text>
        </View>
        
        {currentSubscription && (
          <View style={styles.currentSubscriptionCard}>
            <Text style={styles.currentSubscriptionTitle}>Current Plan</Text>
            <View style={styles.currentSubscriptionInfo}>
              {getPlanIcon(currentSubscription.subscription_plans.name)}
              <View style={styles.currentSubscriptionDetails}>
                <Text style={styles.currentSubscriptionName}>
                  {currentSubscription.subscription_plans.name}
                </Text>
                <Text style={styles.currentSubscriptionStatus}>
                  Active until {new Date(currentSubscription.current_period_end).toLocaleDateString()}
                </Text>
              </View>
            </View>
            <Button
              title="Cancel Subscription"
              variant="outline"
              size="small"
              onPress={handleCancelSubscription}
            />
          </View>
        )}
        
        <View style={styles.billingToggle}>
          <TouchableOpacity
            style={[
              styles.billingOption,
              billingPeriod === 'monthly' && styles.billingOptionActive,
            ]}
            onPress={() => setBillingPeriod('monthly')}
          >
            <Text
              style={[
                styles.billingOptionText,
                billingPeriod === 'monthly' && styles.billingOptionTextActive,
              ]}
            >
              Monthly
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.billingOption,
              billingPeriod === 'yearly' && styles.billingOptionActive,
            ]}
            onPress={() => setBillingPeriod('yearly')}
          >
            <Text
              style={[
                styles.billingOptionText,
                billingPeriod === 'yearly' && styles.billingOptionTextActive,
              ]}
            >
              Yearly
            </Text>
            <View style={styles.saveBadge}>
              <Text style={styles.saveText}>Save 17%</Text>
            </View>
          </TouchableOpacity>
        </View>
        
        <View style={styles.plansContainer}>
          {plans.map((plan) => {
            const isCurrentPlan = currentSubscription?.plan_id === plan.id;
            const planColor = getPlanColor(plan.name);
            const price = billingPeriod === 'yearly' ? plan.price_yearly : plan.price_monthly;
            const isPurchasing = purchasing === plan.id;
            
            return (
              <View
                key={plan.id}
                style={[
                  styles.planCard,
                  isCurrentPlan && styles.currentPlanCard,
                  plan.name.toLowerCase() === 'premium' && styles.popularPlan,
                ]}
              >
                {plan.name.toLowerCase() === 'premium' && (
                  <View style={styles.popularBadge}>
                    <Text style={styles.popularText}>Most Popular</Text>
                  </View>
                )}
                
                <View style={styles.planHeader}>
                  {getPlanIcon(plan.name)}
                  <Text style={styles.planName}>{plan.name}</Text>
                </View>
                
                <Text style={styles.planDescription}>{plan.description}</Text>
                
                <View style={styles.priceContainer}>
                  <Text style={styles.price}>${price}</Text>
                  <Text style={styles.pricePeriod}>
                    /{billingPeriod === 'yearly' ? 'year' : 'month'}
                  </Text>
                </View>
                
                {billingPeriod === 'yearly' && (
                  <Text style={styles.monthlyEquivalent}>
                    ${(plan.price_yearly / 12).toFixed(2)}/month
                  </Text>
                )}
                
                <View style={styles.featuresContainer}>
                  {plan.features.map((feature, index) => (
                    <View key={index} style={styles.featureItem}>
                      <Check size={16} color={planColor} />
                      <Text style={styles.featureText}>{feature}</Text>
                    </View>
                  ))}
                </View>
                
                <Button
                  title={
                    isCurrentPlan
                      ? 'Current Plan'
                      : isPurchasing
                      ? 'Processing...'
                      : `Subscribe to ${plan.name}`
                  }
                  onPress={() => handleSubscribe(plan)}
                  disabled={isCurrentPlan || isPurchasing}
                  style={[
                    styles.subscribeButton,
                    { backgroundColor: isCurrentPlan ? '#6B7280' : planColor },
                  ]}
                >
                  {isPurchasing && (
                    <ActivityIndicator
                      color="#FFFFFF"
                      size="small"
                      style={{ marginRight: 8 }}
                    />
                  )}
                </Button>
              </View>
            );
          })}
        </View>
        
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            All plans include a 7-day free trial. Cancel anytime.
          </Text>
          <Text style={styles.footerSubtext}>
            Prices may vary by region. Subscriptions automatically renew.
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F3E9',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    padding: 24,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1A2E4C',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  currentSubscriptionCard: {
    backgroundColor: '#FFFFFF',
    margin: 16,
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  currentSubscriptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 12,
  },
  currentSubscriptionInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  currentSubscriptionDetails: {
    marginLeft: 12,
    flex: 1,
  },
  currentSubscriptionName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
  },
  currentSubscriptionStatus: {
    fontSize: 14,
    color: '#6B7280',
  },
  billingToggle: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    margin: 16,
    borderRadius: 8,
    padding: 4,
  },
  billingOption: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
    position: 'relative',
  },
  billingOptionActive: {
    backgroundColor: '#B76E79',
  },
  billingOptionText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#6B7280',
  },
  billingOptionTextActive: {
    color: '#FFFFFF',
  },
  saveBadge: {
    position: 'absolute',
    top: -8,
    right: 8,
    backgroundColor: '#10B981',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  saveText: {
    fontSize: 10,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  plansContainer: {
    padding: 16,
  },
  planCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 24,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    position: 'relative',
  },
  currentPlanCard: {
    borderWidth: 2,
    borderColor: '#10B981',
  },
  popularPlan: {
    borderWidth: 2,
    borderColor: '#8B5CF6',
  },
  popularBadge: {
    position: 'absolute',
    top: -8,
    left: 24,
    right: 24,
    backgroundColor: '#8B5CF6',
    paddingVertical: 6,
    borderRadius: 16,
    alignItems: 'center',
  },
  popularText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  planHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    marginTop: 8,
  },
  planName: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1A2E4C',
    marginLeft: 12,
  },
  planDescription: {
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 20,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 4,
  },
  price: {
    fontSize: 36,
    fontWeight: '700',
    color: '#1A2E4C',
  },
  pricePeriod: {
    fontSize: 16,
    color: '#6B7280',
    marginLeft: 4,
  },
  monthlyEquivalent: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 20,
  },
  featuresContainer: {
    marginBottom: 24,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  featureText: {
    fontSize: 16,
    color: '#4B5563',
    marginLeft: 12,
  },
  subscribeButton: {
    marginTop: 8,
  },
  footer: {
    padding: 24,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 8,
  },
  footerSubtext: {
    fontSize: 12,
    color: '#9CA3AF',
    textAlign: 'center',
  },
});