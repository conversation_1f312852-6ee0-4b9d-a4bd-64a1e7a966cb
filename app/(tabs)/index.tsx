import { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, ActivityIndicator, TouchableOpacity, ScrollView } from 'react-native';
import { Link } from 'expo-router';
import { useSupabase } from '@/lib/supabase/supabase-provider';
import { SafeAreaView } from 'react-native-safe-area-context';
import { SearchBar } from '@/components/ui/SearchBar';
import { CompanionCard } from '@/components/companions/CompanionCard';
import { StatusBar } from 'expo-status-bar';
import { LocationMarker, Star, SlidersHorizontal, Heart, Clock } from 'lucide-react-native';
import { AdvancedSearchModal } from '@/components/search/AdvancedSearchModal';
import { UserService, SearchFilters, SortOption } from '@/lib/user/user-service';
import { AnalyticsService } from '@/lib/analytics/analytics-service';

export default function DiscoverScreen() {
  const [companions, setCompanions] = useState<any[]>([]);
  const [recentlyViewed, setRecentlyViewed] = useState<any[]>([]);
  const [favorites, setFavorites] = useState<any[]>([]);
  const [recommendations, setRecommendations] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({});
  const [sortOption, setSortOption] = useState<SortOption>({ field: 'created_at', direction: 'desc' });
  const { supabase, user } = useSupabase();
  
  const userService = UserService.getInstance();
  const analyticsService = AnalyticsService.getInstance();

  useEffect(() => {
    userService.initialize(supabase);
    analyticsService.initialize(supabase, user?.id);
    fetchCompanions();
    if (user) {
      fetchUserData();
    }
    
    // Track page view
    analyticsService.trackPageView('discover');
  }, []);
  
  useEffect(() => {
    if (searchQuery || Object.keys(searchFilters).length > 0) {
      searchCompanions();
    } else {
      fetchCompanions();
    }
  }, [searchQuery, searchFilters, sortOption]);

  const fetchCompanions = async () => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('companions')
        .select(`
          *,
          profiles(*),
          companion_photos(photo_url, is_primary)
        `)
        .eq('is_verified', true)
        .eq('is_active', true);
        
      if (error) {
        throw error;
      }
      
      setCompanions(data || []);
    } catch (err: any) {
      console.error('Error fetching companions:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchUserData = async () => {
    if (!user) return;
    
    try {
      // Fetch recently viewed
      const recentlyViewedData = await userService.getRecentlyViewed(user.id, 5);
      setRecentlyViewed(recentlyViewedData);
      
      // Fetch favorites
      const favoritesData = await userService.getFavorites(user.id, 'companion');
      setFavorites(favoritesData);
      
      // Fetch personalized recommendations
      const recommendationsData = await userService.getPersonalizedRecommendations(user.id, 5);
      setRecommendations(recommendationsData);
    } catch (error) {
      console.error('Error fetching user data:', error);
    }
  };
  
  const searchCompanions = async () => {
    try {
      setLoading(true);
      
      const results = await userService.searchCompanions(
        { ...searchFilters, query: searchQuery },
        sortOption,
        undefined, // user location would go here
        20
      );
      
      setCompanions(results);
      
      // Track search
      analyticsService.trackAction('search_performed', 'discovery', {
        query: searchQuery,
        filters: searchFilters,
        results_count: results.length,
      });
    } catch (err: any) {
      console.error('Error searching companions:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  
  const handleCompanionPress = async (companion: any) => {
    // Track recently viewed
    if (user) {
      await userService.trackRecentlyViewed(user.id, companion.id);
    }
    
    // Track analytics
    analyticsService.trackAction('companion_profile_viewed', 'discovery', {
      companion_id: companion.id,
      source: 'discover_screen',
    });
  };
  
  const toggleFavorite = async (companion: any) => {
    if (!user) return;
    
    try {
      const isFavorite = await userService.isFavorite(user.id, companion.id);
      
      if (isFavorite) {
        const favorite = favorites.find(f => f.companionId === companion.id);
        if (favorite) {
          await userService.removeFromFavorites(user.id, favorite.id);
          setFavorites(favorites.filter(f => f.id !== favorite.id));
        }
      } else {
        await userService.addToFavorites(user.id, 'companion', { companionId: companion.id });
        fetchUserData(); // Refresh favorites
      }
      
      analyticsService.trackAction('companion_favorited', 'engagement', {
        companion_id: companion.id,
        action: isFavorite ? 'removed' : 'added',
      });
    } catch (error) {
      console.error('Error toggling favorite:', error);
    }
  };
  const filteredCompanions = companions.filter(companion => {
    const profile = companion.profiles;
    const fullName = profile?.full_name?.toLowerCase() || '';
    const interests = companion.interests?.toLowerCase() || '';
    const query = searchQuery.toLowerCase();
    
    return fullName.includes(query) || interests.includes(query);
  });

  const renderSection = (title: string, data: any[], emptyText: string) => {
    if (!data.length) return null;
    
    return (
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>{title}</Text>
          <TouchableOpacity>
            <Text style={styles.seeAllText}>See All</Text>
          </TouchableOpacity>
        </View>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {data.map((item, index) => (
            <View key={index} style={styles.horizontalCard}>
              <Link href={`/(companion)/${item.companion?.id || item.id}`} asChild>
                <TouchableOpacity onPress={() => handleCompanionPress(item.companion || item)}>
                  <CompanionCard companion={item.companion || item} />
                </TouchableOpacity>
              </Link>
            </View>
          ))}
        </ScrollView>
      </View>
    );
  };

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <Text style={styles.headerTitle}>Find Your Perfect Match</Text>
      <Text style={styles.headerSubtitle}>Discover companions near you</Text>
      
      <View style={styles.locationContainer}>
        <LocationMarker size={16} color="#B76E79" />
        <Text style={styles.locationText}>New York, NY</Text>
      </View>
      
      <View style={styles.searchContainer}>
        <SearchBar
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholder="Search by name, interests..."
        />
        <TouchableOpacity 
          style={styles.filterButton}
          onPress={() => setShowAdvancedSearch(true)}
        >
          <SlidersHorizontal size={20} color="#1A2E4C" />
        </TouchableOpacity>
      </View>
      
      <View style={styles.categoriesContainer}>
        <Text style={styles.categoryHeader}>Categories</Text>
        <ScrollCategories />
      </View>
      
      {/* Recently Viewed */}
      {renderSection('Recently Viewed', recentlyViewed, 'No recently viewed companions')}
      
      {/* Favorites */}
      {renderSection('Your Favorites', favorites, 'No favorite companions yet')}
      
      {/* Recommendations */}
      {renderSection('Recommended for You', recommendations, 'No recommendations available')}
      
      {/* All Companions Header */}
      <View style={styles.allCompanionsHeader}>
        <Text style={styles.sectionTitle}>All Companions</Text>
        <View style={styles.sortContainer}>
          <TouchableOpacity style={styles.sortButton}>
            <Text style={styles.sortText}>Sort by: Rating</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color="#B76E79" />
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.centered}>
        <Text style={styles.errorText}>Failed to load companions</Text>
        <TouchableOpacity style={styles.retryButton} onPress={fetchCompanions}>
          <Text style={styles.retryText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style="dark" />
      
      <FlatList
        data={filteredCompanions}
        keyExtractor={(item) => item.id.toString()}
        renderItem={({ item }) => (
          <Link href={`/(companion)/${item.id}`} asChild>
            <TouchableOpacity onPress={() => handleCompanionPress(item)}>
              <CompanionCard companion={item} />
            </TouchableOpacity>
          </Link>
        )}
        ListHeaderComponent={renderHeader}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
      />
      
      <AdvancedSearchModal
        visible={showAdvancedSearch}
        onClose={() => setShowAdvancedSearch(false)}
        onApplyFilters={setSearchFilters}
        initialFilters={searchFilters}
      />
    </SafeAreaView>
  );
}

function ScrollCategories() {
  const categories = [
    { id: 1, name: 'Dinner Date', icon: '🍽️' },
    { id: 2, name: 'Movie Night', icon: '🎬' },
    { id: 3, name: 'Outdoor', icon: '🏞️' },
    { id: 4, name: 'Cultural', icon: '🏛️' },
    { id: 5, name: 'Party', icon: '🎉' },
  ];

  return (
    <View style={styles.scrollCategoriesContainer}>
      {categories.map((category) => (
        <TouchableOpacity key={category.id} style={styles.categoryItem}>
          <Text style={styles.categoryIcon}>{category.icon}</Text>
          <Text style={styles.categoryName}>{category.name}</Text>
        </TouchableOpacity>
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F3E9',
  },
  headerContainer: {
    padding: 16,
    paddingTop: 8,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1A2E4C',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 12,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  locationText: {
    fontSize: 14,
    color: '#B76E79',
    marginLeft: 4,
    fontWeight: '500',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  categoriesContainer: {
    marginBottom: 24,
  },
  categoryHeader: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 12,
  },
  scrollCategoriesContainer: {
    flexDirection: 'row',
    paddingVertical: 4,
  },
  categoryItem: {
    alignItems: 'center',
    marginRight: 16,
    width: 80,
  },
  categoryIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  categoryName: {
    fontSize: 13,
    color: '#4B5563',
    textAlign: 'center',
  },
  featuredContainer: {
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
  },
  seeAllText: {
    fontSize: 14,
    color: '#B76E79',
    fontWeight: '500',
  },
  filterButton: {
    width: 48,
    height: 48,
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  section: {
    marginBottom: 24,
  },
  horizontalCard: {
    width: 280,
    marginRight: 16,
  },
  allCompanionsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sortContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sortButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  sortText: {
    fontSize: 12,
    color: '#6B7280',
  },
  listContent: {
    paddingBottom: 24,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F7F3E9',
  },
  errorText: {
    fontSize: 16,
    color: '#B91C1C',
    marginBottom: 12,
  },
  retryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#B76E79',
    borderRadius: 8,
  },
  retryText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
});