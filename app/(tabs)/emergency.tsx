import { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, ScrollView, ActivityIndicator, Switch } from 'react-native';
import { useSupabase } from '@/lib/supabase/supabase-provider';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { 
  AlertTriangle, 
  Phone, 
  Shield, 
  Users, 
  Plus, 
  Settings,
  Heart,
  Clock,
  MapPin,
  Mic,
  Video,
  Camera,
  CheckCircle,
  XCircle
} from 'lucide-react-native';
import { EmergencyService, EmergencyContact, EmergencyIncident, SOSConfiguration } from '@/lib/emergency/emergency-service';
import { LocationService } from '@/lib/location/location-service';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';

export default function EmergencyScreen() {
  const [emergencyContacts, setEmergencyContacts] = useState<EmergencyContact[]>([]);
  const [activeIncident, setActiveIncident] = useState<EmergencyIncident | null>(null);
  const [sosConfig, setSOSConfig] = useState<SOSConfiguration | null>(null);
  const [loading, setLoading] = useState(true);
  const [showAddContact, setShowAddContact] = useState(false);
  const [systemStatus, setSystemStatus] = useState<boolean | null>(null);
  const [newContact, setNewContact] = useState({
    name: '',
    phoneNumber: '',
    email: '',
    relationship: 'family' as EmergencyContact['relationship'],
  });
  
  const { supabase, user } = useSupabase();
  const emergencyService = EmergencyService.getInstance();
  const locationService = LocationService.getInstance();

  useEffect(() => {
    loadEmergencyData();
    testEmergencySystem();
  }, []);

  const loadEmergencyData = async () => {
    try {
      setLoading(true);
      
      // Load emergency contacts from database
      const { data: contactsData, error: contactsError } = await supabase
        .from('emergency_contacts')
        .select('*')
        .eq('user_id', user?.id)
        .order('is_primary', { ascending: false });

      if (contactsError) throw contactsError;
      
      const contacts: EmergencyContact[] = contactsData?.map(contact => ({
        id: contact.id,
        name: contact.name,
        phoneNumber: contact.phone_number,
        email: contact.email,
        relationship: contact.relationship,
        isPrimary: contact.is_primary,
        isMedical: contact.is_medical,
        notes: contact.notes,
      })) || [];
      
      setEmergencyContacts(contacts);
      
      // Load SOS configuration
      const config = emergencyService.getSOSConfiguration();
      setSOSConfig(config);
      
      // Check for active incidents
      const { data: incidentData, error: incidentError } = await supabase
        .from('emergency_incidents')
        .select('*')
        .eq('user_id', user?.id)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .limit(1)
        .maybeSingle();

      if (incidentError && incidentError.code !== 'PGRST116') throw incidentError;
      
      if (incidentData) {
        const incident: EmergencyIncident = {
          id: incidentData.id,
          userId: incidentData.user_id,
          incidentType: incidentData.incident_type,
          status: incidentData.status,
          priority: incidentData.priority,
          description: incidentData.description,
          contactsNotified: incidentData.contacts_notified || [],
          authoritiesContacted: incidentData.authorities_contacted,
          createdAt: new Date(incidentData.created_at),
        };
        setActiveIncident(incident);
      }
    } catch (error) {
      console.error('Error loading emergency data:', error);
    } finally {
      setLoading(false);
    }
  };

  const testEmergencySystem = async () => {
    try {
      const isSystemReady = await emergencyService.testEmergencySystem();
      setSystemStatus(isSystemReady);
    } catch (error) {
      console.error('Error testing emergency system:', error);
      setSystemStatus(false);
    }
  };

  const triggerSOS = async () => {
    Alert.alert(
      '🚨 Emergency SOS',
      'This will immediately alert your emergency contacts and start emergency protocols. Are you sure?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'TRIGGER SOS',
          style: 'destructive',
          onPress: async () => {
            try {
              // Get current location
              const location = await locationService.getCurrentLocation();
              
              // Trigger SOS
              const incident = await emergencyService.triggerSOS(
                location || undefined,
                'Emergency SOS triggered from app',
                'sos'
              );
              
              // Save to database
              const { error } = await supabase
                .from('emergency_incidents')
                .insert({
                  user_id: user?.id,
                  incident_type: incident.incidentType,
                  status: incident.status,
                  priority: incident.priority,
                  latitude: incident.location?.latitude,
                  longitude: incident.location?.longitude,
                  description: incident.description,
                  contacts_notified: incident.contactsNotified,
                  authorities_contacted: incident.authoritiesContacted,
                });

              if (error) throw error;
              
              setActiveIncident(incident);
              
              Alert.alert(
                'SOS Activated',
                'Emergency contacts have been notified. Emergency services will be contacted automatically if configured.',
                [{ text: 'OK' }]
              );
            } catch (error) {
              console.error('Error triggering SOS:', error);
              Alert.alert('Error', 'Failed to trigger SOS. Please call emergency services directly.');
            }
          },
        },
      ]
    );
  };

  const resolveIncident = async (resolution: 'resolved' | 'false_alarm') => {
    if (!activeIncident) return;

    try {
      await emergencyService.resolveIncident(activeIncident.id, resolution);
      
      // Update database
      const { error } = await supabase
        .from('emergency_incidents')
        .update({
          status: resolution,
          resolved_at: new Date().toISOString(),
        })
        .eq('id', activeIncident.id);

      if (error) throw error;
      
      setActiveIncident(null);
      
      Alert.alert(
        'Incident Resolved',
        resolution === 'resolved' 
          ? 'Emergency has been resolved. Contacts have been notified.'
          : 'False alarm reported. Contacts have been notified.'
      );
    } catch (error) {
      console.error('Error resolving incident:', error);
    }
  };

  const addEmergencyContact = async () => {
    if (!newContact.name || !newContact.phoneNumber) {
      Alert.alert('Error', 'Please fill in name and phone number');
      return;
    }

    try {
      const { data, error } = await supabase
        .from('emergency_contacts')
        .insert({
          user_id: user?.id,
          name: newContact.name,
          phone_number: newContact.phoneNumber,
          email: newContact.email || null,
          relationship: newContact.relationship,
          is_primary: emergencyContacts.length === 0, // First contact is primary
        })
        .select()
        .single();

      if (error) throw error;

      const contact: EmergencyContact = {
        id: data.id,
        name: data.name,
        phoneNumber: data.phone_number,
        email: data.email,
        relationship: data.relationship,
        isPrimary: data.is_primary,
        isMedical: data.is_medical,
        notes: data.notes,
      };

      setEmergencyContacts([...emergencyContacts, contact]);
      setNewContact({ name: '', phoneNumber: '', email: '', relationship: 'family' });
      setShowAddContact(false);
      
      Alert.alert('Success', 'Emergency contact added successfully');
    } catch (error) {
      console.error('Error adding emergency contact:', error);
      Alert.alert('Error', 'Failed to add emergency contact');
    }
  };

  const removeEmergencyContact = async (contactId: string) => {
    Alert.alert(
      'Remove Contact',
      'Are you sure you want to remove this emergency contact?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              const { error } = await supabase
                .from('emergency_contacts')
                .delete()
                .eq('id', contactId);

              if (error) throw error;
              
              setEmergencyContacts(emergencyContacts.filter(c => c.id !== contactId));
            } catch (error) {
              console.error('Error removing emergency contact:', error);
            }
          },
        },
      ]
    );
  };

  const updateSOSConfig = (updates: Partial<SOSConfiguration>) => {
    if (!sosConfig) return;
    
    const newConfig = { ...sosConfig, ...updates };
    setSOSConfig(newConfig);
    emergencyService.updateSOSConfiguration(newConfig);
  };

  const callEmergencyServices = () => {
    Alert.alert(
      'Call Emergency Services',
      'This will call 911 (or your local emergency number). Continue?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Call 911',
          style: 'destructive',
          onPress: () => {
            // This would open the phone dialer
            console.log('Calling emergency services...');
          },
        },
      ]
    );
  };

  const ContactCard = ({ contact }: { contact: EmergencyContact }) => (
    <View style={styles.contactCard}>
      <View style={styles.contactInfo}>
        <Text style={styles.contactName}>{contact.name}</Text>
        <Text style={styles.contactPhone}>{contact.phoneNumber}</Text>
        <Text style={styles.contactRelationship}>{contact.relationship}</Text>
        {contact.isPrimary && (
          <View style={styles.primaryBadge}>
            <Text style={styles.primaryText}>Primary</Text>
          </View>
        )}
      </View>
      <TouchableOpacity
        style={styles.removeButton}
        onPress={() => removeEmergencyContact(contact.id)}
      >
        <XCircle size={20} color="#EF4444" />
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color="#B76E79" />
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style="dark" />
      
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Emergency & Safety</Text>
        <TouchableOpacity style={styles.settingsButton}>
          <Settings size={24} color="#1A2E4C" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* System Status */}
        <View style={styles.statusCard}>
          <View style={styles.statusHeader}>
            <Shield size={24} color={systemStatus ? '#10B981' : '#EF4444'} />
            <Text style={styles.statusTitle}>Emergency System Status</Text>
            {systemStatus ? (
              <CheckCircle size={20} color="#10B981" />
            ) : (
              <XCircle size={20} color="#EF4444" />
            )}
          </View>
          <Text style={styles.statusText}>
            {systemStatus 
              ? 'All emergency systems are operational'
              : 'Some emergency features may not be available'
            }
          </Text>
          <TouchableOpacity style={styles.testButton} onPress={testEmergencySystem}>
            <Text style={styles.testButtonText}>Test System</Text>
          </TouchableOpacity>
        </View>

        {/* Active Incident */}
        {activeIncident && (
          <View style={styles.activeIncidentCard}>
            <View style={styles.incidentHeader}>
              <AlertTriangle size={24} color="#EF4444" />
              <Text style={styles.incidentTitle}>Active Emergency</Text>
            </View>
            <Text style={styles.incidentDescription}>
              {activeIncident.description}
            </Text>
            <Text style={styles.incidentTime}>
              Started: {activeIncident.createdAt.toLocaleString()}
            </Text>
            <View style={styles.incidentActions}>
              <Button
                title="Mark Resolved"
                onPress={() => resolveIncident('resolved')}
                style={styles.resolveButton}
              />
              <Button
                title="False Alarm"
                variant="outline"
                onPress={() => resolveIncident('false_alarm')}
                style={styles.falseAlarmButton}
              />
            </View>
          </View>
        )}

        {/* SOS Button */}
        <TouchableOpacity
          style={[styles.sosButton, activeIncident && styles.sosButtonDisabled]}
          onPress={triggerSOS}
          disabled={!!activeIncident}
        >
          <AlertTriangle size={32} color="#FFFFFF" />
          <Text style={styles.sosButtonText}>EMERGENCY SOS</Text>
          <Text style={styles.sosButtonSubtext}>
            {activeIncident ? 'Emergency Active' : 'Tap to alert contacts'}
          </Text>
        </TouchableOpacity>

        {/* Quick Actions */}
        <View style={styles.quickActions}>
          <TouchableOpacity style={styles.quickActionButton} onPress={callEmergencyServices}>
            <Phone size={24} color="#EF4444" />
            <Text style={styles.quickActionText}>Call 911</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.quickActionButton}>
            <MapPin size={24} color="#3B82F6" />
            <Text style={styles.quickActionText}>Share Location</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.quickActionButton}>
            <Heart size={24} color="#10B981" />
            <Text style={styles.quickActionText}>Medical Info</Text>
          </TouchableOpacity>
        </View>

        {/* Emergency Contacts */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Users size={20} color="#B76E79" />
            <Text style={styles.sectionTitle}>Emergency Contacts</Text>
            <TouchableOpacity
              style={styles.addButton}
              onPress={() => setShowAddContact(true)}
            >
              <Plus size={20} color="#B76E79" />
            </TouchableOpacity>
          </View>
          
          {emergencyContacts.length === 0 ? (
            <View style={styles.emptyContacts}>
              <Text style={styles.emptyContactsText}>
                No emergency contacts added yet
              </Text>
              <Text style={styles.emptyContactsSubtext}>
                Add contacts who should be notified in case of emergency
              </Text>
            </View>
          ) : (
            emergencyContacts.map((contact) => (
              <ContactCard key={contact.id} contact={contact} />
            ))
          )}
        </View>

        {/* SOS Configuration */}
        {sosConfig && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>SOS Settings</Text>
            
            <View style={styles.configOption}>
              <Text style={styles.configLabel}>Auto-call Emergency Services</Text>
              <Switch
                value={sosConfig.enableAutoCall}
                onValueChange={(value) => updateSOSConfig({ enableAutoCall: value })}
                trackColor={{ false: '#D1D5DB', true: '#EF4444' }}
                thumbColor={sosConfig.enableAutoCall ? '#FFFFFF' : '#F3F4F6'}
              />
            </View>
            
            <View style={styles.configOption}>
              <Text style={styles.configLabel}>Share Location Automatically</Text>
              <Switch
                value={sosConfig.enableLocationSharing}
                onValueChange={(value) => updateSOSConfig({ enableLocationSharing: value })}
                trackColor={{ false: '#D1D5DB', true: '#3B82F6' }}
                thumbColor={sosConfig.enableLocationSharing ? '#FFFFFF' : '#F3F4F6'}
              />
            </View>
            
            <View style={styles.configOption}>
              <Text style={styles.configLabel}>Record Audio During Emergency</Text>
              <Switch
                value={sosConfig.enableAudioRecording}
                onValueChange={(value) => updateSOSConfig({ enableAudioRecording: value })}
                trackColor={{ false: '#D1D5DB', true: '#10B981' }}
                thumbColor={sosConfig.enableAudioRecording ? '#FFFFFF' : '#F3F4F6'}
              />
            </View>
          </View>
        )}

        {/* Add Contact Modal */}
        {showAddContact && (
          <View style={styles.addContactModal}>
            <Text style={styles.modalTitle}>Add Emergency Contact</Text>
            
            <Input
              label="Name *"
              value={newContact.name}
              onChangeText={(text) => setNewContact({ ...newContact, name: text })}
              placeholder="Contact name"
            />
            
            <Input
              label="Phone Number *"
              value={newContact.phoneNumber}
              onChangeText={(text) => setNewContact({ ...newContact, phoneNumber: text })}
              placeholder="+****************"
              keyboardType="phone-pad"
            />
            
            <Input
              label="Email"
              value={newContact.email}
              onChangeText={(text) => setNewContact({ ...newContact, email: text })}
              placeholder="<EMAIL>"
              keyboardType="email-address"
            />
            
            <View style={styles.modalActions}>
              <Button
                title="Cancel"
                variant="outline"
                onPress={() => setShowAddContact(false)}
                style={styles.modalButton}
              />
              <Button
                title="Add Contact"
                onPress={addEmergencyContact}
                style={styles.modalButton}
              />
            </View>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F3E9',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1A2E4C',
  },
  settingsButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  statusCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    flex: 1,
    marginLeft: 8,
  },
  statusText: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 12,
  },
  testButton: {
    backgroundColor: '#F3F4F6',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignSelf: 'flex-start',
  },
  testButtonText: {
    fontSize: 14,
    color: '#1A2E4C',
    fontWeight: '500',
  },
  activeIncidentCard: {
    backgroundColor: '#FEE2E2',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    borderLeftWidth: 4,
    borderLeftColor: '#EF4444',
  },
  incidentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  incidentTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#991B1B',
    marginLeft: 8,
  },
  incidentDescription: {
    fontSize: 14,
    color: '#7F1D1D',
    marginBottom: 8,
  },
  incidentTime: {
    fontSize: 12,
    color: '#991B1B',
    marginBottom: 16,
  },
  incidentActions: {
    flexDirection: 'row',
    gap: 12,
  },
  resolveButton: {
    flex: 1,
    backgroundColor: '#10B981',
  },
  falseAlarmButton: {
    flex: 1,
    borderColor: '#EF4444',
  },
  sosButton: {
    backgroundColor: '#EF4444',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    marginBottom: 24,
    shadowColor: '#EF4444',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  sosButtonDisabled: {
    backgroundColor: '#9CA3AF',
    shadowColor: '#9CA3AF',
  },
  sosButtonText: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
    marginTop: 8,
  },
  sosButtonSubtext: {
    fontSize: 14,
    color: '#FFFFFF',
    opacity: 0.9,
    marginTop: 4,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  quickActionButton: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  quickActionText: {
    fontSize: 12,
    color: '#1A2E4C',
    marginTop: 8,
    textAlign: 'center',
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
    flex: 1,
    marginLeft: 8,
  },
  addButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F7F3E9',
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContacts: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
  },
  emptyContactsText: {
    fontSize: 16,
    color: '#1A2E4C',
    marginBottom: 8,
  },
  emptyContactsSubtext: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
  },
  contactCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 4,
  },
  contactPhone: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 2,
  },
  contactRelationship: {
    fontSize: 12,
    color: '#9CA3AF',
    textTransform: 'capitalize',
  },
  primaryBadge: {
    backgroundColor: '#D1FAE5',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    alignSelf: 'flex-start',
    marginTop: 4,
  },
  primaryText: {
    fontSize: 10,
    color: '#065F46',
    fontWeight: '500',
  },
  removeButton: {
    padding: 8,
  },
  configOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  configLabel: {
    fontSize: 16,
    color: '#1A2E4C',
    flex: 1,
  },
  addContactModal: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    marginTop: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 20,
  },
  modalActions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 16,
  },
  modalButton: {
    flex: 1,
  },
});