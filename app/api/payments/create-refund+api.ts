import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16',
});

export async function POST(request: Request) {
  try {
    const { payment_intent_id, amount, reason = 'requested_by_customer' } = await request.json();

    if (!payment_intent_id) {
      return new Response(
        JSON.stringify({ error: 'Missing payment intent ID' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    const refund = await stripe.refunds.create({
      payment_intent: payment_intent_id,
      amount,
      reason,
    });

    return new Response(
      JSON.stringify({
        refund_id: refund.id,
        amount: refund.amount,
        status: refund.status,
        reason: refund.reason,
      }),
      { headers: { 'Content-Type': 'application/json' } }
    );
  } catch (error: any) {
    console.error('Error creating refund:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}