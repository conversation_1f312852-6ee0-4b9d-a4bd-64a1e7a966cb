import Stripe from 'stripe';
import { createClient } from '@supabase/supabase-js';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16',
});

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: Request) {
  const body = await request.text();
  const signature = request.headers.get('stripe-signature');

  if (!signature) {
    return new Response('Missing signature', { status: 400 });
  }

  try {
    const event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    );

    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentSucceeded(event.data.object as Stripe.PaymentIntent);
        break;
      
      case 'payment_intent.payment_failed':
        await handlePaymentFailed(event.data.object as Stripe.PaymentIntent);
        break;
      
      case 'invoice.payment_succeeded':
        await handleSubscriptionPaymentSucceeded(event.data.object as Stripe.Invoice);
        break;
      
      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object as Stripe.Subscription);
        break;
      
      case 'customer.subscription.deleted':
        await handleSubscriptionCanceled(event.data.object as Stripe.Subscription);
        break;
      
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return new Response('Webhook handled', { status: 200 });
  } catch (error: any) {
    console.error('Webhook error:', error);
    return new Response(`Webhook error: ${error.message}`, { status: 400 });
  }
}

async function handlePaymentSucceeded(paymentIntent: Stripe.PaymentIntent) {
  try {
    // Update booking payment status
    if (paymentIntent.metadata.booking_id) {
      await supabase
        .from('bookings')
        .update({
          payment_status: 'paid',
          status: 'confirmed',
          updated_at: new Date().toISOString(),
        })
        .eq('payment_intent_id', paymentIntent.id);
    }

    // Create transaction record
    await supabase
      .from('transactions')
      .insert({
        user_id: paymentIntent.metadata.user_id,
        booking_id: paymentIntent.metadata.booking_id,
        companion_id: paymentIntent.metadata.companion_id,
        type: 'booking_payment',
        status: 'completed',
        amount: paymentIntent.amount / 100,
        currency: paymentIntent.currency,
        provider: 'stripe',
        provider_transaction_id: paymentIntent.id,
        provider_payment_intent_id: paymentIntent.id,
        processed_at: new Date().toISOString(),
      });
  } catch (error) {
    console.error('Error handling payment succeeded:', error);
  }
}

async function handlePaymentFailed(paymentIntent: Stripe.PaymentIntent) {
  try {
    // Update booking payment status
    if (paymentIntent.metadata.booking_id) {
      await supabase
        .from('bookings')
        .update({
          payment_status: 'failed',
          updated_at: new Date().toISOString(),
        })
        .eq('payment_intent_id', paymentIntent.id);
    }

    // Create failed transaction record
    await supabase
      .from('transactions')
      .insert({
        user_id: paymentIntent.metadata.user_id,
        booking_id: paymentIntent.metadata.booking_id,
        type: 'booking_payment',
        status: 'failed',
        amount: paymentIntent.amount / 100,
        currency: paymentIntent.currency,
        provider: 'stripe',
        provider_payment_intent_id: paymentIntent.id,
      });
  } catch (error) {
    console.error('Error handling payment failed:', error);
  }
}

async function handleSubscriptionPaymentSucceeded(invoice: Stripe.Invoice) {
  try {
    if (invoice.subscription && invoice.customer) {
      // Create subscription payment transaction
      await supabase
        .from('transactions')
        .insert({
          user_id: invoice.metadata?.user_id,
          type: 'subscription_payment',
          status: 'completed',
          amount: (invoice.amount_paid || 0) / 100,
          currency: invoice.currency || 'usd',
          provider: 'stripe',
          provider_transaction_id: invoice.id,
          processed_at: new Date().toISOString(),
        });
    }
  } catch (error) {
    console.error('Error handling subscription payment succeeded:', error);
  }
}

async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  try {
    // Update user subscription status
    await supabase
      .from('user_subscriptions')
      .update({
        status: subscription.status === 'active' ? 'active' : subscription.status,
        current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
        current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
        cancel_at_period_end: subscription.cancel_at_period_end,
        updated_at: new Date().toISOString(),
      })
      .eq('stripe_subscription_id', subscription.id);
  } catch (error) {
    console.error('Error handling subscription updated:', error);
  }
}

async function handleSubscriptionCanceled(subscription: Stripe.Subscription) {
  try {
    // Update user subscription status
    await supabase
      .from('user_subscriptions')
      .update({
        status: 'cancelled',
        updated_at: new Date().toISOString(),
      })
      .eq('stripe_subscription_id', subscription.id);
  } catch (error) {
    console.error('Error handling subscription canceled:', error);
  }
}