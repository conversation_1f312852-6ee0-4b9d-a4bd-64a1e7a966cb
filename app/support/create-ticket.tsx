import { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import { useSupabase } from '@/lib/supabase/supabase-provider';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { ChevronLeft, CircleAlert as AlertCircle, CreditCard, Shield, User, Calendar, CircleHelp as HelpCircle } from 'lucide-react-native';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { router } from 'expo-router';

const CATEGORIES = [
  { id: 'technical', label: 'Technical Issue', icon: AlertCircle, color: '#3B82F6' },
  { id: 'billing', label: 'Billing & Payments', icon: CreditCard, color: '#10B981' },
  { id: 'safety', label: 'Safety & Security', icon: Shield, color: '#EF4444' },
  { id: 'account', label: 'Account Issues', icon: User, color: '#8B5CF6' },
  { id: 'booking', label: 'Booking Problems', icon: Calendar, color: '#F59E0B' },
  { id: 'general', label: 'General Inquiry', icon: HelpCircle, color: '#6B7280' },
];

const PRIORITIES = [
  { id: 'low', label: 'Low', description: 'General questions, non-urgent issues' },
  { id: 'medium', label: 'Medium', description: 'Standard support requests' },
  { id: 'high', label: 'High', description: 'Urgent issues affecting service' },
  { id: 'urgent', label: 'Urgent', description: 'Critical issues requiring immediate attention' },
];

export default function CreateTicketScreen() {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState('');
  const [priority, setPriority] = useState('medium');
  const [submitting, setSubmitting] = useState(false);
  const { supabase, user } = useSupabase();

  const handleSubmit = async () => {
    if (!title.trim() || !description.trim() || !category) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    try {
      setSubmitting(true);
      
      const { data, error } = await supabase
        .from('support_tickets')
        .insert([
          {
            user_id: user?.id,
            title: title.trim(),
            description: description.trim(),
            category,
            priority,
            status: 'open',
          }
        ])
        .select()
        .single();
        
      if (error) throw error;
      
      // Send initial system message
      await supabase
        .from('support_messages')
        .insert([
          {
            ticket_id: data.id,
            sender_id: user?.id,
            message: `Support ticket created: ${title}`,
            is_internal: false,
          }
        ]);
      
      Alert.alert(
        'Ticket Created',
        'Your support ticket has been created successfully. Our team will respond within 24 hours.',
        [
          {
            text: 'OK',
            onPress: () => router.replace(`/support/ticket/${data.id}`),
          }
        ]
      );
      
    } catch (error: any) {
      console.error('Error creating ticket:', error);
      Alert.alert('Error', error.message || 'Failed to create support ticket');
    } finally {
      setSubmitting(false);
    }
  };

  const CategoryCard = ({ item, selected, onSelect }: any) => {
    const IconComponent = item.icon;
    
    return (
      <TouchableOpacity
        style={[
          styles.categoryCard,
          selected && styles.categoryCardSelected,
          selected && { borderColor: item.color }
        ]}
        onPress={() => onSelect(item.id)}
      >
        <IconComponent size={24} color={selected ? item.color : '#6B7280'} />
        <Text style={[
          styles.categoryLabel,
          selected && { color: item.color }
        ]}>
          {item.label}
        </Text>
      </TouchableOpacity>
    );
  };

  const PriorityCard = ({ item, selected, onSelect }: any) => (
    <TouchableOpacity
      style={[
        styles.priorityCard,
        selected && styles.priorityCardSelected
      ]}
      onPress={() => onSelect(item.id)}
    >
      <View style={styles.priorityHeader}>
        <Text style={[
          styles.priorityLabel,
          selected && styles.priorityLabelSelected
        ]}>
          {item.label}
        </Text>
        {selected && (
          <View style={styles.selectedIndicator} />
        )}
      </View>
      <Text style={styles.priorityDescription}>
        {item.description}
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style="dark" />
      
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => router.back()}
        >
          <ChevronLeft size={24} color="#1A2E4C" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Create Support Ticket</Text>
        <View style={{ width: 24 }} />
      </View>
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>What can we help you with?</Text>
          
          <Input
            label="Subject *"
            value={title}
            onChangeText={setTitle}
            placeholder="Brief description of your issue"
            maxLength={100}
          />
          
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Description *</Text>
            <Input
              value={description}
              onChangeText={setDescription}
              placeholder="Please provide detailed information about your issue..."
              multiline
              style={styles.textArea}
              maxLength={1000}
            />
            <Text style={styles.characterCount}>
              {description.length}/1000 characters
            </Text>
          </View>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Category *</Text>
          <Text style={styles.sectionDescription}>
            Select the category that best describes your issue
          </Text>
          
          <View style={styles.categoriesGrid}>
            {CATEGORIES.map((item) => (
              <CategoryCard
                key={item.id}
                item={item}
                selected={category === item.id}
                onSelect={setCategory}
              />
            ))}
          </View>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Priority Level</Text>
          <Text style={styles.sectionDescription}>
            How urgent is this issue?
          </Text>
          
          <View style={styles.priorityList}>
            {PRIORITIES.map((item) => (
              <PriorityCard
                key={item.id}
                item={item}
                selected={priority === item.id}
                onSelect={setPriority}
              />
            ))}
          </View>
        </View>
        
        <View style={styles.section}>
          <View style={styles.infoCard}>
            <AlertCircle size={20} color="#3B82F6" />
            <View style={styles.infoContent}>
              <Text style={styles.infoTitle}>Response Time</Text>
              <Text style={styles.infoText}>
                We typically respond to support tickets within 24 hours. 
                Urgent issues are prioritized and may receive faster responses.
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
      
      <SafeAreaView edges={['bottom']} style={styles.footer}>
        <Button
          title={submitting ? "Creating Ticket..." : "Create Support Ticket"}
          onPress={handleSubmit}
          disabled={submitting || !title.trim() || !description.trim() || !category}
        >
          {submitting && (
            <ActivityIndicator color="#FFFFFF\" size="small\" style={{ marginRight: 8 }} />
          )}
        </Button>
      </SafeAreaView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F3E9',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4B5563',
    marginBottom: 6,
  },
  textArea: {
    height: 120,
    textAlignVertical: 'top',
  },
  characterCount: {
    fontSize: 12,
    color: '#9CA3AF',
    textAlign: 'right',
    marginTop: 4,
  },
  categoriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -6,
  },
  categoryCard: {
    width: '48%',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    margin: 6,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  categoryCardSelected: {
    borderWidth: 2,
    backgroundColor: '#F8FAFC',
  },
  categoryLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
    marginTop: 8,
    textAlign: 'center',
  },
  priorityList: {
    gap: 12,
  },
  priorityCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  priorityCardSelected: {
    borderColor: '#B76E79',
    backgroundColor: '#FDF2F8',
  },
  priorityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  priorityLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
  },
  priorityLabelSelected: {
    color: '#B76E79',
  },
  selectedIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#B76E79',
  },
  priorityDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
  infoCard: {
    flexDirection: 'row',
    backgroundColor: '#EBF8FF',
    borderRadius: 12,
    padding: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#3B82F6',
  },
  infoContent: {
    marginLeft: 12,
    flex: 1,
  },
  infoTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1E40AF',
    marginBottom: 4,
  },
  infoText: {
    fontSize: 14,
    color: '#1E40AF',
    lineHeight: 20,
  },
  footer: {
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    padding: 16,
  },
});