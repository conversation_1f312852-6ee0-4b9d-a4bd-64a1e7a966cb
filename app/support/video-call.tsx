import { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, Platform } from 'react-native';
import { useSupabase } from '@/lib/supabase/supabase-provider';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { Video, VideoOff, Mic, MicOff, Phone, PhoneOff, Users, Settings, Monitor } from 'lucide-react-native';
import { Button } from '@/components/ui/Button';
import { VideoCallInterface } from '@/components/video/VideoCallInterface';
import { router } from 'expo-router';

export default function VideoCallScreen() {
  const [isCallActive, setIsCallActive] = useState(false);
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [participants, setParticipants] = useState<any[]>([]);
  const [callSession, setCallSession] = useState<any>(null);
  const [connectionStatus, setConnectionStatus] = useState<string>('disconnected');
  const { supabase, user } = useSupabase();

  useEffect(() => {
    if (Platform.OS === 'web') {
      requestPermissions();
    }
    
    return () => {
      cleanup();
    };
  }, []);

  const requestPermissions = async () => {
    try {
      if (Platform.OS === 'web' && navigator.mediaDevices) {
        // Request camera and microphone permissions
        await navigator.mediaDevices.getUserMedia({
          video: true,
          audio: true,
        });
        setConnectionStatus('ready');
      }
    } catch (error) {
      console.error('Error requesting permissions:', error);
      Alert.alert('Permission Required', 'Please allow camera and microphone access to use video calling.');
    }
  };

  const createVideoCall = async (type: 'support' | 'booking' = 'support') => {
    try {
      const roomId = `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const { data: callData, error: callError } = await supabase
        .from('video_calls')
        .insert([
          {
            room_id: roomId,
            call_type: type,
            status: 'scheduled',
            host_id: user?.id,
            recording_enabled: false,
            screen_sharing_enabled: true,
            max_participants: 2,
          }
        ])
        .select()
        .single();
        
      if (callError) throw callError;
      
      // Add user as participant
      const { error: participantError } = await supabase
        .from('call_participants')
        .insert([
          {
            call_id: callData.id,
            user_id: user?.id,
            role: 'host',
            is_video_enabled: isVideoEnabled,
            is_audio_enabled: isAudioEnabled,
          }
        ]);
        
      if (participantError) throw participantError;
      
      setCallSession(callData);
      return callData;
    } catch (error) {
      console.error('Error creating video call:', error);
      throw error;
    }
  };

  const startCall = async () => {
    try {
      const call = await createVideoCall('support');
      
      // Update call status to active
      await supabase
        .from('video_calls')
        .update({
          status: 'active',
          started_at: new Date().toISOString(),
        })
        .eq('id', call.id);
        
      // Update participant join time
      await supabase
        .from('call_participants')
        .update({
          joined_at: new Date().toISOString(),
          is_video_enabled: isVideoEnabled,
          is_muted: !isAudioEnabled,
        })
        .eq('call_id', call.id)
        .eq('user_id', user?.id);
        
      setIsCallActive(true);
      setConnectionStatus('active');
      
    } catch (error) {
      console.error('Error starting call:', error);
      Alert.alert('Error', 'Failed to start video call');
    }
  };

  const endCall = async () => {
    try {
      if (callSession) {
        // Update call status
        await supabase
          .from('video_calls')
          .update({
            status: 'ended',
            ended_at: new Date().toISOString(),
            duration_seconds: Math.floor((Date.now() - new Date(callSession.started_at || callSession.created_at).getTime()) / 1000),
          })
          .eq('id', callSession.id);
          
        // Update participant leave time
        await supabase
          .from('call_participants')
          .update({
            left_at: new Date().toISOString(),
          })
          .eq('call_id', callSession.id)
          .eq('user_id', user?.id);
      }
      
      setIsCallActive(false);
      setConnectionStatus('disconnected');
      cleanup();
      
      router.back();
    } catch (error) {
      console.error('Error ending call:', error);
    }
  };

  const handleToggleVideo = (enabled: boolean) => {
    setIsVideoEnabled(enabled);
    
    if (callSession) {
      supabase
        .from('call_participants')
        .update({ is_video_enabled: enabled })
        .eq('call_id', callSession.id)
        .eq('user_id', user?.id);
    }
  };

  const handleToggleAudio = (enabled: boolean) => {
    setIsAudioEnabled(enabled);
    
    if (callSession) {
      supabase
        .from('call_participants')
        .update({ 
          is_audio_enabled: enabled,
          is_muted: !enabled 
        })
        .eq('call_id', callSession.id)
        .eq('user_id', user?.id);
    }
  };

  const handleToggleScreenShare = (enabled: boolean) => {
    if (callSession) {
      supabase
        .from('video_calls')
        .update({ screen_sharing_enabled: enabled })
        .eq('id', callSession.id);
    }
  };

  const cleanup = () => {
    // Cleanup will be handled by WebRTC service
  };

  const scheduleCall = () => {
    Alert.alert(
      'Schedule Video Call',
      'Would you like to schedule a video call with our support team?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Schedule', onPress: () => router.push('/support/schedule-call') },
      ]
    );
  };

  if (isCallActive) {
    return (
      <VideoCallInterface
        callId={callSession?.id}
        isHost={true}
        onEndCall={endCall}
        onToggleVideo={handleToggleVideo}
        onToggleAudio={handleToggleAudio}
        onToggleScreenShare={handleToggleScreenShare}
      />
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar style="light" />
      
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Video Support</Text>
        <TouchableOpacity style={styles.settingsButton}>
          <Settings size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>
      
      <View style={styles.preCallContainer}>
        <View style={styles.preCallContent}>
          <Video size={64} color="#B76E79" style={styles.preCallIcon} />
          <Text style={styles.preCallTitle}>Video Support Call</Text>
          <Text style={styles.preCallDescription}>
            Connect with our support team via video call for personalized assistance with screen sharing and HD quality
          </Text>
          
          <View style={styles.featuresList}>
            <View style={styles.featureItem}>
              <Video size={20} color="#10B981" />
              <Text style={styles.featureText}>HD Video Quality</Text>
            </View>
            <View style={styles.featureItem}>
              <Monitor size={20} color="#10B981" />
              <Text style={styles.featureText}>Screen Sharing</Text>
            </View>
            <View style={styles.featureItem}>
              <Phone size={20} color="#10B981" />
              <Text style={styles.featureText}>Crystal Clear Audio</Text>
            </View>
            <View style={styles.featureItem}>
              <Users size={20} color="#10B981" />
              <Text style={styles.featureText}>Multi-participant Support</Text>
            </View>
          </View>
          
          <View style={styles.preCallActions}>
            <Button
              title="Start Video Call"
              onPress={startCall}
              style={styles.startCallButton}
              disabled={connectionStatus !== 'ready'}
            />
            <Button
              title="Schedule Call"
              variant="outline"
              onPress={scheduleCall}
              style={styles.scheduleButton}
            />
          </View>
          
          {connectionStatus !== 'ready' && (
            <Text style={styles.statusText}>
              {connectionStatus === 'disconnected' 
                ? 'Requesting camera and microphone access...'
                : `Status: ${connectionStatus}`
              }
            </Text>
          )}
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1A2E4C',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  settingsButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  preCallContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  preCallContent: {
    alignItems: 'center',
    maxWidth: 320,
  },
  preCallIcon: {
    marginBottom: 24,
  },
  preCallTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
    marginBottom: 12,
    textAlign: 'center',
  },
  preCallDescription: {
    fontSize: 16,
    color: '#B0BEC5',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 32,
  },
  featuresList: {
    alignSelf: 'stretch',
    marginBottom: 32,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  featureText: {
    fontSize: 16,
    color: '#FFFFFF',
    marginLeft: 12,
  },
  preCallActions: {
    alignSelf: 'stretch',
    gap: 12,
  },
  startCallButton: {
    marginBottom: 0,
  },
  scheduleButton: {
    borderColor: '#FFFFFF',
  },
  statusText: {
    fontSize: 14,
    color: '#B0BEC5',
    textAlign: 'center',
    marginTop: 16,
    fontStyle: 'italic',
  },
});