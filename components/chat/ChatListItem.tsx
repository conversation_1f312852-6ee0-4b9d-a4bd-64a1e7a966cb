import React from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import { format } from 'date-fns';

interface ChatListItemProps {
  conversation: any;
}

export function ChatListItem({ conversation }: ChatListItemProps) {
  const otherPerson = conversation.companion.profiles;
  const latestMessage = conversation.latest_message?.[0];
  
  return (
    <View style={styles.container}>
      {otherPerson.avatar_url ? (
        <Image source={{ uri: otherPerson.avatar_url }} style={styles.avatar} />
      ) : (
        <View style={styles.avatarPlaceholder}>
          <Text style={styles.avatarText}>
            {otherPerson.full_name?.charAt(0) || '?'}
          </Text>
        </View>
      )}
      
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.name} numberOfLines={1}>
            {otherPerson.full_name}
          </Text>
          {latestMessage && (
            <Text style={styles.time}>
              {format(new Date(latestMessage.created_at), 'h:mm a')}
            </Text>
          )}
        </View>
        
        <Text style={styles.message} numberOfLines={2}>
          {latestMessage?.content || 'No messages yet'}
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  avatarPlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#B76E79',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  avatarText: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  name: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    flex: 1,
    marginRight: 8,
  },
  time: {
    fontSize: 12,
    color: '#6B7280',
  },
  message: {
    fontSize: 14,
    color: '#4B5563',
    lineHeight: 20,
  },
});