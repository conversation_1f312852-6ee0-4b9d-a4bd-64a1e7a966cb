import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, Dimensions } from 'react-native';
import { Heart, MapPin, Bell, Shield, ChevronRight, Check } from 'lucide-react-native';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { UserService } from '@/lib/user/user-service';
import { AnalyticsService } from '@/lib/analytics/analytics-service';
import { useSupabase } from '@/lib/supabase/supabase-provider';
import Animated, { FadeInRight, FadeOutLeft } from 'react-native-reanimated';

const { width } = Dimensions.get('window');

interface OnboardingFlowProps {
  onComplete: () => void;
}

export function OnboardingFlow({ onComplete }: OnboardingFlowProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [preferences, setPreferences] = useState({
    ageRangeMin: 21,
    ageRangeMax: 35,
    maxDistanceKm: 25,
    priceRangeMin: 50,
    priceRangeMax: 200,
    preferredInterests: [] as string[],
    notificationPreferences: {
      messages: true,
      bookings: true,
      marketing: false,
    },
  });
  const [loading, setLoading] = useState(false);
  const { user } = useSupabase();
  
  const userService = UserService.getInstance();
  const analyticsService = AnalyticsService.getInstance();

  const onboardingSteps = [
    {
      id: 'welcome',
      title: 'Welcome to HourlyGF',
      subtitle: 'Your premium companion booking platform',
      component: WelcomeStep,
    },
    {
      id: 'preferences',
      title: 'Set Your Preferences',
      subtitle: 'Help us find the perfect matches for you',
      component: PreferencesStep,
    },
    {
      id: 'interests',
      title: 'Choose Your Interests',
      subtitle: 'What activities do you enjoy?',
      component: InterestsStep,
    },
    {
      id: 'notifications',
      title: 'Stay Connected',
      subtitle: 'Choose how you want to be notified',
      component: NotificationsStep,
    },
    {
      id: 'location',
      title: 'Enable Location',
      subtitle: 'Find companions near you',
      component: LocationStep,
    },
    {
      id: 'complete',
      title: 'You\'re All Set!',
      subtitle: 'Start discovering amazing companions',
      component: CompleteStep,
    },
  ];

  useEffect(() => {
    analyticsService.trackPageView('onboarding_start');
  }, []);

  const handleNext = async () => {
    const currentStepData = onboardingSteps[currentStep];
    
    // Track step completion
    await analyticsService.trackAction('onboarding_step_completed', 'onboarding', {
      step: currentStepData.id,
      step_number: currentStep + 1,
    });

    if (user) {
      await userService.updateOnboardingStep(user.id, currentStepData.id, true, preferences);
    }

    if (currentStep < onboardingSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      await handleComplete();
    }
  };

  const handleComplete = async () => {
    setLoading(true);
    
    try {
      if (user) {
        // Save user preferences
        await userService.updateUserPreferences(user.id, preferences);
        
        // Mark onboarding as complete
        await userService.updateOnboardingStep(user.id, 'onboarding_complete', true);
        
        // Track completion
        await analyticsService.trackConversion('onboarding_completed', 1, {
          total_steps: onboardingSteps.length,
          preferences_set: Object.keys(preferences).length,
        });
      }
      
      onComplete();
    } catch (error) {
      console.error('Error completing onboarding:', error);
    } finally {
      setLoading(false);
    }
  };

  const CurrentStepComponent = onboardingSteps[currentStep].component;

  return (
    <View style={styles.container}>
      {/* Progress Bar */}
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View 
            style={[
              styles.progressFill, 
              { width: `${((currentStep + 1) / onboardingSteps.length) * 100}%` }
            ]} 
          />
        </View>
        <Text style={styles.progressText}>
          {currentStep + 1} of {onboardingSteps.length}
        </Text>
      </View>

      {/* Step Content */}
      <Animated.View 
        key={currentStep}
        entering={FadeInRight.duration(300)}
        exiting={FadeOutLeft.duration(300)}
        style={styles.stepContainer}
      >
        <CurrentStepComponent
          preferences={preferences}
          setPreferences={setPreferences}
          onNext={handleNext}
          loading={loading}
        />
      </Animated.View>
    </View>
  );
}

function WelcomeStep({ onNext }: any) {
  return (
    <ScrollView style={styles.stepContent} showsVerticalScrollIndicator={false}>
      <View style={styles.welcomeContainer}>
        <Heart size={80} color="#B76E79" style={styles.welcomeIcon} />
        <Text style={styles.stepTitle}>Welcome to HourlyGF</Text>
        <Text style={styles.stepSubtitle}>Your premium companion booking platform</Text>
        
        <View style={styles.featuresContainer}>
          <View style={styles.feature}>
            <Shield size={24} color="#10B981" />
            <Text style={styles.featureText}>Verified companions</Text>
          </View>
          <View style={styles.feature}>
            <MapPin size={24} color="#3B82F6" />
            <Text style={styles.featureText}>Location-based matching</Text>
          </View>
          <View style={styles.feature}>
            <Bell size={24} color="#F59E0B" />
            <Text style={styles.featureText}>Real-time notifications</Text>
          </View>
        </View>
        
        <Text style={styles.welcomeDescription}>
          Let's set up your profile to help you find the perfect companion matches. 
          This will only take a few minutes.
        </Text>
      </View>
      
      <Button title="Get Started" onPress={onNext} style={styles.nextButton} />
    </ScrollView>
  );
}

function PreferencesStep({ preferences, setPreferences, onNext }: any) {
  return (
    <ScrollView style={styles.stepContent} showsVerticalScrollIndicator={false}>
      <Text style={styles.stepTitle}>Set Your Preferences</Text>
      <Text style={styles.stepSubtitle}>Help us find the perfect matches for you</Text>
      
      <View style={styles.preferenceSection}>
        <Text style={styles.sectionTitle}>Age Range</Text>
        <View style={styles.rangeContainer}>
          <View style={styles.rangeInput}>
            <Input
              label="Min Age"
              value={preferences.ageRangeMin.toString()}
              onChangeText={(text) => setPreferences({
                ...preferences,
                ageRangeMin: parseInt(text) || 18
              })}
              keyboardType="numeric"
            />
          </View>
          <View style={styles.rangeInput}>
            <Input
              label="Max Age"
              value={preferences.ageRangeMax.toString()}
              onChangeText={(text) => setPreferences({
                ...preferences,
                ageRangeMax: parseInt(text) || 65
              })}
              keyboardType="numeric"
            />
          </View>
        </View>
      </View>

      <View style={styles.preferenceSection}>
        <Text style={styles.sectionTitle}>Price Range (per hour)</Text>
        <View style={styles.rangeContainer}>
          <View style={styles.rangeInput}>
            <Input
              label="Min Price"
              value={preferences.priceRangeMin.toString()}
              onChangeText={(text) => setPreferences({
                ...preferences,
                priceRangeMin: parseInt(text) || 0
              })}
              keyboardType="numeric"
            />
          </View>
          <View style={styles.rangeInput}>
            <Input
              label="Max Price"
              value={preferences.priceRangeMax.toString()}
              onChangeText={(text) => setPreferences({
                ...preferences,
                priceRangeMax: parseInt(text) || 1000
              })}
              keyboardType="numeric"
            />
          </View>
        </View>
      </View>

      <View style={styles.preferenceSection}>
        <Text style={styles.sectionTitle}>Maximum Distance</Text>
        <View style={styles.distanceOptions}>
          {[10, 25, 50, 100].map((distance) => (
            <TouchableOpacity
              key={distance}
              style={[
                styles.distanceOption,
                preferences.maxDistanceKm === distance && styles.distanceOptionSelected
              ]}
              onPress={() => setPreferences({
                ...preferences,
                maxDistanceKm: distance
              })}
            >
              <Text style={[
                styles.distanceOptionText,
                preferences.maxDistanceKm === distance && styles.distanceOptionTextSelected
              ]}>
                {distance} km
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
      
      <Button title="Continue" onPress={onNext} style={styles.nextButton} />
    </ScrollView>
  );
}

function InterestsStep({ preferences, setPreferences, onNext }: any) {
  const interests = [
    'Dining', 'Movies', 'Theater', 'Museums', 'Art Galleries', 'Concerts',
    'Sports Events', 'Outdoor Activities', 'Travel', 'Shopping', 'Nightlife',
    'Cultural Events', 'Business Events', 'Social Gatherings', 'Fitness',
    'Cooking', 'Wine Tasting', 'Dancing', 'Photography', 'Reading'
  ];

  const toggleInterest = (interest: string) => {
    const currentInterests = preferences.preferredInterests || [];
    const newInterests = currentInterests.includes(interest)
      ? currentInterests.filter((i: string) => i !== interest)
      : [...currentInterests, interest];
    
    setPreferences({
      ...preferences,
      preferredInterests: newInterests
    });
  };

  return (
    <ScrollView style={styles.stepContent} showsVerticalScrollIndicator={false}>
      <Text style={styles.stepTitle}>Choose Your Interests</Text>
      <Text style={styles.stepSubtitle}>What activities do you enjoy?</Text>
      
      <View style={styles.interestsGrid}>
        {interests.map((interest) => (
          <TouchableOpacity
            key={interest}
            style={[
              styles.interestChip,
              preferences.preferredInterests?.includes(interest) && styles.interestChipSelected
            ]}
            onPress={() => toggleInterest(interest)}
          >
            <Text style={[
              styles.interestChipText,
              preferences.preferredInterests?.includes(interest) && styles.interestChipTextSelected
            ]}>
              {interest}
            </Text>
            {preferences.preferredInterests?.includes(interest) && (
              <Check size={16} color="#FFFFFF" style={styles.interestCheck} />
            )}
          </TouchableOpacity>
        ))}
      </View>
      
      <Button 
        title="Continue" 
        onPress={onNext} 
        style={styles.nextButton}
        disabled={!preferences.preferredInterests?.length}
      />
    </ScrollView>
  );
}

function NotificationsStep({ preferences, setPreferences, onNext }: any) {
  const updateNotificationPreference = (key: string, value: boolean) => {
    setPreferences({
      ...preferences,
      notificationPreferences: {
        ...preferences.notificationPreferences,
        [key]: value
      }
    });
  };

  return (
    <ScrollView style={styles.stepContent} showsVerticalScrollIndicator={false}>
      <Text style={styles.stepTitle}>Stay Connected</Text>
      <Text style={styles.stepSubtitle}>Choose how you want to be notified</Text>
      
      <View style={styles.notificationOptions}>
        <View style={styles.notificationOption}>
          <View style={styles.notificationInfo}>
            <Text style={styles.notificationTitle}>New Messages</Text>
            <Text style={styles.notificationDescription}>
              Get notified when companions send you messages
            </Text>
          </View>
          <TouchableOpacity
            style={[
              styles.toggle,
              preferences.notificationPreferences?.messages && styles.toggleActive
            ]}
            onPress={() => updateNotificationPreference('messages', !preferences.notificationPreferences?.messages)}
          >
            <View style={[
              styles.toggleThumb,
              preferences.notificationPreferences?.messages && styles.toggleThumbActive
            ]} />
          </TouchableOpacity>
        </View>

        <View style={styles.notificationOption}>
          <View style={styles.notificationInfo}>
            <Text style={styles.notificationTitle}>Booking Updates</Text>
            <Text style={styles.notificationDescription}>
              Get notified about booking confirmations and changes
            </Text>
          </View>
          <TouchableOpacity
            style={[
              styles.toggle,
              preferences.notificationPreferences?.bookings && styles.toggleActive
            ]}
            onPress={() => updateNotificationPreference('bookings', !preferences.notificationPreferences?.bookings)}
          >
            <View style={[
              styles.toggleThumb,
              preferences.notificationPreferences?.bookings && styles.toggleThumbActive
            ]} />
          </TouchableOpacity>
        </View>

        <View style={styles.notificationOption}>
          <View style={styles.notificationInfo}>
            <Text style={styles.notificationTitle}>Marketing & Promotions</Text>
            <Text style={styles.notificationDescription}>
              Receive special offers and app updates
            </Text>
          </View>
          <TouchableOpacity
            style={[
              styles.toggle,
              preferences.notificationPreferences?.marketing && styles.toggleActive
            ]}
            onPress={() => updateNotificationPreference('marketing', !preferences.notificationPreferences?.marketing)}
          >
            <View style={[
              styles.toggleThumb,
              preferences.notificationPreferences?.marketing && styles.toggleThumbActive
            ]} />
          </TouchableOpacity>
        </View>
      </View>
      
      <Button title="Continue" onPress={onNext} style={styles.nextButton} />
    </ScrollView>
  );
}

function LocationStep({ onNext }: any) {
  return (
    <ScrollView style={styles.stepContent} showsVerticalScrollIndicator={false}>
      <Text style={styles.stepTitle}>Enable Location</Text>
      <Text style={styles.stepSubtitle}>Find companions near you</Text>
      
      <View style={styles.locationContainer}>
        <MapPin size={80} color="#B76E79" style={styles.locationIcon} />
        
        <View style={styles.locationBenefits}>
          <View style={styles.locationBenefit}>
            <Check size={20} color="#10B981" />
            <Text style={styles.locationBenefitText}>
              Discover companions in your area
            </Text>
          </View>
          <View style={styles.locationBenefit}>
            <Check size={20} color="#10B981" />
            <Text style={styles.locationBenefitText}>
              Get accurate distance estimates
            </Text>
          </View>
          <View style={styles.locationBenefit}>
            <Check size={20} color="#10B981" />
            <Text style={styles.locationBenefitText}>
              Enhanced safety features
            </Text>
          </View>
        </View>
        
        <Text style={styles.locationPrivacy}>
          Your location is encrypted and only shared with your consent. 
          You can disable this at any time in settings.
        </Text>
      </View>
      
      <Button title="Enable Location" onPress={onNext} style={styles.nextButton} />
      <TouchableOpacity onPress={onNext} style={styles.skipButton}>
        <Text style={styles.skipButtonText}>Skip for now</Text>
      </TouchableOpacity>
    </ScrollView>
  );
}

function CompleteStep({ onNext, loading }: any) {
  return (
    <ScrollView style={styles.stepContent} showsVerticalScrollIndicator={false}>
      <View style={styles.completeContainer}>
        <View style={styles.successIcon}>
          <Check size={60} color="#FFFFFF" />
        </View>
        
        <Text style={styles.stepTitle}>You're All Set!</Text>
        <Text style={styles.stepSubtitle}>Start discovering amazing companions</Text>
        
        <View style={styles.nextSteps}>
          <View style={styles.nextStep}>
            <ChevronRight size={20} color="#B76E79" />
            <Text style={styles.nextStepText}>Browse verified companions</Text>
          </View>
          <View style={styles.nextStep}>
            <ChevronRight size={20} color="#B76E79" />
            <Text style={styles.nextStepText}>Book your first date</Text>
          </View>
          <View style={styles.nextStep}>
            <ChevronRight size={20} color="#B76E79" />
            <Text style={styles.nextStepText}>Enjoy premium experiences</Text>
          </View>
        </View>
      </View>
      
      <Button 
        title={loading ? "Setting up..." : "Start Exploring"} 
        onPress={onNext} 
        style={styles.nextButton}
        disabled={loading}
      />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F3E9',
  },
  progressContainer: {
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 16,
  },
  progressBar: {
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#B76E79',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
  },
  stepContainer: {
    flex: 1,
  },
  stepContent: {
    flex: 1,
    paddingHorizontal: 24,
  },
  stepTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1A2E4C',
    textAlign: 'center',
    marginBottom: 8,
  },
  stepSubtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 32,
  },
  welcomeContainer: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  welcomeIcon: {
    marginBottom: 24,
  },
  featuresContainer: {
    marginVertical: 32,
    gap: 16,
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  featureText: {
    fontSize: 16,
    color: '#4B5563',
    marginLeft: 12,
  },
  welcomeDescription: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
    marginTop: 24,
  },
  preferenceSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 12,
  },
  rangeContainer: {
    flexDirection: 'row',
    gap: 16,
  },
  rangeInput: {
    flex: 1,
  },
  distanceOptions: {
    flexDirection: 'row',
    gap: 12,
  },
  distanceOption: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    alignItems: 'center',
  },
  distanceOptionSelected: {
    borderColor: '#B76E79',
    backgroundColor: '#FDF2F8',
  },
  distanceOptionText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  distanceOptionTextSelected: {
    color: '#B76E79',
  },
  interestsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 32,
  },
  interestChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    flexDirection: 'row',
    alignItems: 'center',
  },
  interestChipSelected: {
    borderColor: '#B76E79',
    backgroundColor: '#B76E79',
  },
  interestChipText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  interestChipTextSelected: {
    color: '#FFFFFF',
  },
  interestCheck: {
    marginLeft: 4,
  },
  notificationOptions: {
    gap: 20,
    marginBottom: 32,
  },
  notificationOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
  },
  notificationInfo: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 4,
  },
  notificationDescription: {
    fontSize: 14,
    color: '#6B7280',
  },
  toggle: {
    width: 50,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#E5E7EB',
    padding: 2,
    justifyContent: 'center',
  },
  toggleActive: {
    backgroundColor: '#B76E79',
  },
  toggleThumb: {
    width: 26,
    height: 26,
    borderRadius: 13,
    backgroundColor: '#FFFFFF',
    alignSelf: 'flex-start',
  },
  toggleThumbActive: {
    alignSelf: 'flex-end',
  },
  locationContainer: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  locationIcon: {
    marginBottom: 32,
  },
  locationBenefits: {
    gap: 16,
    marginBottom: 32,
  },
  locationBenefit: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationBenefitText: {
    fontSize: 16,
    color: '#4B5563',
    marginLeft: 12,
  },
  locationPrivacy: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
  },
  completeContainer: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  successIcon: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#10B981',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 32,
  },
  nextSteps: {
    gap: 16,
    marginTop: 32,
  },
  nextStep: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  nextStepText: {
    fontSize: 16,
    color: '#4B5563',
    marginLeft: 12,
  },
  nextButton: {
    marginTop: 32,
    marginBottom: 16,
  },
  skipButton: {
    paddingVertical: 12,
    alignItems: 'center',
  },
  skipButtonText: {
    fontSize: 16,
    color: '#6B7280',
  },
});