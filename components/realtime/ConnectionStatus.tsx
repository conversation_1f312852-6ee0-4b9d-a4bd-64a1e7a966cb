import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Wifi, WifiOff, RefreshCw } from 'lucide-react-native';
import Animated, { 
  useAnimatedStyle, 
  useSharedValue, 
  withRepeat, 
  withTiming,
  interpolate
} from 'react-native-reanimated';

interface ConnectionStatusProps {
  isConnected: boolean;
  isReconnecting?: boolean;
  onRetry?: () => void;
  style?: any;
}

export function ConnectionStatus({ 
  isConnected, 
  isReconnecting = false, 
  onRetry,
  style 
}: ConnectionStatusProps) {
  const pulseAnimation = useSharedValue(0);
  const rotateAnimation = useSharedValue(0);

  React.useEffect(() => {
    if (isReconnecting) {
      rotateAnimation.value = withRepeat(
        withTiming(360, { duration: 1000 }),
        -1,
        false
      );
    } else {
      rotateAnimation.value = withTiming(0);
    }
  }, [isReconnecting]);

  React.useEffect(() => {
    if (!isConnected && !isReconnecting) {
      pulseAnimation.value = withRepeat(
        withTiming(1, { duration: 1000 }),
        -1,
        true
      );
    } else {
      pulseAnimation.value = withTiming(0);
    }
  }, [isConnected, isReconnecting]);

  const pulseStyle = useAnimatedStyle(() => {
    const opacity = interpolate(pulseAnimation.value, [0, 1], [0.3, 1]);
    return {
      opacity,
    };
  });

  const rotateStyle = useAnimatedStyle(() => {
    return {
      transform: [{ rotate: `${rotateAnimation.value}deg` }],
    };
  });

  if (isConnected) {
    return null; // Don't show anything when connected
  }

  return (
    <Animated.View style={[styles.container, style, pulseStyle]}>
      <View style={styles.content}>
        <View style={styles.iconContainer}>
          {isReconnecting ? (
            <Animated.View style={rotateStyle}>
              <RefreshCw size={16} color="#F59E0B" />
            </Animated.View>
          ) : (
            <WifiOff size={16} color="#EF4444" />
          )}
        </View>
        
        <Text style={styles.text}>
          {isReconnecting ? 'Reconnecting...' : 'Connection lost'}
        </Text>
        
        {!isReconnecting && onRetry && (
          <TouchableOpacity style={styles.retryButton} onPress={onRetry}>
            <Text style={styles.retryText}>Retry</Text>
          </TouchableOpacity>
        )}
      </View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FEF3C7',
    borderRadius: 8,
    padding: 12,
    marginHorizontal: 16,
    marginVertical: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#F59E0B',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    marginRight: 8,
  },
  text: {
    flex: 1,
    fontSize: 14,
    color: '#92400E',
    fontWeight: '500',
  },
  retryButton: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    backgroundColor: '#F59E0B',
    borderRadius: 4,
  },
  retryText: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: '600',
  },
});