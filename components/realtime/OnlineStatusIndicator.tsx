import React from 'react';
import { View, StyleSheet } from 'react-native';
import Animated, { 
  useAnimatedStyle, 
  useSharedValue, 
  withRepeat, 
  withTiming,
  interpolate
} from 'react-native-reanimated';

interface OnlineStatusIndicatorProps {
  isOnline: boolean;
  size?: 'small' | 'medium' | 'large';
  showPulse?: boolean;
  style?: any;
}

export function OnlineStatusIndicator({ 
  isOnline, 
  size = 'medium', 
  showPulse = true,
  style 
}: OnlineStatusIndicatorProps) {
  const pulseAnimation = useSharedValue(0);

  React.useEffect(() => {
    if (isOnline && showPulse) {
      pulseAnimation.value = withRepeat(
        withTiming(1, { duration: 2000 }),
        -1,
        true
      );
    } else {
      pulseAnimation.value = withTiming(0);
    }
  }, [isOnline, showPulse]);

  const getSizeConfig = () => {
    switch (size) {
      case 'small':
        return { indicator: 8, pulse: 16 };
      case 'large':
        return { indicator: 16, pulse: 32 };
      default:
        return { indicator: 12, pulse: 24 };
    }
  };

  const sizeConfig = getSizeConfig();

  const pulseStyle = useAnimatedStyle(() => {
    const scale = interpolate(pulseAnimation.value, [0, 1], [1, 1.5]);
    const opacity = interpolate(pulseAnimation.value, [0, 1], [0.7, 0]);
    
    return {
      transform: [{ scale }],
      opacity: isOnline ? opacity : 0,
    };
  });

  const indicatorStyle = useAnimatedStyle(() => {
    const scale = interpolate(pulseAnimation.value, [0, 1], [1, 1.1]);
    
    return {
      transform: [{ scale: isOnline ? scale : 1 }],
    };
  });

  return (
    <View style={[styles.container, style]}>
      {/* Pulse effect */}
      <Animated.View
        style={[
          styles.pulse,
          {
            width: sizeConfig.pulse,
            height: sizeConfig.pulse,
            borderRadius: sizeConfig.pulse / 2,
            backgroundColor: isOnline ? '#10B981' : 'transparent',
          },
          pulseStyle,
        ]}
      />
      
      {/* Main indicator */}
      <Animated.View
        style={[
          styles.indicator,
          {
            width: sizeConfig.indicator,
            height: sizeConfig.indicator,
            borderRadius: sizeConfig.indicator / 2,
            backgroundColor: isOnline ? '#10B981' : '#6B7280',
          },
          indicatorStyle,
        ]}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  pulse: {
    position: 'absolute',
  },
  indicator: {
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
});