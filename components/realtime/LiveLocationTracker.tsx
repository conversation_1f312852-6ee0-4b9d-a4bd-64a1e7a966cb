import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { MapPin, Navigation, Share, Eye, EyeOff } from 'lucide-react-native';
import { LocationService } from '@/lib/location/location-service';
import { RealtimeService } from '@/lib/realtime/realtime-service';
import { useSupabase } from '@/lib/supabase/supabase-provider';

interface LiveLocationTrackerProps {
  bookingId?: string;
  companionId?: string;
  isSharing: boolean;
  onSharingChange: (sharing: boolean) => void;
}

export function LiveLocationTracker({ 
  bookingId, 
  companionId, 
  isSharing, 
  onSharingChange 
}: LiveLocationTrackerProps) {
  const [currentLocation, setCurrentLocation] = useState<any>(null);
  const [isTracking, setIsTracking] = useState(false);
  const [accuracy, setAccuracy] = useState<number | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const { user } = useSupabase();
  
  const locationService = LocationService.getInstance();
  const realtimeService = RealtimeService.getInstance();

  useEffect(() => {
    if (isSharing && user) {
      startLocationSharing();
    } else {
      stopLocationSharing();
    }

    return () => {
      stopLocationSharing();
    };
  }, [isSharing, user]);

  const startLocationSharing = async () => {
    try {
      const permissions = await locationService.requestPermissions();
      if (!permissions.granted) {
        Alert.alert(
          'Location Permission Required',
          'Please enable location access to share your location during bookings.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Settings', onPress: () => {/* Open settings */} },
          ]
        );
        onSharingChange(false);
        return;
      }

      setIsTracking(true);

      // Start real-time location tracking
      const success = await locationService.startLocationTracking(
        (location) => {
          setCurrentLocation(location);
          setAccuracy(location.accuracy || null);
          setLastUpdate(new Date());

          // Share location via real-time service
          if (user) {
            realtimeService.shareLocation(user.id, {
              userId: user.id,
              latitude: location.latitude,
              longitude: location.longitude,
              accuracy: location.accuracy,
              timestamp: location.timestamp,
              isSharing: true,
            });
          }
        },
        {
          timeInterval: 10000, // Update every 10 seconds
          distanceInterval: 10, // Update every 10 meters
        }
      );

      if (!success) {
        throw new Error('Failed to start location tracking');
      }

    } catch (error) {
      console.error('Error starting location sharing:', error);
      Alert.alert('Error', 'Failed to start location sharing');
      onSharingChange(false);
      setIsTracking(false);
    }
  };

  const stopLocationSharing = async () => {
    try {
      await locationService.stopLocationTracking();
      setIsTracking(false);
      setCurrentLocation(null);
      setAccuracy(null);
      setLastUpdate(null);

      // Stop sharing location
      if (user) {
        realtimeService.shareLocation(user.id, {
          userId: user.id,
          latitude: 0,
          longitude: 0,
          timestamp: Date.now(),
          isSharing: false,
        });
      }
    } catch (error) {
      console.error('Error stopping location sharing:', error);
    }
  };

  const toggleLocationSharing = () => {
    if (isSharing) {
      Alert.alert(
        'Stop Location Sharing',
        'Are you sure you want to stop sharing your location? This may affect safety features.',
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Stop Sharing', 
            style: 'destructive',
            onPress: () => onSharingChange(false)
          },
        ]
      );
    } else {
      onSharingChange(true);
    }
  };

  const shareCurrentLocation = async () => {
    if (!currentLocation) {
      Alert.alert('No Location', 'Current location is not available');
      return;
    }

    try {
      const locationUrl = `https://maps.google.com/?q=${currentLocation.latitude},${currentLocation.longitude}`;
      
      // This would integrate with your sharing mechanism
      Alert.alert(
        'Share Location',
        `Share your current location?\n\nLat: ${currentLocation.latitude.toFixed(6)}\nLng: ${currentLocation.longitude.toFixed(6)}`,
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Share', onPress: () => console.log('Share:', locationUrl) },
        ]
      );
    } catch (error) {
      console.error('Error sharing location:', error);
    }
  };

  const formatAccuracy = (acc: number | null): string => {
    if (!acc) return 'Unknown';
    if (acc < 10) return 'High';
    if (acc < 50) return 'Good';
    if (acc < 100) return 'Fair';
    return 'Low';
  };

  const formatLastUpdate = (date: Date | null): string => {
    if (!date) return 'Never';
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const seconds = Math.floor(diff / 1000);
    
    if (seconds < 60) return `${seconds}s ago`;
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    return `${hours}h ago`;
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <MapPin size={20} color="#B76E79" />
          <Text style={styles.title}>Live Location</Text>
        </View>
        
        <TouchableOpacity
          style={[
            styles.toggleButton,
            isSharing && styles.toggleButtonActive
          ]}
          onPress={toggleLocationSharing}
        >
          {isSharing ? (
            <Eye size={16} color="#FFFFFF" />
          ) : (
            <EyeOff size={16} color="#6B7280" />
          )}
          <Text style={[
            styles.toggleText,
            isSharing && styles.toggleTextActive
          ]}>
            {isSharing ? 'Sharing' : 'Share'}
          </Text>
        </TouchableOpacity>
      </View>

      {isSharing && (
        <View style={styles.content}>
          <View style={styles.statusContainer}>
            <View style={[
              styles.statusIndicator,
              { backgroundColor: isTracking ? '#10B981' : '#EF4444' }
            ]} />
            <Text style={styles.statusText}>
              {isTracking ? 'Location sharing active' : 'Location sharing inactive'}
            </Text>
          </View>

          {currentLocation && (
            <View style={styles.locationInfo}>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Coordinates:</Text>
                <Text style={styles.infoValue}>
                  {currentLocation.latitude.toFixed(6)}, {currentLocation.longitude.toFixed(6)}
                </Text>
              </View>
              
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Accuracy:</Text>
                <Text style={styles.infoValue}>
                  {formatAccuracy(accuracy)} {accuracy && `(±${Math.round(accuracy)}m)`}
                </Text>
              </View>
              
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Last Update:</Text>
                <Text style={styles.infoValue}>
                  {formatLastUpdate(lastUpdate)}
                </Text>
              </View>
            </View>
          )}

          <View style={styles.actions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={shareCurrentLocation}
              disabled={!currentLocation}
            >
              <Share size={16} color="#3B82F6" />
              <Text style={styles.actionText}>Share Location</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => {/* Open in maps */}}
              disabled={!currentLocation}
            >
              <Navigation size={16} color="#10B981" />
              <Text style={styles.actionText}>Open in Maps</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {!isSharing && (
        <View style={styles.inactiveContent}>
          <Text style={styles.inactiveText}>
            Location sharing is disabled. Enable it to share your location during bookings for safety.
          </Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    marginLeft: 8,
  },
  toggleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  toggleButtonActive: {
    backgroundColor: '#B76E79',
    borderColor: '#B76E79',
  },
  toggleText: {
    fontSize: 14,
    color: '#6B7280',
    marginLeft: 4,
    fontWeight: '500',
  },
  toggleTextActive: {
    color: '#FFFFFF',
  },
  content: {
    gap: 12,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  statusText: {
    fontSize: 14,
    color: '#4B5563',
  },
  locationInfo: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 12,
    gap: 8,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  infoLabel: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  infoValue: {
    fontSize: 14,
    color: '#1A2E4C',
    fontWeight: '400',
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
  },
  actionText: {
    fontSize: 14,
    color: '#4B5563',
    marginLeft: 6,
    fontWeight: '500',
  },
  inactiveContent: {
    padding: 12,
    backgroundColor: '#FEF3C7',
    borderRadius: 8,
  },
  inactiveText: {
    fontSize: 14,
    color: '#92400E',
    textAlign: 'center',
    lineHeight: 20,
  },
});