import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Animated, { 
  useAnimatedStyle, 
  useSharedValue, 
  withRepeat, 
  withTiming,
  interpolate,
  Easing
} from 'react-native-reanimated';

interface TypingIndicatorProps {
  isVisible: boolean;
  userName?: string;
  style?: any;
}

export function TypingIndicator({ isVisible, userName, style }: TypingIndicatorProps) {
  const [showIndicator, setShowIndicator] = useState(false);
  const animationValue = useSharedValue(0);

  useEffect(() => {
    if (isVisible) {
      setShowIndicator(true);
      animationValue.value = withRepeat(
        withTiming(1, {
          duration: 1500,
          easing: Easing.inOut(Easing.ease),
        }),
        -1,
        true
      );
    } else {
      animationValue.value = withTiming(0, {
        duration: 300,
      });
      setTimeout(() => setShowIndicator(false), 300);
    }
  }, [isVisible]);

  const animatedStyle = useAnimatedStyle(() => {
    const opacity = interpolate(animationValue.value, [0, 1], [0.3, 1]);
    return {
      opacity,
    };
  });

  const dot1Style = useAnimatedStyle(() => {
    const translateY = interpolate(
      animationValue.value,
      [0, 0.33, 0.66, 1],
      [0, -8, 0, 0]
    );
    return {
      transform: [{ translateY }],
    };
  });

  const dot2Style = useAnimatedStyle(() => {
    const translateY = interpolate(
      animationValue.value,
      [0, 0.33, 0.66, 1],
      [0, 0, -8, 0]
    );
    return {
      transform: [{ translateY }],
    };
  });

  const dot3Style = useAnimatedStyle(() => {
    const translateY = interpolate(
      animationValue.value,
      [0, 0.33, 0.66, 1],
      [0, 0, 0, -8]
    );
    return {
      transform: [{ translateY }],
    };
  });

  if (!showIndicator) return null;

  return (
    <Animated.View style={[styles.container, animatedStyle, style]}>
      <View style={styles.bubble}>
        <View style={styles.dotsContainer}>
          <Animated.View style={[styles.dot, dot1Style]} />
          <Animated.View style={[styles.dot, dot2Style]} />
          <Animated.View style={[styles.dot, dot3Style]} />
        </View>
      </View>
      {userName && (
        <Text style={styles.userName}>{userName} is typing...</Text>
      )}
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignSelf: 'flex-start',
    marginVertical: 4,
    marginHorizontal: 16,
  },
  bubble: {
    backgroundColor: '#F3F4F6',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 8,
    maxWidth: 80,
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 20,
  },
  dot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#6B7280',
    marginHorizontal: 2,
  },
  userName: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
    marginLeft: 4,
  },
});