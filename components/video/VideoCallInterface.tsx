import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Dimensions } from 'react-native';
import { Video, VideoOff, Mic, MicOff, PhoneOff, Monitor, MonitorOff, Settings } from 'lucide-react-native';
import { WebRTCService } from '@/lib/webrtc/webrtc-service';
import { Platform } from 'react-native';

const { width, height } = Dimensions.get('window');

interface VideoCallInterfaceProps {
  callId: string;
  isHost: boolean;
  onEndCall: () => void;
  onToggleVideo: (enabled: boolean) => void;
  onToggleAudio: (enabled: boolean) => void;
  onToggleScreenShare: (enabled: boolean) => void;
}

export function VideoCallInterface({
  callId,
  isHost,
  onEndCall,
  onToggleVideo,
  onToggleAudio,
  onToggleScreenShare,
}: VideoCallInterfaceProps) {
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [connectionQuality, setConnectionQuality] = useState<'poor' | 'fair' | 'good' | 'excellent'>('good');
  const [callDuration, setCallDuration] = useState(0);
  const [showControls, setShowControls] = useState(true);

  const localVideoRef = useRef<HTMLVideoElement>(null);
  const remoteVideoRef = useRef<HTMLVideoElement>(null);
  const webrtcService = WebRTCService.getInstance();
  const controlsTimeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    initializeCall();
    startCallTimer();

    return () => {
      cleanup();
    };
  }, []);

  useEffect(() => {
    // Auto-hide controls after 5 seconds
    if (showControls) {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
      controlsTimeoutRef.current = setTimeout(() => {
        setShowControls(false);
      }, 5000);
    }

    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
    };
  }, [showControls]);

  const initializeCall = async () => {
    if (Platform.OS !== 'web') return;

    try {
      await webrtcService.initialize();
      const stream = await webrtcService.getUserMedia();
      
      if (localVideoRef.current) {
        localVideoRef.current.srcObject = stream;
      }

      await webrtcService.createPeerConnection();
      
      // Monitor connection quality
      const qualityInterval = setInterval(async () => {
        const quality = webrtcService.getConnectionQuality();
        setConnectionQuality(quality);
      }, 2000);

      return () => clearInterval(qualityInterval);
    } catch (error) {
      console.error('Error initializing call:', error);
    }
  };

  const startCallTimer = () => {
    const startTime = Date.now();
    const timer = setInterval(() => {
      setCallDuration(Math.floor((Date.now() - startTime) / 1000));
    }, 1000);

    return () => clearInterval(timer);
  };

  const handleToggleVideo = async () => {
    const enabled = webrtcService.toggleVideo();
    setIsVideoEnabled(enabled);
    onToggleVideo(enabled);
  };

  const handleToggleAudio = async () => {
    const enabled = webrtcService.toggleAudio();
    setIsAudioEnabled(enabled);
    onToggleAudio(enabled);
  };

  const handleToggleScreenShare = async () => {
    try {
      if (isScreenSharing) {
        await webrtcService.stopScreenShare();
        setIsScreenSharing(false);
        onToggleScreenShare(false);
      } else {
        await webrtcService.startScreenShare();
        setIsScreenSharing(true);
        onToggleScreenShare(true);
      }
    } catch (error) {
      console.error('Error toggling screen share:', error);
    }
  };

  const handleEndCall = () => {
    webrtcService.endCall();
    onEndCall();
  };

  const cleanup = () => {
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }
  };

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getQualityColor = () => {
    switch (connectionQuality) {
      case 'excellent': return '#10B981';
      case 'good': return '#3B82F6';
      case 'fair': return '#F59E0B';
      case 'poor': return '#EF4444';
      default: return '#6B7280';
    }
  };

  if (Platform.OS !== 'web') {
    return (
      <View style={styles.unsupportedContainer}>
        <Text style={styles.unsupportedText}>
          Video calls are not supported on this platform yet.
        </Text>
        <Text style={styles.unsupportedSubtext}>
          Please use the web version for video calling.
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Remote Video */}
      <View style={styles.remoteVideoContainer}>
        <video
          ref={remoteVideoRef}
          style={styles.remoteVideo}
          autoPlay
          playsInline
        />
        
        {/* Connection Quality Indicator */}
        <View style={[styles.qualityIndicator, { backgroundColor: getQualityColor() }]}>
          <Text style={styles.qualityText}>
            {connectionQuality.toUpperCase()}
          </Text>
        </View>

        {/* Call Duration */}
        <View style={styles.durationContainer}>
          <Text style={styles.durationText}>
            {formatDuration(callDuration)}
          </Text>
        </View>
      </View>

      {/* Local Video */}
      <View style={styles.localVideoContainer}>
        <video
          ref={localVideoRef}
          style={styles.localVideo}
          autoPlay
          playsInline
          muted
        />
        {!isVideoEnabled && (
          <View style={styles.videoOffOverlay}>
            <VideoOff size={24} color="#FFFFFF" />
          </View>
        )}
      </View>

      {/* Controls */}
      {showControls && (
        <View style={styles.controlsContainer}>
          <View style={styles.controls}>
            <TouchableOpacity
              style={[styles.controlButton, !isAudioEnabled && styles.controlButtonMuted]}
              onPress={handleToggleAudio}
            >
              {isAudioEnabled ? (
                <Mic size={24} color="#FFFFFF" />
              ) : (
                <MicOff size={24} color="#FFFFFF" />
              )}
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.controlButton, !isVideoEnabled && styles.controlButtonMuted]}
              onPress={handleToggleVideo}
            >
              {isVideoEnabled ? (
                <Video size={24} color="#FFFFFF" />
              ) : (
                <VideoOff size={24} color="#FFFFFF" />
              )}
            </TouchableOpacity>

            {Platform.OS === 'web' && (
              <TouchableOpacity
                style={[styles.controlButton, isScreenSharing && styles.controlButtonActive]}
                onPress={handleToggleScreenShare}
              >
                {isScreenSharing ? (
                  <MonitorOff size={24} color="#FFFFFF" />
                ) : (
                  <Monitor size={24} color="#FFFFFF" />
                )}
              </TouchableOpacity>
            )}

            <TouchableOpacity
              style={styles.settingsButton}
              onPress={() => {/* Open settings */}}
            >
              <Settings size={24} color="#FFFFFF" />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.endCallButton}
              onPress={handleEndCall}
            >
              <PhoneOff size={24} color="#FFFFFF" />
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Tap to show controls */}
      <TouchableOpacity
        style={styles.tapOverlay}
        onPress={() => setShowControls(true)}
        activeOpacity={1}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
    position: 'relative',
  },
  unsupportedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1A2E4C',
    padding: 24,
  },
  unsupportedText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 8,
  },
  unsupportedSubtext: {
    fontSize: 14,
    color: '#B0BEC5',
    textAlign: 'center',
  },
  remoteVideoContainer: {
    flex: 1,
    position: 'relative',
  },
  remoteVideo: {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
  },
  qualityIndicator: {
    position: 'absolute',
    top: 16,
    left: 16,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  qualityText: {
    fontSize: 10,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  durationContainer: {
    position: 'absolute',
    top: 16,
    right: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  durationText: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  localVideoContainer: {
    position: 'absolute',
    top: 60,
    right: 16,
    width: 120,
    height: 160,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: '#1A2E4C',
  },
  localVideo: {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
  },
  videoOffOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  controlsContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingBottom: 40,
    paddingHorizontal: 24,
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderRadius: 32,
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  controlButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 8,
  },
  controlButtonMuted: {
    backgroundColor: '#EF4444',
  },
  controlButtonActive: {
    backgroundColor: '#3B82F6',
  },
  settingsButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 8,
  },
  endCallButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#EF4444',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 16,
  },
  tapOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
});