import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { format } from 'date-fns';
import { ArrowUpRight, ArrowDownLeft, RefreshCw, AlertCircle } from 'lucide-react-native';
import { formatCurrency } from '@/lib/utils/formatters';

interface TransactionItemProps {
  transaction: {
    id: string;
    type: string;
    status: string;
    amount: number;
    currency: string;
    description?: string;
    created_at: string;
    booking?: {
      id: string;
      companion: {
        profiles: {
          full_name: string;
        };
      };
    };
  };
}

export function TransactionItem({ transaction }: TransactionItemProps) {
  const getTransactionIcon = () => {
    switch (transaction.type) {
      case 'booking_payment':
        return <ArrowUpRight size={20} color="#EF4444" />;
      case 'refund':
        return <ArrowDownLeft size={20} color="#10B981" />;
      case 'subscription_payment':
        return <RefreshCw size={20} color="#3B82F6" />;
      default:
        return <AlertCircle size={20} color="#6B7280" />;
    }
  };

  const getStatusColor = () => {
    switch (transaction.status) {
      case 'completed':
        return '#10B981';
      case 'pending':
        return '#F59E0B';
      case 'failed':
        return '#EF4444';
      default:
        return '#6B7280';
    }
  };

  const getTransactionTitle = () => {
    switch (transaction.type) {
      case 'booking_payment':
        return transaction.booking?.companion?.profiles?.full_name 
          ? `Booking with ${transaction.booking.companion.profiles.full_name}`
          : 'Booking Payment';
      case 'subscription_payment':
        return 'Subscription Payment';
      case 'refund':
        return 'Refund';
      default:
        return transaction.description || 'Transaction';
    }
  };

  const isDebit = ['booking_payment', 'subscription_payment'].includes(transaction.type);

  return (
    <View style={styles.container}>
      <View style={styles.iconContainer}>
        {getTransactionIcon()}
      </View>
      
      <View style={styles.content}>
        <Text style={styles.title}>{getTransactionTitle()}</Text>
        <Text style={styles.date}>
          {format(new Date(transaction.created_at), 'MMM d, yyyy • h:mm a')}
        </Text>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor() + '20' }]}>
          <Text style={[styles.statusText, { color: getStatusColor() }]}>
            {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
          </Text>
        </View>
      </View>
      
      <View style={styles.amountContainer}>
        <Text style={[
          styles.amount,
          { color: isDebit ? '#EF4444' : '#10B981' }
        ]}>
          {isDebit ? '-' : '+'}{formatCurrency(transaction.amount)}
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  content: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 2,
  },
  date: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
  },
  statusBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  amountContainer: {
    alignItems: 'flex-end',
  },
  amount: {
    fontSize: 18,
    fontWeight: '700',
  },
});