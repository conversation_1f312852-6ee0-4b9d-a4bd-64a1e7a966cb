import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { CreditCard, Trash2, Check } from 'lucide-react-native';

interface PaymentMethodCardProps {
  paymentMethod: {
    id: string;
    type: string;
    brand?: string;
    last_four?: string;
    exp_month?: number;
    exp_year?: number;
    is_default: boolean;
  };
  onDelete: (id: string) => void;
  onSetDefault: (id: string) => void;
}

export function PaymentMethodCard({ paymentMethod, onDelete, onSetDefault }: PaymentMethodCardProps) {
  const getBrandColor = (brand?: string) => {
    switch (brand?.toLowerCase()) {
      case 'visa':
        return '#1A1F71';
      case 'mastercard':
        return '#EB001B';
      case 'amex':
        return '#006FCF';
      case 'discover':
        return '#FF6000';
      default:
        return '#6B7280';
    }
  };

  const formatBrand = (brand?: string) => {
    if (!brand) return 'Card';
    return brand.charAt(0).toUpperCase() + brand.slice(1);
  };

  return (
    <View style={[
      styles.container,
      paymentMethod.is_default && styles.defaultCard
    ]}>
      <View style={styles.cardInfo}>
        <View style={[styles.cardIcon, { backgroundColor: getBrandColor(paymentMethod.brand) }]}>
          <CreditCard size={20} color="#FFFFFF" />
        </View>
        
        <View style={styles.cardDetails}>
          <Text style={styles.cardBrand}>
            {formatBrand(paymentMethod.brand)} •••• {paymentMethod.last_four}
          </Text>
          {paymentMethod.exp_month && paymentMethod.exp_year && (
            <Text style={styles.cardExpiry}>
              Expires {paymentMethod.exp_month.toString().padStart(2, '0')}/{paymentMethod.exp_year}
            </Text>
          )}
        </View>
        
        {paymentMethod.is_default && (
          <View style={styles.defaultBadge}>
            <Check size={16} color="#10B981" />
            <Text style={styles.defaultText}>Default</Text>
          </View>
        )}
      </View>
      
      <View style={styles.actions}>
        {!paymentMethod.is_default && (
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => onSetDefault(paymentMethod.id)}
          >
            <Text style={styles.setDefaultText}>Set Default</Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity
          style={[styles.actionButton, styles.deleteButton]}
          onPress={() => onDelete(paymentMethod.id)}
        >
          <Trash2 size={16} color="#EF4444" />
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  defaultCard: {
    borderWidth: 2,
    borderColor: '#10B981',
  },
  cardInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  cardIcon: {
    width: 40,
    height: 40,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  cardDetails: {
    flex: 1,
  },
  cardBrand: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 2,
  },
  cardExpiry: {
    fontSize: 14,
    color: '#6B7280',
  },
  defaultBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#D1FAE5',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  defaultText: {
    fontSize: 12,
    color: '#065F46',
    marginLeft: 4,
    fontWeight: '500',
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  actionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    marginLeft: 8,
  },
  setDefaultText: {
    fontSize: 14,
    color: '#B76E79',
    fontWeight: '500',
  },
  deleteButton: {
    backgroundColor: '#FEE2E2',
  },
});