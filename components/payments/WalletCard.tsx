import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Wallet, Plus, ArrowUpRight, ArrowDownLeft, Eye, EyeOff } from 'lucide-react-native';
import { formatCurrency } from '@/lib/utils/formatters';

interface WalletCardProps {
  balance: number;
  pendingBalance: number;
  currency: string;
  onAddFunds: () => void;
  onViewTransactions: () => void;
  showBalance: boolean;
  onToggleBalance: () => void;
}

export function WalletCard({
  balance,
  pendingBalance,
  currency,
  onAddFunds,
  onViewTransactions,
  showBalance,
  onToggleBalance,
}: WalletCardProps) {
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Wallet size={24} color="#B76E79" />
          <Text style={styles.title}>Wallet Balance</Text>
        </View>
        
        <TouchableOpacity style={styles.toggleButton} onPress={onToggleBalance}>
          {showBalance ? (
            <Eye size={20} color="#6B7280" />
          ) : (
            <EyeOff size={20} color="#6B7280" />
          )}
        </TouchableOpacity>
      </View>
      
      <View style={styles.balanceContainer}>
        <Text style={styles.balanceLabel}>Available Balance</Text>
        <Text style={styles.balanceAmount}>
          {showBalance ? formatCurrency(balance) : '••••'}
        </Text>
        
        {pendingBalance > 0 && (
          <Text style={styles.pendingBalance}>
            {formatCurrency(pendingBalance)} pending
          </Text>
        )}
      </View>
      
      <View style={styles.actions}>
        <TouchableOpacity style={styles.actionButton} onPress={onAddFunds}>
          <Plus size={16} color="#FFFFFF" />
          <Text style={styles.actionText}>Add Funds</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.actionButton, styles.secondaryButton]} 
          onPress={onViewTransactions}
        >
          <ArrowUpRight size={16} color="#B76E79" />
          <Text style={[styles.actionText, styles.secondaryText]}>Transactions</Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.quickStats}>
        <View style={styles.quickStat}>
          <ArrowDownLeft size={16} color="#10B981" />
          <Text style={styles.quickStatLabel}>This Month</Text>
          <Text style={styles.quickStatValue}>
            {showBalance ? formatCurrency(245) : '•••'}
          </Text>
        </View>
        
        <View style={styles.quickStat}>
          <ArrowUpRight size={16} color="#EF4444" />
          <Text style={styles.quickStatLabel}>Spent</Text>
          <Text style={styles.quickStatValue}>
            {showBalance ? formatCurrency(180) : '•••'}
          </Text>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
    marginLeft: 8,
  },
  toggleButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  balanceContainer: {
    marginBottom: 20,
  },
  balanceLabel: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
  },
  balanceAmount: {
    fontSize: 32,
    fontWeight: '700',
    color: '#1A2E4C',
    marginBottom: 4,
  },
  pendingBalance: {
    fontSize: 14,
    color: '#F59E0B',
    fontWeight: '500',
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#B76E79',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#B76E79',
  },
  actionText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  secondaryText: {
    color: '#B76E79',
  },
  quickStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  quickStat: {
    flex: 1,
    alignItems: 'center',
  },
  quickStatLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
    marginBottom: 2,
  },
  quickStatValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
  },
});