import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { format, parseISO } from 'date-fns';
import { Calendar, Clock, MapPin, ChevronRight } from 'lucide-react-native';
import { Link } from 'expo-router';

interface BookingCardProps {
  booking: any;
  isUpcoming: boolean;
}

export function BookingCard({ booking, isUpcoming }: BookingCardProps) {
  const companion = booking.companions;
  const profile = companion?.profiles;
  const bookingDate = parseISO(booking.booking_time);
  
  const getStatusColor = () => {
    switch (booking.status) {
      case 'confirmed':
        return '#10B981'; // green
      case 'pending':
        return '#F59E0B'; // amber
      case 'cancelled':
        return '#EF4444'; // red
      default:
        return '#6B7280'; // gray
    }
  };
  
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.profileContainer}>
          {profile?.avatar_url ? (
            <Image source={{ uri: profile.avatar_url }} style={styles.avatar} />
          ) : (
            <View style={styles.avatarPlaceholder}>
              <Text style={styles.avatarText}>
                {profile?.full_name?.charAt(0) || '?'}
              </Text>
            </View>
          )}
          <View>
            <Text style={styles.name}>{profile?.full_name || 'Companion'}</Text>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor() + '20' }]}>
              <View style={[styles.statusDot, { backgroundColor: getStatusColor() }]} />
              <Text style={[styles.statusText, { color: getStatusColor() }]}>
                {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
              </Text>
            </View>
          </View>
        </View>
        
        <Link 
          href={isUpcoming ? `/booking/${booking.id}` : `/booking/${booking.id}/review`} 
          asChild
        >
          <TouchableOpacity style={styles.actionButton}>
            <Text style={styles.actionText}>
              {isUpcoming ? 'Manage' : 'Review'}
            </Text>
            <ChevronRight size={16} color="#B76E79" />
          </TouchableOpacity>
        </Link>
      </View>
      
      <View style={styles.divider} />
      
      <View style={styles.detailsContainer}>
        <View style={styles.detailRow}>
          <Calendar size={16} color="#B76E79" style={styles.icon} />
          <Text style={styles.detailText}>
            {format(bookingDate, 'EEEE, MMMM d, yyyy')}
          </Text>
        </View>
        
        <View style={styles.detailRow}>
          <Clock size={16} color="#B76E79" style={styles.icon} />
          <Text style={styles.detailText}>
            {format(bookingDate, 'h:mm a')} • {booking.duration_hours} {booking.duration_hours === 1 ? 'hour' : 'hours'}
          </Text>
        </View>
        
        <View style={styles.detailRow}>
          <MapPin size={16} color="#B76E79" style={styles.icon} />
          <Text style={styles.detailText}>
            {booking.location || companion?.location || 'New York, NY'}
          </Text>
        </View>
      </View>
      
      {isUpcoming && (
        <View style={styles.footer}>
          <TouchableOpacity style={styles.footerButton}>
            <Text style={styles.footerButtonText}>Message</Text>
          </TouchableOpacity>
          
          {booking.status === 'pending' ? (
            <TouchableOpacity style={[styles.footerButton, styles.dangerButton]}>
              <Text style={styles.dangerButtonText}>Cancel</Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity style={[styles.footerButton, styles.primaryButton]}>
              <Text style={styles.primaryButtonText}>Reschedule</Text>
            </TouchableOpacity>
          )}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  profileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  avatarPlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#B76E79',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  avatarText: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: '600',
  },
  name: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 4,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  statusDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginRight: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#B76E79',
    marginRight: 2,
  },
  divider: {
    height: 1,
    backgroundColor: '#E5E7EB',
    marginBottom: 16,
  },
  detailsContainer: {
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  icon: {
    marginRight: 8,
  },
  detailText: {
    fontSize: 14,
    color: '#4B5563',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  footerButton: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    marginHorizontal: 4,
  },
  footerButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4B5563',
  },
  primaryButton: {
    backgroundColor: '#B76E79',
    borderColor: '#B76E79',
  },
  primaryButtonText: {
    color: '#FFFFFF',
  },
  dangerButton: {
    backgroundColor: '#FEE2E2',
    borderColor: '#EF4444',
  },
  dangerButtonText: {
    color: '#EF4444',
  },
});