import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Dimensions } from 'react-native';
import { BarChart3, TrendingUp, TrendingDown, Users, DollarSign, Calendar, Eye } from 'lucide-react-native';
import { AnalyticsService } from '@/lib/analytics/analytics-service';
import { format, subDays, startOfDay, endOfDay } from 'date-fns';

const { width } = Dimensions.get('window');

interface AnalyticsDashboardProps {
  userId?: string;
  isAdmin?: boolean;
}

export function AnalyticsDashboard({ userId, isAdmin = false }: AnalyticsDashboardProps) {
  const [timeRange, setTimeRange] = useState('7d');
  const [metrics, setMetrics] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [chartData, setChartData] = useState<any[]>([]);

  const analyticsService = AnalyticsService.getInstance();

  const timeRanges = [
    { id: '24h', label: '24 Hours', days: 1 },
    { id: '7d', label: '7 Days', days: 7 },
    { id: '30d', label: '30 Days', days: 30 },
    { id: '90d', label: '90 Days', days: 90 },
  ];

  useEffect(() => {
    fetchAnalytics();
  }, [timeRange]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      
      const selectedRange = timeRanges.find(r => r.id === timeRange);
      const endDate = endOfDay(new Date());
      const startDate = startOfDay(subDays(endDate, selectedRange?.days || 7));

      // Fetch basic metrics
      const events = await analyticsService.getEventCounts(startDate, endDate);
      
      // Calculate metrics
      const pageViews = events.filter(e => e.event_name === 'page_view').length;
      const bookings = events.filter(e => e.event_name === 'booking_created').length;
      const conversions = events.filter(e => e.event_category === 'conversion').length;
      const uniqueUsers = new Set(events.map(e => e.user_id)).size;

      setMetrics({
        pageViews,
        bookings,
        conversions,
        uniqueUsers,
        conversionRate: uniqueUsers > 0 ? (conversions / uniqueUsers * 100).toFixed(1) : 0,
      });

      // Generate chart data
      const chartData = generateChartData(events, selectedRange?.days || 7);
      setChartData(chartData);

    } catch (error) {
      console.error('Error fetching analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateChartData = (events: any[], days: number) => {
    const data = [];
    for (let i = days - 1; i >= 0; i--) {
      const date = subDays(new Date(), i);
      const dayEvents = events.filter(e => 
        format(new Date(e.created_at), 'yyyy-MM-dd') === format(date, 'yyyy-MM-dd')
      );
      
      data.push({
        date: format(date, 'MMM d'),
        pageViews: dayEvents.filter(e => e.event_name === 'page_view').length,
        bookings: dayEvents.filter(e => e.event_name === 'booking_created').length,
        users: new Set(dayEvents.map(e => e.user_id)).size,
      });
    }
    return data;
  };

  const MetricCard = ({ title, value, subtitle, trend, icon: Icon, color = '#B76E79' }: any) => (
    <View style={[styles.metricCard, { borderLeftColor: color }]}>
      <View style={styles.metricHeader}>
        <Icon size={24} color={color} />
        <View style={styles.trendContainer}>
          {trend > 0 ? (
            <TrendingUp size={16} color="#10B981" />
          ) : (
            <TrendingDown size={16} color="#EF4444" />
          )}
          <Text style={[
            styles.trendText,
            { color: trend > 0 ? '#10B981' : '#EF4444' }
          ]}>
            {Math.abs(trend)}%
          </Text>
        </View>
      </View>
      <Text style={styles.metricValue}>{value}</Text>
      <Text style={styles.metricTitle}>{title}</Text>
      {subtitle && <Text style={styles.metricSubtitle}>{subtitle}</Text>}
    </View>
  );

  const SimpleChart = ({ data }: { data: any[] }) => {
    if (!data.length) return null;

    const maxValue = Math.max(...data.map(d => Math.max(d.pageViews, d.bookings, d.users)));
    
    return (
      <View style={styles.chartContainer}>
        <Text style={styles.chartTitle}>Activity Overview</Text>
        <View style={styles.chart}>
          {data.map((item, index) => (
            <View key={index} style={styles.chartBar}>
              <View style={styles.chartBars}>
                <View 
                  style={[
                    styles.bar,
                    { 
                      height: (item.pageViews / maxValue) * 60,
                      backgroundColor: '#3B82F6'
                    }
                  ]} 
                />
                <View 
                  style={[
                    styles.bar,
                    { 
                      height: (item.bookings / maxValue) * 60,
                      backgroundColor: '#10B981'
                    }
                  ]} 
                />
                <View 
                  style={[
                    styles.bar,
                    { 
                      height: (item.users / maxValue) * 60,
                      backgroundColor: '#F59E0B'
                    }
                  ]} 
                />
              </View>
              <Text style={styles.chartLabel}>{item.date}</Text>
            </View>
          ))}
        </View>
        <View style={styles.chartLegend}>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: '#3B82F6' }]} />
            <Text style={styles.legendText}>Page Views</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: '#10B981' }]} />
            <Text style={styles.legendText}>Bookings</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: '#F59E0B' }]} />
            <Text style={styles.legendText}>Users</Text>
          </View>
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading analytics...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Time Range Selector */}
      <View style={styles.timeRangeContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {timeRanges.map((range) => (
            <TouchableOpacity
              key={range.id}
              style={[
                styles.timeRangeButton,
                timeRange === range.id && styles.timeRangeButtonActive
              ]}
              onPress={() => setTimeRange(range.id)}
            >
              <Text style={[
                styles.timeRangeText,
                timeRange === range.id && styles.timeRangeTextActive
              ]}>
                {range.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Key Metrics */}
      <View style={styles.metricsGrid}>
        <MetricCard
          title="Page Views"
          value={metrics?.pageViews || 0}
          trend={12}
          icon={Eye}
          color="#3B82F6"
        />
        <MetricCard
          title="Bookings"
          value={metrics?.bookings || 0}
          trend={8}
          icon={Calendar}
          color="#10B981"
        />
        <MetricCard
          title="Users"
          value={metrics?.uniqueUsers || 0}
          trend={-3}
          icon={Users}
          color="#F59E0B"
        />
        <MetricCard
          title="Conversion Rate"
          value={`${metrics?.conversionRate || 0}%`}
          trend={5}
          icon={TrendingUp}
          color="#8B5CF6"
        />
      </View>

      {/* Chart */}
      <SimpleChart data={chartData} />

      {/* Top Events */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Popular Actions</Text>
        <View style={styles.eventsList}>
          {[
            { name: 'Profile View', count: 1234, color: '#3B82F6' },
            { name: 'Search', count: 856, color: '#10B981' },
            { name: 'Message Sent', count: 432, color: '#F59E0B' },
            { name: 'Booking Created', count: 123, color: '#8B5CF6' },
          ].map((event, index) => (
            <View key={index} style={styles.eventItem}>
              <View style={[styles.eventDot, { backgroundColor: event.color }]} />
              <Text style={styles.eventName}>{event.name}</Text>
              <Text style={styles.eventCount}>{event.count}</Text>
            </View>
          ))}
        </View>
      </View>

      {/* User Segments */}
      {isAdmin && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>User Segments</Text>
          <View style={styles.segmentsList}>
            {[
              { name: 'New Users', count: 45, percentage: 23 },
              { name: 'Active Users', count: 156, percentage: 67 },
              { name: 'Premium Users', count: 34, percentage: 18 },
              { name: 'Churned Users', count: 12, percentage: 8 },
            ].map((segment, index) => (
              <View key={index} style={styles.segmentItem}>
                <Text style={styles.segmentName}>{segment.name}</Text>
                <View style={styles.segmentBar}>
                  <View 
                    style={[
                      styles.segmentBarFill,
                      { width: `${segment.percentage}%` }
                    ]} 
                  />
                </View>
                <Text style={styles.segmentCount}>{segment.count}</Text>
              </View>
            ))}
          </View>
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F3E9',
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  timeRangeContainer: {
    marginBottom: 24,
  },
  timeRangeButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  timeRangeButtonActive: {
    backgroundColor: '#B76E79',
    borderColor: '#B76E79',
  },
  timeRangeText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  timeRangeTextActive: {
    color: '#FFFFFF',
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -6,
    marginBottom: 24,
  },
  metricCard: {
    width: (width - 44) / 2,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    margin: 6,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  metricHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  trendText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 2,
  },
  metricValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1A2E4C',
    marginBottom: 4,
  },
  metricTitle: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  metricSubtitle: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 2,
  },
  chartContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 16,
  },
  chart: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    height: 80,
    marginBottom: 16,
  },
  chartBar: {
    alignItems: 'center',
    flex: 1,
  },
  chartBars: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 2,
    marginBottom: 8,
  },
  bar: {
    width: 6,
    borderRadius: 3,
    minHeight: 4,
  },
  chartLabel: {
    fontSize: 10,
    color: '#6B7280',
  },
  chartLegend: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 16,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  legendDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 4,
  },
  legendText: {
    fontSize: 12,
    color: '#6B7280',
  },
  section: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 16,
  },
  eventsList: {
    gap: 12,
  },
  eventItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  eventDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  eventName: {
    flex: 1,
    fontSize: 14,
    color: '#4B5563',
  },
  eventCount: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1A2E4C',
  },
  segmentsList: {
    gap: 16,
  },
  segmentItem: {
    gap: 8,
  },
  segmentName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1A2E4C',
  },
  segmentBar: {
    height: 8,
    backgroundColor: '#F3F4F6',
    borderRadius: 4,
    overflow: 'hidden',
  },
  segmentBarFill: {
    height: '100%',
    backgroundColor: '#B76E79',
    borderRadius: 4,
  },
  segmentCount: {
    fontSize: 12,
    color: '#6B7280',
    alignSelf: 'flex-end',
  },
});