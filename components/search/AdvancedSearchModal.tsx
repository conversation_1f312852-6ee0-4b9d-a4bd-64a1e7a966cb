import React, { useState } from 'react';
import { View, Text, StyleSheet, Modal, ScrollView, TouchableOpacity } from 'react-native';
import { X, SlidersHorizontal, MapPin, DollarSign, Star, Clock } from 'lucide-react-native';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';

interface AdvancedSearchModalProps {
  visible: boolean;
  onClose: () => void;
  onApplyFilters: (filters: any) => void;
  initialFilters?: any;
}

export function AdvancedSearchModal({ 
  visible, 
  onClose, 
  onApplyFilters, 
  initialFilters = {} 
}: AdvancedSearchModalProps) {
  const [filters, setFilters] = useState({
    ageMin: initialFilters.ageMin || 18,
    ageMax: initialFilters.ageMax || 65,
    priceMin: initialFilters.priceMin || 0,
    priceMax: initialFilters.priceMax || 500,
    maxDistance: initialFilters.maxDistance || 25,
    rating: initialFilters.rating || 0,
    languages: initialFilters.languages || [],
    interests: initialFilters.interests || [],
    isVerified: initialFilters.isVerified || false,
    isOnline: initialFilters.isOnline || false,
    availability: initialFilters.availability || 'any',
  });

  const languages = [
    'English', 'Spanish', 'French', 'German', 'Italian', 'Portuguese',
    'Russian', 'Chinese', 'Japanese', 'Korean', 'Arabic', 'Hindi'
  ];

  const interests = [
    'Dining', 'Movies', 'Theater', 'Museums', 'Art Galleries', 'Concerts',
    'Sports Events', 'Outdoor Activities', 'Travel', 'Shopping', 'Nightlife',
    'Cultural Events', 'Business Events', 'Social Gatherings', 'Fitness',
    'Cooking', 'Wine Tasting', 'Dancing', 'Photography', 'Reading'
  ];

  const availabilityOptions = [
    { id: 'any', label: 'Any time' },
    { id: 'now', label: 'Available now' },
    { id: 'today', label: 'Available today' },
    { id: 'week', label: 'This week' },
  ];

  const toggleArrayItem = (array: string[], item: string) => {
    return array.includes(item)
      ? array.filter(i => i !== item)
      : [...array, item];
  };

  const handleApply = () => {
    onApplyFilters(filters);
    onClose();
  };

  const handleReset = () => {
    setFilters({
      ageMin: 18,
      ageMax: 65,
      priceMin: 0,
      priceMax: 500,
      maxDistance: 25,
      rating: 0,
      languages: [],
      interests: [],
      isVerified: false,
      isOnline: false,
      availability: 'any',
    });
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <SlidersHorizontal size={24} color="#B76E79" />
            <Text style={styles.title}>Advanced Filters</Text>
          </View>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <X size={24} color="#1A2E4C" />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Age Range */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Age Range</Text>
            <View style={styles.rangeContainer}>
              <View style={styles.rangeInput}>
                <Input
                  label="Min Age"
                  value={filters.ageMin.toString()}
                  onChangeText={(text) => setFilters({
                    ...filters,
                    ageMin: parseInt(text) || 18
                  })}
                  keyboardType="numeric"
                />
              </View>
              <View style={styles.rangeInput}>
                <Input
                  label="Max Age"
                  value={filters.ageMax.toString()}
                  onChangeText={(text) => setFilters({
                    ...filters,
                    ageMax: parseInt(text) || 65
                  })}
                  keyboardType="numeric"
                />
              </View>
            </View>
          </View>

          {/* Price Range */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Price Range (per hour)</Text>
            <View style={styles.rangeContainer}>
              <View style={styles.rangeInput}>
                <Input
                  label="Min Price"
                  value={filters.priceMin.toString()}
                  onChangeText={(text) => setFilters({
                    ...filters,
                    priceMin: parseInt(text) || 0
                  })}
                  keyboardType="numeric"
                />
              </View>
              <View style={styles.rangeInput}>
                <Input
                  label="Max Price"
                  value={filters.priceMax.toString()}
                  onChangeText={(text) => setFilters({
                    ...filters,
                    priceMax: parseInt(text) || 500
                  })}
                  keyboardType="numeric"
                />
              </View>
            </View>
          </View>

          {/* Distance */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Maximum Distance</Text>
            <View style={styles.distanceOptions}>
              {[5, 10, 25, 50, 100].map((distance) => (
                <TouchableOpacity
                  key={distance}
                  style={[
                    styles.distanceOption,
                    filters.maxDistance === distance && styles.distanceOptionSelected
                  ]}
                  onPress={() => setFilters({ ...filters, maxDistance: distance })}
                >
                  <Text style={[
                    styles.distanceOptionText,
                    filters.maxDistance === distance && styles.distanceOptionTextSelected
                  ]}>
                    {distance} km
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Rating */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Minimum Rating</Text>
            <View style={styles.ratingOptions}>
              {[0, 3, 4, 4.5].map((rating) => (
                <TouchableOpacity
                  key={rating}
                  style={[
                    styles.ratingOption,
                    filters.rating === rating && styles.ratingOptionSelected
                  ]}
                  onPress={() => setFilters({ ...filters, rating })}
                >
                  <Star size={16} color="#FFB800" fill="#FFB800" />
                  <Text style={[
                    styles.ratingOptionText,
                    filters.rating === rating && styles.ratingOptionTextSelected
                  ]}>
                    {rating === 0 ? 'Any' : `${rating}+`}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Languages */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Languages</Text>
            <View style={styles.chipsContainer}>
              {languages.map((language) => (
                <TouchableOpacity
                  key={language}
                  style={[
                    styles.chip,
                    filters.languages.includes(language) && styles.chipSelected
                  ]}
                  onPress={() => setFilters({
                    ...filters,
                    languages: toggleArrayItem(filters.languages, language)
                  })}
                >
                  <Text style={[
                    styles.chipText,
                    filters.languages.includes(language) && styles.chipTextSelected
                  ]}>
                    {language}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Interests */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Interests</Text>
            <View style={styles.chipsContainer}>
              {interests.map((interest) => (
                <TouchableOpacity
                  key={interest}
                  style={[
                    styles.chip,
                    filters.interests.includes(interest) && styles.chipSelected
                  ]}
                  onPress={() => setFilters({
                    ...filters,
                    interests: toggleArrayItem(filters.interests, interest)
                  })}
                >
                  <Text style={[
                    styles.chipText,
                    filters.interests.includes(interest) && styles.chipTextSelected
                  ]}>
                    {interest}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Availability */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Availability</Text>
            <View style={styles.availabilityOptions}>
              {availabilityOptions.map((option) => (
                <TouchableOpacity
                  key={option.id}
                  style={[
                    styles.availabilityOption,
                    filters.availability === option.id && styles.availabilityOptionSelected
                  ]}
                  onPress={() => setFilters({ ...filters, availability: option.id })}
                >
                  <Clock size={16} color={filters.availability === option.id ? "#FFFFFF" : "#6B7280"} />
                  <Text style={[
                    styles.availabilityOptionText,
                    filters.availability === option.id && styles.availabilityOptionTextSelected
                  ]}>
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Quick Filters */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Quick Filters</Text>
            <View style={styles.quickFilters}>
              <TouchableOpacity
                style={[
                  styles.quickFilter,
                  filters.isVerified && styles.quickFilterSelected
                ]}
                onPress={() => setFilters({ ...filters, isVerified: !filters.isVerified })}
              >
                <Text style={[
                  styles.quickFilterText,
                  filters.isVerified && styles.quickFilterTextSelected
                ]}>
                  Verified Only
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.quickFilter,
                  filters.isOnline && styles.quickFilterSelected
                ]}
                onPress={() => setFilters({ ...filters, isOnline: !filters.isOnline })}
              >
                <Text style={[
                  styles.quickFilterText,
                  filters.isOnline && styles.quickFilterTextSelected
                ]}>
                  Online Now
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>

        {/* Actions */}
        <View style={styles.actions}>
          <Button
            title="Reset"
            variant="outline"
            onPress={handleReset}
            style={styles.resetButton}
          />
          <Button
            title="Apply Filters"
            onPress={handleApply}
            style={styles.applyButton}
          />
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F3E9',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
    marginLeft: 8,
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 12,
  },
  rangeContainer: {
    flexDirection: 'row',
    gap: 16,
  },
  rangeInput: {
    flex: 1,
  },
  distanceOptions: {
    flexDirection: 'row',
    gap: 8,
  },
  distanceOption: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 8,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    alignItems: 'center',
  },
  distanceOptionSelected: {
    borderColor: '#B76E79',
    backgroundColor: '#FDF2F8',
  },
  distanceOptionText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  distanceOptionTextSelected: {
    color: '#B76E79',
  },
  ratingOptions: {
    flexDirection: 'row',
    gap: 8,
  },
  ratingOption: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#E5E7EB',
  },
  ratingOptionSelected: {
    borderColor: '#B76E79',
    backgroundColor: '#FDF2F8',
  },
  ratingOptionText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
    marginLeft: 4,
  },
  ratingOptionTextSelected: {
    color: '#B76E79',
  },
  chipsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  chip: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  chipSelected: {
    borderColor: '#B76E79',
    backgroundColor: '#B76E79',
  },
  chipText: {
    fontSize: 14,
    color: '#6B7280',
  },
  chipTextSelected: {
    color: '#FFFFFF',
  },
  availabilityOptions: {
    gap: 8,
  },
  availabilityOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#E5E7EB',
  },
  availabilityOptionSelected: {
    borderColor: '#B76E79',
    backgroundColor: '#B76E79',
  },
  availabilityOptionText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
    marginLeft: 8,
  },
  availabilityOptionTextSelected: {
    color: '#FFFFFF',
  },
  quickFilters: {
    flexDirection: 'row',
    gap: 12,
  },
  quickFilter: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    alignItems: 'center',
  },
  quickFilterSelected: {
    borderColor: '#B76E79',
    backgroundColor: '#B76E79',
  },
  quickFilterText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  quickFilterTextSelected: {
    color: '#FFFFFF',
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  resetButton: {
    flex: 1,
  },
  applyButton: {
    flex: 2,
  },
});