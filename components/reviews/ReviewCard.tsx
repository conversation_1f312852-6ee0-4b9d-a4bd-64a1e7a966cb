import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, ScrollView } from 'react-native';
import { Star, ThumbsUp, ThumbsDown, MoreHorizontal, Flag } from 'lucide-react-native';
import { format } from 'date-fns';

interface ReviewCardProps {
  review: {
    id: string;
    overall_rating: number;
    communication_rating?: number;
    punctuality_rating?: number;
    appearance_rating?: number;
    experience_rating?: number;
    title?: string;
    content: string;
    photos?: string[];
    helpful_count: number;
    not_helpful_count: number;
    is_verified: boolean;
    is_featured: boolean;
    response_from_companion?: string;
    response_date?: string;
    created_at: string;
    reviewer: {
      full_name: string;
      avatar_url?: string;
    };
  };
  onHelpfulPress: (reviewId: string, isHelpful: boolean) => void;
  onReportPress: (reviewId: string) => void;
  showCompanionResponse?: boolean;
}

export function ReviewCard({ 
  review, 
  onHelpfulPress, 
  onReportPress, 
  showCompanionResponse = true 
}: ReviewCardProps) {
  const [showFullContent, setShowFullContent] = useState(false);
  const [userHelpfulVote, setUserHelpfulVote] = useState<boolean | null>(null);

  const renderStars = (rating: number, size = 16) => {
    return Array(5)
      .fill(0)
      .map((_, i) => (
        <Star
          key={i}
          size={size}
          color={i < Math.floor(rating) ? '#FFB800' : '#D1D5DB'}
          fill={i < Math.floor(rating) ? '#FFB800' : 'none'}
        />
      ));
  };

  const handleHelpfulPress = (isHelpful: boolean) => {
    if (userHelpfulVote === isHelpful) {
      setUserHelpfulVote(null);
    } else {
      setUserHelpfulVote(isHelpful);
      onHelpfulPress(review.id, isHelpful);
    }
  };

  const truncatedContent = review.content.length > 200 
    ? review.content.substring(0, 200) + '...'
    : review.content;

  return (
    <View style={[styles.container, review.is_featured && styles.featuredContainer]}>
      {review.is_featured && (
        <View style={styles.featuredBadge}>
          <Star size={12} color="#F59E0B" fill="#F59E0B" />
          <Text style={styles.featuredText}>Featured Review</Text>
        </View>
      )}

      <View style={styles.header}>
        <View style={styles.reviewerInfo}>
          {review.reviewer.avatar_url ? (
            <Image source={{ uri: review.reviewer.avatar_url }} style={styles.avatar} />
          ) : (
            <View style={styles.avatarPlaceholder}>
              <Text style={styles.avatarText}>
                {review.reviewer.full_name.charAt(0)}
              </Text>
            </View>
          )}
          <View style={styles.reviewerDetails}>
            <View style={styles.nameRow}>
              <Text style={styles.reviewerName}>{review.reviewer.full_name}</Text>
              {review.is_verified && (
                <View style={styles.verifiedBadge}>
                  <Text style={styles.verifiedText}>Verified</Text>
                </View>
              )}
            </View>
            <Text style={styles.reviewDate}>
              {format(new Date(review.created_at), 'MMMM d, yyyy')}
            </Text>
          </View>
        </View>
        
        <TouchableOpacity 
          style={styles.moreButton}
          onPress={() => onReportPress(review.id)}
        >
          <MoreHorizontal size={20} color="#6B7280" />
        </TouchableOpacity>
      </View>

      {/* Overall Rating */}
      <View style={styles.ratingContainer}>
        <View style={styles.overallRating}>
          {renderStars(review.overall_rating, 20)}
          <Text style={styles.ratingText}>{review.overall_rating}/5</Text>
        </View>
      </View>

      {/* Detailed Ratings */}
      {(review.communication_rating || review.punctuality_rating || 
        review.appearance_rating || review.experience_rating) && (
        <View style={styles.detailedRatings}>
          {review.communication_rating && (
            <View style={styles.detailedRating}>
              <Text style={styles.ratingLabel}>Communication</Text>
              <View style={styles.ratingStars}>
                {renderStars(review.communication_rating, 14)}
              </View>
            </View>
          )}
          {review.punctuality_rating && (
            <View style={styles.detailedRating}>
              <Text style={styles.ratingLabel}>Punctuality</Text>
              <View style={styles.ratingStars}>
                {renderStars(review.punctuality_rating, 14)}
              </View>
            </View>
          )}
          {review.appearance_rating && (
            <View style={styles.detailedRating}>
              <Text style={styles.ratingLabel}>Appearance</Text>
              <View style={styles.ratingStars}>
                {renderStars(review.appearance_rating, 14)}
              </View>
            </View>
          )}
          {review.experience_rating && (
            <View style={styles.detailedRating}>
              <Text style={styles.ratingLabel}>Experience</Text>
              <View style={styles.ratingStars}>
                {renderStars(review.experience_rating, 14)}
              </View>
            </View>
          )}
        </View>
      )}

      {/* Review Title */}
      {review.title && (
        <Text style={styles.reviewTitle}>{review.title}</Text>
      )}

      {/* Review Content */}
      <View style={styles.contentContainer}>
        <Text style={styles.reviewContent}>
          {showFullContent ? review.content : truncatedContent}
        </Text>
        {review.content.length > 200 && (
          <TouchableOpacity onPress={() => setShowFullContent(!showFullContent)}>
            <Text style={styles.readMoreText}>
              {showFullContent ? 'Read less' : 'Read more'}
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Review Photos */}
      {review.photos && review.photos.length > 0 && (
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          style={styles.photosContainer}
        >
          {review.photos.map((photo, index) => (
            <Image key={index} source={{ uri: photo }} style={styles.reviewPhoto} />
          ))}
        </ScrollView>
      )}

      {/* Companion Response */}
      {showCompanionResponse && review.response_from_companion && (
        <View style={styles.companionResponse}>
          <Text style={styles.responseLabel}>Response from companion:</Text>
          <Text style={styles.responseContent}>{review.response_from_companion}</Text>
          <Text style={styles.responseDate}>
            {format(new Date(review.response_date!), 'MMMM d, yyyy')}
          </Text>
        </View>
      )}

      {/* Actions */}
      <View style={styles.actions}>
        <View style={styles.helpfulActions}>
          <TouchableOpacity
            style={[
              styles.helpfulButton,
              userHelpfulVote === true && styles.helpfulButtonActive
            ]}
            onPress={() => handleHelpfulPress(true)}
          >
            <ThumbsUp 
              size={16} 
              color={userHelpfulVote === true ? "#10B981" : "#6B7280"} 
              fill={userHelpfulVote === true ? "#10B981" : "none"}
            />
            <Text style={[
              styles.helpfulText,
              userHelpfulVote === true && styles.helpfulTextActive
            ]}>
              Helpful ({review.helpful_count})
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.helpfulButton,
              userHelpfulVote === false && styles.notHelpfulButtonActive
            ]}
            onPress={() => handleHelpfulPress(false)}
          >
            <ThumbsDown 
              size={16} 
              color={userHelpfulVote === false ? "#EF4444" : "#6B7280"} 
              fill={userHelpfulVote === false ? "#EF4444" : "none"}
            />
            <Text style={[
              styles.helpfulText,
              userHelpfulVote === false && styles.notHelpfulTextActive
            ]}>
              ({review.not_helpful_count})
            </Text>
          </TouchableOpacity>
        </View>
        
        <TouchableOpacity 
          style={styles.reportButton}
          onPress={() => onReportPress(review.id)}
        >
          <Flag size={16} color="#6B7280" />
          <Text style={styles.reportText}>Report</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  featuredContainer: {
    borderWidth: 2,
    borderColor: '#F59E0B',
  },
  featuredBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF3C7',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
    marginBottom: 12,
  },
  featuredText: {
    fontSize: 12,
    color: '#92400E',
    fontWeight: '500',
    marginLeft: 4,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  reviewerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  avatarPlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#B76E79',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  avatarText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  reviewerDetails: {
    flex: 1,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  reviewerName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    marginRight: 8,
  },
  verifiedBadge: {
    backgroundColor: '#D1FAE5',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  verifiedText: {
    fontSize: 10,
    color: '#065F46',
    fontWeight: '500',
  },
  reviewDate: {
    fontSize: 12,
    color: '#6B7280',
  },
  moreButton: {
    padding: 4,
  },
  ratingContainer: {
    marginBottom: 12,
  },
  overallRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    marginLeft: 8,
  },
  detailedRatings: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    gap: 8,
  },
  detailedRating: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  ratingLabel: {
    fontSize: 14,
    color: '#4B5563',
    flex: 1,
  },
  ratingStars: {
    flexDirection: 'row',
  },
  reviewTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 8,
  },
  contentContainer: {
    marginBottom: 12,
  },
  reviewContent: {
    fontSize: 14,
    color: '#4B5563',
    lineHeight: 20,
  },
  readMoreText: {
    fontSize: 14,
    color: '#B76E79',
    fontWeight: '500',
    marginTop: 4,
  },
  photosContainer: {
    marginBottom: 12,
  },
  reviewPhoto: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginRight: 8,
  },
  companionResponse: {
    backgroundColor: '#F0FDF4',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#10B981',
  },
  responseLabel: {
    fontSize: 12,
    color: '#065F46',
    fontWeight: '600',
    marginBottom: 4,
  },
  responseContent: {
    fontSize: 14,
    color: '#166534',
    lineHeight: 18,
    marginBottom: 4,
  },
  responseDate: {
    fontSize: 12,
    color: '#059669',
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  helpfulActions: {
    flexDirection: 'row',
    gap: 16,
  },
  helpfulButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 6,
  },
  helpfulButtonActive: {
    backgroundColor: '#D1FAE5',
  },
  notHelpfulButtonActive: {
    backgroundColor: '#FEE2E2',
  },
  helpfulText: {
    fontSize: 12,
    color: '#6B7280',
    marginLeft: 4,
  },
  helpfulTextActive: {
    color: '#10B981',
  },
  notHelpfulTextActive: {
    color: '#EF4444',
  },
  reportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  reportText: {
    fontSize: 12,
    color: '#6B7280',
    marginLeft: 4,
  },
});