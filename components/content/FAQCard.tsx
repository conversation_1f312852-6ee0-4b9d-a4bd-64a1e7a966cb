import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { ChevronDown, ChevronUp, ThumbsUp, ThumbsDown, Eye } from 'lucide-react-native';

interface FAQCardProps {
  faq: {
    id: string;
    question: string;
    answer: string;
    category: string;
    viewCount: number;
    helpfulCount: number;
    notHelpfulCount: number;
    isFeatured: boolean;
  };
  onMarkHelpful: (faqId: string, isHelpful: boolean) => void;
}

export function FAQCard({ faq, onMarkHelpful }: FAQCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [userVote, setUserVote] = useState<boolean | null>(null);

  const handleVote = (isHelpful: boolean) => {
    if (userVote === isHelpful) {
      setUserVote(null);
    } else {
      setUserVote(isHelpful);
      onMarkHelpful(faq.id, isHelpful);
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'getting_started':
        return '#10B981';
      case 'booking':
        return '#3B82F6';
      case 'payments':
        return '#F59E0B';
      case 'safety':
        return '#EF4444';
      case 'account':
        return '#8B5CF6';
      case 'technical':
        return '#6B7280';
      case 'companions':
        return '#B76E79';
      default:
        return '#6B7280';
    }
  };

  return (
    <View style={[
      styles.container,
      faq.isFeatured && styles.featuredContainer
    ]}>
      {faq.isFeatured && (
        <View style={styles.featuredBadge}>
          <Text style={styles.featuredText}>Popular</Text>
        </View>
      )}
      
      <TouchableOpacity 
        style={styles.questionContainer}
        onPress={() => setIsExpanded(!isExpanded)}
      >
        <View style={styles.questionHeader}>
          <View style={[
            styles.categoryDot,
            { backgroundColor: getCategoryColor(faq.category) }
          ]} />
          <Text style={styles.question}>{faq.question}</Text>
          {isExpanded ? (
            <ChevronUp size={20} color="#6B7280" />
          ) : (
            <ChevronDown size={20} color="#6B7280" />
          )}
        </View>
      </TouchableOpacity>
      
      {isExpanded && (
        <View style={styles.answerContainer}>
          <Text style={styles.answer}>{faq.answer}</Text>
          
          <View style={styles.footer}>
            <View style={styles.stats}>
              <View style={styles.stat}>
                <Eye size={14} color="#6B7280" />
                <Text style={styles.statText}>{faq.viewCount} views</Text>
              </View>
            </View>
            
            <View style={styles.helpfulActions}>
              <Text style={styles.helpfulLabel}>Was this helpful?</Text>
              <TouchableOpacity
                style={[
                  styles.helpfulButton,
                  userVote === true && styles.helpfulButtonActive
                ]}
                onPress={() => handleVote(true)}
              >
                <ThumbsUp 
                  size={16} 
                  color={userVote === true ? "#10B981" : "#6B7280"} 
                  fill={userVote === true ? "#10B981" : "none"}
                />
                <Text style={[
                  styles.helpfulText,
                  userVote === true && styles.helpfulTextActive
                ]}>
                  {faq.helpfulCount}
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.helpfulButton,
                  userVote === false && styles.notHelpfulButtonActive
                ]}
                onPress={() => handleVote(false)}
              >
                <ThumbsDown 
                  size={16} 
                  color={userVote === false ? "#EF4444" : "#6B7280"} 
                  fill={userVote === false ? "#EF4444" : "none"}
                />
                <Text style={[
                  styles.helpfulText,
                  userVote === false && styles.notHelpfulTextActive
                ]}>
                  {faq.notHelpfulCount}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
    overflow: 'hidden',
    position: 'relative',
  },
  featuredContainer: {
    borderLeftWidth: 4,
    borderLeftColor: '#F59E0B',
  },
  featuredBadge: {
    position: 'absolute',
    top: 12,
    right: 12,
    backgroundColor: '#F59E0B',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    zIndex: 1,
  },
  featuredText: {
    fontSize: 10,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  questionContainer: {
    padding: 16,
  },
  questionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 12,
  },
  question: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    flex: 1,
    marginRight: 8,
    lineHeight: 22,
  },
  answerContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  answer: {
    fontSize: 14,
    color: '#4B5563',
    lineHeight: 20,
    marginTop: 12,
    marginBottom: 16,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  stats: {
    flexDirection: 'row',
  },
  stat: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    fontSize: 12,
    color: '#6B7280',
    marginLeft: 4,
  },
  helpfulActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  helpfulLabel: {
    fontSize: 12,
    color: '#6B7280',
  },
  helpfulButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: '#F3F4F6',
  },
  helpfulButtonActive: {
    backgroundColor: '#D1FAE5',
  },
  notHelpfulButtonActive: {
    backgroundColor: '#FEE2E2',
  },
  helpfulText: {
    fontSize: 12,
    color: '#6B7280',
    marginLeft: 4,
  },
  helpfulTextActive: {
    color: '#10B981',
  },
  notHelpfulTextActive: {
    color: '#EF4444',
  },
});