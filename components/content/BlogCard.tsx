import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { Clock, Eye, Heart, Share } from 'lucide-react-native';
import { format } from 'date-fns';

interface BlogCardProps {
  article: {
    id: string;
    title: string;
    excerpt?: string;
    featuredImageUrl?: string;
    category: string;
    estimatedReadTime: number;
    viewCount: number;
    likeCount: number;
    publishedAt?: Date;
    isFeatured: boolean;
  };
  onPress: () => void;
  onLike: () => void;
  onShare: () => void;
}

export function BlogCard({ article, onPress, onLike, onShare }: BlogCardProps) {
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'dating_tips':
        return '#F59E0B';
      case 'safety':
        return '#EF4444';
      case 'company_news':
        return '#3B82F6';
      case 'success_stories':
        return '#10B981';
      case 'guides':
        return '#8B5CF6';
      default:
        return '#6B7280';
    }
  };

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'dating_tips':
        return 'Dating Tips';
      case 'safety':
        return 'Safety';
      case 'company_news':
        return 'Company News';
      case 'success_stories':
        return 'Success Stories';
      case 'guides':
        return 'Guides';
      default:
        return category;
    }
  };

  return (
    <TouchableOpacity style={[
      styles.container,
      article.isFeatured && styles.featuredContainer
    ]} onPress={onPress}>
      {article.isFeatured && (
        <View style={styles.featuredBadge}>
          <Text style={styles.featuredText}>Featured</Text>
        </View>
      )}
      
      {article.featuredImageUrl && (
        <Image source={{ uri: article.featuredImageUrl }} style={styles.image} />
      )}
      
      <View style={styles.content}>
        <View style={styles.header}>
          <View style={[
            styles.categoryBadge,
            { backgroundColor: getCategoryColor(article.category) + '20' }
          ]}>
            <Text style={[
              styles.categoryText,
              { color: getCategoryColor(article.category) }
            ]}>
              {getCategoryLabel(article.category)}
            </Text>
          </View>
          
          <View style={styles.readTime}>
            <Clock size={12} color="#6B7280" />
            <Text style={styles.readTimeText}>{article.estimatedReadTime} min</Text>
          </View>
        </View>
        
        <Text style={styles.title} numberOfLines={2}>
          {article.title}
        </Text>
        
        {article.excerpt && (
          <Text style={styles.excerpt} numberOfLines={3}>
            {article.excerpt}
          </Text>
        )}
        
        <View style={styles.footer}>
          <View style={styles.stats}>
            <View style={styles.stat}>
              <Eye size={14} color="#6B7280" />
              <Text style={styles.statText}>{article.viewCount}</Text>
            </View>
            <View style={styles.stat}>
              <Heart size={14} color="#6B7280" />
              <Text style={styles.statText}>{article.likeCount}</Text>
            </View>
          </View>
          
          <View style={styles.actions}>
            <TouchableOpacity style={styles.actionButton} onPress={onLike}>
              <Heart size={16} color="#B76E79" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton} onPress={onShare}>
              <Share size={16} color="#B76E79" />
            </TouchableOpacity>
          </View>
        </View>
        
        {article.publishedAt && (
          <Text style={styles.publishDate}>
            {format(article.publishedAt, 'MMMM d, yyyy')}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    position: 'relative',
  },
  featuredContainer: {
    borderWidth: 2,
    borderColor: '#F59E0B',
  },
  featuredBadge: {
    position: 'absolute',
    top: 12,
    right: 12,
    backgroundColor: '#F59E0B',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    zIndex: 1,
  },
  featuredText: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  image: {
    width: '100%',
    height: 160,
    resizeMode: 'cover',
  },
  content: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  categoryText: {
    fontSize: 12,
    fontWeight: '500',
  },
  readTime: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  readTimeText: {
    fontSize: 12,
    color: '#6B7280',
    marginLeft: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
    lineHeight: 24,
    marginBottom: 8,
  },
  excerpt: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
    marginBottom: 12,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  stats: {
    flexDirection: 'row',
    gap: 16,
  },
  stat: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    fontSize: 12,
    color: '#6B7280',
    marginLeft: 4,
  },
  actions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F7F3E9',
    justifyContent: 'center',
    alignItems: 'center',
  },
  publishDate: {
    fontSize: 12,
    color: '#9CA3AF',
  },
});