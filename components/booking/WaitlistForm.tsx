import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Switch } from 'react-native';
import { Clock, Calendar, DollarSign, Bell } from 'lucide-react-native';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { format, addDays } from 'date-fns';

interface WaitlistFormProps {
  companionId: string;
  companionName: string;
  hourlyRate: number;
  onSubmit: (data: any) => void;
  onCancel: () => void;
}

export function WaitlistForm({ 
  companionId, 
  companionName, 
  hourlyRate, 
  onSubmit, 
  onCancel 
}: WaitlistFormProps) {
  const [preferredDate, setPreferredDate] = useState<Date | null>(null);
  const [preferredTimeStart, setPreferredTimeStart] = useState('18:00');
  const [preferredTimeEnd, setPreferredTimeEnd] = useState('22:00');
  const [durationHours, setDurationHours] = useState(2);
  const [flexibleDates, setFlexibleDates] = useState(false);
  const [dateRangeStart, setDateRangeStart] = useState(new Date());
  const [dateRangeEnd, setDateRangeEnd] = useState(addDays(new Date(), 14));
  const [maxPrice, setMaxPrice] = useState<number | null>(null);
  const [autoBook, setAutoBook] = useState(false);
  const [notes, setNotes] = useState('');

  const handleSubmit = () => {
    const waitlistData = {
      companionId,
      preferredDate,
      preferredTimeStart,
      preferredTimeEnd,
      durationHours,
      flexibleDates,
      dateRangeStart: flexibleDates ? dateRangeStart : null,
      dateRangeEnd: flexibleDates ? dateRangeEnd : null,
      maxPrice,
      autoBook,
      notes,
    };

    onSubmit(waitlistData);
  };

  const estimatedCost = hourlyRate * durationHours;

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Bell size={24} color="#B76E79" />
        <Text style={styles.title}>Join Waitlist</Text>
      </View>

      <View style={styles.companionInfo}>
        <Text style={styles.companionName}>{companionName}</Text>
        <Text style={styles.companionRate}>{hourlyRate}/hour</Text>
      </View>

      {/* Preferred Date/Time */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Preferred Booking</Text>
        
        {!flexibleDates && (
          <TouchableOpacity style={styles.dateButton}>
            <Calendar size={16} color="#B76E79" />
            <Text style={styles.dateText}>
              {preferredDate ? format(preferredDate, 'EEEE, MMMM d, yyyy') : 'Select preferred date'}
            </Text>
          </TouchableOpacity>
        )}

        <View style={styles.timeRow}>
          <View style={styles.timeInput}>
            <Text style={styles.timeLabel}>Start Time</Text>
            <Input
              value={preferredTimeStart}
              onChangeText={setPreferredTimeStart}
              placeholder="18:00"
              style={styles.timeField}
            />
          </View>
          
          <View style={styles.timeInput}>
            <Text style={styles.timeLabel}>End Time</Text>
            <Input
              value={preferredTimeEnd}
              onChangeText={setPreferredTimeEnd}
              placeholder="22:00"
              style={styles.timeField}
            />
          </View>
        </View>

        <View style={styles.durationSection}>
          <Text style={styles.durationLabel}>Duration</Text>
          <View style={styles.durationButtons}>
            {[1, 2, 3, 4, 6, 8].map((hours) => (
              <TouchableOpacity
                key={hours}
                style={[
                  styles.durationButton,
                  durationHours === hours && styles.durationButtonSelected
                ]}
                onPress={() => setDurationHours(hours)}
              >
                <Text style={[
                  styles.durationButtonText,
                  durationHours === hours && styles.durationButtonTextSelected
                ]}>
                  {hours}h
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>

      {/* Flexibility Options */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Flexibility</Text>
        
        <View style={styles.flexibilityRow}>
          <Text style={styles.flexibilityLabel}>I'm flexible with dates</Text>
          <Switch
            value={flexibleDates}
            onValueChange={setFlexibleDates}
            trackColor={{ false: '#D1D5DB', true: '#B76E79' }}
            thumbColor={flexibleDates ? '#FFFFFF' : '#F3F4F6'}
          />
        </View>

        {flexibleDates && (
          <View style={styles.dateRangeContainer}>
            <Text style={styles.dateRangeLabel}>Available date range:</Text>
            <View style={styles.dateRangeRow}>
              <TouchableOpacity style={styles.dateRangeButton}>
                <Text style={styles.dateRangeText}>
                  {format(dateRangeStart, 'MMM d')}
                </Text>
              </TouchableOpacity>
              <Text style={styles.dateRangeSeparator}>to</Text>
              <TouchableOpacity style={styles.dateRangeButton}>
                <Text style={styles.dateRangeText}>
                  {format(dateRangeEnd, 'MMM d')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        <Input
          label="Maximum price per hour (optional)"
          value={maxPrice?.toString() || ''}
          onChangeText={(text) => setMaxPrice(text ? parseFloat(text) : null)}
          placeholder={`Current rate: $${hourlyRate}`}
          keyboardType="numeric"
        />
      </View>

      {/* Auto-booking */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Booking Preferences</Text>
        
        <View style={styles.autoBookRow}>
          <View style={styles.autoBookInfo}>
            <Text style={styles.autoBookLabel}>Auto-book when available</Text>
            <Text style={styles.autoBookDescription}>
              Automatically book when a slot becomes available
            </Text>
          </View>
          <Switch
            value={autoBook}
            onValueChange={setAutoBook}
            trackColor={{ false: '#D1D5DB', true: '#10B981' }}
            thumbColor={autoBook ? '#FFFFFF' : '#F3F4F6'}
          />
        </View>

        <Input
          label="Special requests (optional)"
          value={notes}
          onChangeText={setNotes}
          placeholder="Any special requests or preferences..."
          multiline
          style={styles.notesInput}
        />
      </View>

      {/* Cost Estimate */}
      <View style={styles.section}>
        <View style={styles.costCard}>
          <DollarSign size={20} color="#B76E79" />
          <View style={styles.costInfo}>
            <Text style={styles.costLabel}>Estimated cost</Text>
            <Text style={styles.costValue}>
              ${estimatedCost} ({durationHours} hours × ${hourlyRate})
            </Text>
          </View>
        </View>
      </View>

      {/* Waitlist Info */}
      <View style={styles.infoCard}>
        <Text style={styles.infoTitle}>How the waitlist works:</Text>
        <Text style={styles.infoText}>
          • You'll be notified when a matching slot becomes available{'\n'}
          • Higher priority given to flexible dates and times{'\n'}
          • Auto-booking requires a valid payment method{'\n'}
          • You can leave the waitlist anytime
        </Text>
      </View>

      {/* Actions */}
      <View style={styles.actions}>
        <Button
          title="Cancel"
          variant="outline"
          onPress={onCancel}
          style={styles.cancelButton}
        />
        <Button
          title="Join Waitlist"
          onPress={handleSubmit}
          style={styles.submitButton}
        />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F3E9',
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1A2E4C',
    marginLeft: 8,
  },
  companionInfo: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  companionName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
  },
  companionRate: {
    fontSize: 16,
    fontWeight: '500',
    color: '#B76E79',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 12,
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  dateText: {
    fontSize: 16,
    color: '#1A2E4C',
    marginLeft: 8,
  },
  timeRow: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  timeInput: {
    flex: 1,
  },
  timeLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4B5563',
    marginBottom: 6,
  },
  timeField: {
    marginBottom: 0,
  },
  durationSection: {
    marginTop: 8,
  },
  durationLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4B5563',
    marginBottom: 8,
  },
  durationButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  durationButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  durationButtonSelected: {
    borderColor: '#B76E79',
    backgroundColor: '#B76E79',
  },
  durationButtonText: {
    fontSize: 14,
    color: '#6B7280',
  },
  durationButtonTextSelected: {
    color: '#FFFFFF',
  },
  flexibilityRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  flexibilityLabel: {
    fontSize: 16,
    color: '#1A2E4C',
  },
  dateRangeContainer: {
    marginBottom: 16,
  },
  dateRangeLabel: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 8,
  },
  dateRangeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dateRangeButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  dateRangeText: {
    fontSize: 14,
    color: '#1A2E4C',
  },
  dateRangeSeparator: {
    fontSize: 14,
    color: '#6B7280',
    marginHorizontal: 12,
  },
  autoBookRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  autoBookInfo: {
    flex: 1,
    marginRight: 16,
  },
  autoBookLabel: {
    fontSize: 16,
    color: '#1A2E4C',
    marginBottom: 2,
  },
  autoBookDescription: {
    fontSize: 12,
    color: '#6B7280',
  },
  notesInput: {
    height: 80,
    textAlignVertical: 'top',
  },
  costCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
  },
  costInfo: {
    marginLeft: 12,
  },
  costLabel: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 2,
  },
  costValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
  },
  infoCard: {
    backgroundColor: '#EBF8FF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    borderLeftWidth: 4,
    borderLeftColor: '#3B82F6',
  },
  infoTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1E40AF',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#1E40AF',
    lineHeight: 20,
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 32,
  },
  cancelButton: {
    flex: 1,
  },
  submitButton: {
    flex: 2,
  },
});