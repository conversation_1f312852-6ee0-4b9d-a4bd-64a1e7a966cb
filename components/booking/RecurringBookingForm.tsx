import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Switch } from 'react-native';
import { Calendar, Clock, Repeat, DollarSign } from 'lucide-react-native';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { format, addDays, addWeeks, addMonths } from 'date-fns';

interface RecurringBookingFormProps {
  companionId: string;
  hourlyRate: number;
  onSubmit: (data: any) => void;
  onCancel: () => void;
}

export function RecurringBookingForm({ 
  companionId, 
  hourlyRate, 
  onSubmit, 
  onCancel 
}: RecurringBookingFormProps) {
  const [patternType, setPatternType] = useState<'weekly' | 'biweekly' | 'monthly'>('weekly');
  const [frequency, setFrequency] = useState(1);
  const [selectedDays, setSelectedDays] = useState<number[]>([]);
  const [timeOfDay, setTimeOfDay] = useState('18:00');
  const [durationHours, setDurationHours] = useState(2);
  const [startDate, setStartDate] = useState(addDays(new Date(), 1));
  const [hasEndDate, setHasEndDate] = useState(false);
  const [endDate, setEndDate] = useState(addMonths(new Date(), 3));
  const [maxOccurrences, setMaxOccurrences] = useState<number | null>(null);

  const daysOfWeek = [
    { id: 0, label: 'Sun', name: 'Sunday' },
    { id: 1, label: 'Mon', name: 'Monday' },
    { id: 2, label: 'Tue', name: 'Tuesday' },
    { id: 3, label: 'Wed', name: 'Wednesday' },
    { id: 4, label: 'Thu', name: 'Thursday' },
    { id: 5, label: 'Fri', name: 'Friday' },
    { id: 6, label: 'Sat', name: 'Saturday' },
  ];

  const patternOptions = [
    { id: 'weekly', label: 'Weekly', description: 'Every week' },
    { id: 'biweekly', label: 'Bi-weekly', description: 'Every 2 weeks' },
    { id: 'monthly', label: 'Monthly', description: 'Every month' },
  ];

  const toggleDay = (dayId: number) => {
    setSelectedDays(prev => 
      prev.includes(dayId) 
        ? prev.filter(d => d !== dayId)
        : [...prev, dayId].sort()
    );
  };

  const calculateTotalCost = () => {
    const costPerBooking = hourlyRate * durationHours;
    let estimatedBookings = 0;

    if (maxOccurrences) {
      estimatedBookings = maxOccurrences;
    } else if (hasEndDate) {
      const weeks = Math.ceil((endDate.getTime() - startDate.getTime()) / (7 * 24 * 60 * 60 * 1000));
      estimatedBookings = Math.floor(weeks / (patternType === 'biweekly' ? 2 : patternType === 'monthly' ? 4 : 1));
    } else {
      estimatedBookings = 12; // Default estimate
    }

    return costPerBooking * estimatedBookings;
  };

  const getNextBookingDates = () => {
    const dates = [];
    let currentDate = new Date(startDate);
    
    for (let i = 0; i < 5; i++) {
      dates.push(new Date(currentDate));
      
      switch (patternType) {
        case 'weekly':
          currentDate = addWeeks(currentDate, frequency);
          break;
        case 'biweekly':
          currentDate = addWeeks(currentDate, 2);
          break;
        case 'monthly':
          currentDate = addMonths(currentDate, frequency);
          break;
      }
    }
    
    return dates;
  };

  const handleSubmit = () => {
    const recurringBookingData = {
      companionId,
      pattern: {
        type: patternType,
        frequency,
        daysOfWeek: selectedDays,
        endDate: hasEndDate ? endDate : null,
        maxOccurrences,
      },
      timeOfDay,
      durationHours,
      startDate,
      totalAmount: hourlyRate * durationHours,
    };

    onSubmit(recurringBookingData);
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Repeat size={24} color="#B76E79" />
        <Text style={styles.title}>Set Up Recurring Booking</Text>
      </View>

      {/* Pattern Selection */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Booking Pattern</Text>
        <View style={styles.patternGrid}>
          {patternOptions.map((option) => (
            <TouchableOpacity
              key={option.id}
              style={[
                styles.patternCard,
                patternType === option.id && styles.patternCardSelected
              ]}
              onPress={() => setPatternType(option.id as any)}
            >
              <Text style={[
                styles.patternLabel,
                patternType === option.id && styles.patternLabelSelected
              ]}>
                {option.label}
              </Text>
              <Text style={styles.patternDescription}>
                {option.description}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Days of Week (for weekly patterns) */}
      {(patternType === 'weekly' || patternType === 'biweekly') && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Days of Week</Text>
          <View style={styles.daysGrid}>
            {daysOfWeek.map((day) => (
              <TouchableOpacity
                key={day.id}
                style={[
                  styles.dayButton,
                  selectedDays.includes(day.id) && styles.dayButtonSelected
                ]}
                onPress={() => toggleDay(day.id)}
              >
                <Text style={[
                  styles.dayLabel,
                  selectedDays.includes(day.id) && styles.dayLabelSelected
                ]}>
                  {day.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      )}

      {/* Time and Duration */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Time & Duration</Text>
        
        <View style={styles.timeRow}>
          <View style={styles.timeInput}>
            <Clock size={16} color="#B76E79" />
            <Input
              value={timeOfDay}
              onChangeText={setTimeOfDay}
              placeholder="18:00"
              style={styles.timeField}
            />
          </View>
          
          <View style={styles.durationInput}>
            <Text style={styles.durationLabel}>Duration</Text>
            <View style={styles.durationButtons}>
              {[1, 2, 3, 4].map((hours) => (
                <TouchableOpacity
                  key={hours}
                  style={[
                    styles.durationButton,
                    durationHours === hours && styles.durationButtonSelected
                  ]}
                  onPress={() => setDurationHours(hours)}
                >
                  <Text style={[
                    styles.durationButtonText,
                    durationHours === hours && styles.durationButtonTextSelected
                  ]}>
                    {hours}h
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>
      </View>

      {/* Start Date */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Start Date</Text>
        <TouchableOpacity style={styles.dateButton}>
          <Calendar size={16} color="#B76E79" />
          <Text style={styles.dateText}>
            {format(startDate, 'EEEE, MMMM d, yyyy')}
          </Text>
        </TouchableOpacity>
      </View>

      {/* End Conditions */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>End Conditions</Text>
        
        <View style={styles.endConditionRow}>
          <Text style={styles.endConditionLabel}>Set end date</Text>
          <Switch
            value={hasEndDate}
            onValueChange={setHasEndDate}
            trackColor={{ false: '#D1D5DB', true: '#B76E79' }}
            thumbColor={hasEndDate ? '#FFFFFF' : '#F3F4F6'}
          />
        </View>

        {hasEndDate && (
          <TouchableOpacity style={styles.dateButton}>
            <Calendar size={16} color="#B76E79" />
            <Text style={styles.dateText}>
              End: {format(endDate, 'MMMM d, yyyy')}
            </Text>
          </TouchableOpacity>
        )}

        <Input
          label="Maximum bookings (optional)"
          value={maxOccurrences?.toString() || ''}
          onChangeText={(text) => setMaxOccurrences(text ? parseInt(text) : null)}
          placeholder="e.g., 12"
          keyboardType="numeric"
        />
      </View>

      {/* Preview */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Preview</Text>
        <View style={styles.previewCard}>
          <Text style={styles.previewTitle}>Next 5 bookings:</Text>
          {getNextBookingDates().map((date, index) => (
            <Text key={index} style={styles.previewDate}>
              {format(date, 'EEEE, MMM d')} at {timeOfDay} ({durationHours}h)
            </Text>
          ))}
        </View>
      </View>

      {/* Cost Summary */}
      <View style={styles.section}>
        <View style={styles.costCard}>
          <View style={styles.costRow}>
            <Text style={styles.costLabel}>Per booking</Text>
            <Text style={styles.costValue}>${hourlyRate * durationHours}</Text>
          </View>
          <View style={styles.costRow}>
            <Text style={styles.costLabel}>Estimated total</Text>
            <Text style={styles.totalCost}>${calculateTotalCost()}</Text>
          </View>
        </View>
      </View>

      {/* Actions */}
      <View style={styles.actions}>
        <Button
          title="Cancel"
          variant="outline"
          onPress={onCancel}
          style={styles.cancelButton}
        />
        <Button
          title="Create Recurring Booking"
          onPress={handleSubmit}
          style={styles.submitButton}
          disabled={selectedDays.length === 0 && (patternType === 'weekly' || patternType === 'biweekly')}
        />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F3E9',
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1A2E4C',
    marginLeft: 8,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 12,
  },
  patternGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  patternCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    alignItems: 'center',
  },
  patternCardSelected: {
    borderColor: '#B76E79',
    backgroundColor: '#FDF2F8',
  },
  patternLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 4,
  },
  patternLabelSelected: {
    color: '#B76E79',
  },
  patternDescription: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'center',
  },
  daysGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  dayButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    borderWidth: 2,
    borderColor: '#E5E7EB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dayButtonSelected: {
    borderColor: '#B76E79',
    backgroundColor: '#B76E79',
  },
  dayLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6B7280',
  },
  dayLabelSelected: {
    color: '#FFFFFF',
  },
  timeRow: {
    flexDirection: 'row',
    gap: 16,
  },
  timeInput: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  timeField: {
    marginLeft: 8,
    flex: 1,
    marginBottom: 0,
  },
  durationInput: {
    flex: 1,
  },
  durationLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4B5563',
    marginBottom: 8,
  },
  durationButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  durationButton: {
    flex: 1,
    paddingVertical: 8,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    alignItems: 'center',
  },
  durationButtonSelected: {
    borderColor: '#B76E79',
    backgroundColor: '#B76E79',
  },
  durationButtonText: {
    fontSize: 14,
    color: '#6B7280',
  },
  durationButtonTextSelected: {
    color: '#FFFFFF',
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 12,
  },
  dateText: {
    fontSize: 16,
    color: '#1A2E4C',
    marginLeft: 8,
  },
  endConditionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  endConditionLabel: {
    fontSize: 16,
    color: '#1A2E4C',
  },
  previewCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
  },
  previewTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 8,
  },
  previewDate: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
  },
  costCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
  },
  costRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  costLabel: {
    fontSize: 14,
    color: '#6B7280',
  },
  costValue: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1A2E4C',
  },
  totalCost: {
    fontSize: 18,
    fontWeight: '700',
    color: '#B76E79',
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 24,
    marginBottom: 32,
  },
  cancelButton: {
    flex: 1,
  },
  submitButton: {
    flex: 2,
  },
});