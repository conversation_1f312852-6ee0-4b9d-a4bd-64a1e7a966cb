import React, { useState } from 'react';
import { View, Text, StyleSheet, Modal, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { X, Clock, Calendar, MapPin, DollarSign, AlertTriangle } from 'lucide-react-native';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { format, parseISO, addHours } from 'date-fns';
import { formatCurrency } from '@/lib/utils/formatters';

interface BookingModificationModalProps {
  visible: boolean;
  booking: any;
  onClose: () => void;
  onSubmit: (modification: any) => void;
}

export function BookingModificationModal({ 
  visible, 
  booking, 
  onClose, 
  onSubmit 
}: BookingModificationModalProps) {
  const [modificationType, setModificationType] = useState<'time_change' | 'duration_change' | 'location_change' | 'cancellation'>('time_change');
  const [newDate, setNewDate] = useState(booking?.booking_time ? format(parseISO(booking.booking_time), 'yyyy-MM-dd') : '');
  const [newTime, setNewTime] = useState(booking?.booking_time ? format(parseISO(booking.booking_time), 'HH:mm') : '');
  const [newDuration, setNewDuration] = useState(booking?.duration_hours?.toString() || '2');
  const [newLocation, setNewLocation] = useState(booking?.location || '');
  const [reason, setReason] = useState('');
  const [cancellationFee, setCancellationFee] = useState(0);

  const modificationTypes = [
    { id: 'time_change', label: 'Change Time', icon: Clock, description: 'Modify booking date and time' },
    { id: 'duration_change', label: 'Change Duration', icon: Clock, description: 'Extend or reduce booking duration' },
    { id: 'location_change', label: 'Change Location', icon: MapPin, description: 'Update meeting location' },
    { id: 'cancellation', label: 'Cancel Booking', icon: X, description: 'Cancel this booking', danger: true },
  ];

  const handleSubmit = () => {
    let requestedData: any = {};
    let originalData: any = {
      booking_time: booking.booking_time,
      duration_hours: booking.duration_hours,
      location: booking.location,
      total_amount: booking.total_amount,
    };

    switch (modificationType) {
      case 'time_change':
        const newDateTime = new Date(`${newDate}T${newTime}`);
        requestedData = {
          booking_time: newDateTime.toISOString(),
        };
        break;
      
      case 'duration_change':
        const hourlyRate = booking.total_amount / booking.duration_hours;
        requestedData = {
          duration_hours: parseInt(newDuration),
          total_amount: hourlyRate * parseInt(newDuration),
        };
        break;
      
      case 'location_change':
        requestedData = {
          location: newLocation,
        };
        break;
      
      case 'cancellation':
        requestedData = {
          status: 'cancelled',
        };
        break;
    }

    const modification = {
      bookingId: booking.id,
      modificationType,
      originalData,
      requestedData,
      reason,
    };

    onSubmit(modification);
  };

  const calculateNewTotal = () => {
    if (modificationType === 'duration_change') {
      const hourlyRate = booking.total_amount / booking.duration_hours;
      return hourlyRate * parseInt(newDuration || '2');
    }
    return booking.total_amount;
  };

  const getModificationFee = () => {
    // Simple fee calculation - in production this would be more sophisticated
    const hoursUntilBooking = (parseISO(booking.booking_time).getTime() - Date.now()) / (1000 * 60 * 60);
    
    if (hoursUntilBooking < 24) {
      return booking.total_amount * 0.1; // 10% fee for changes within 24 hours
    }
    return 0;
  };

  const renderModificationForm = () => {
    switch (modificationType) {
      case 'time_change':
        return (
          <View style={styles.formSection}>
            <Input
              label="New Date"
              value={newDate}
              onChangeText={setNewDate}
              placeholder="YYYY-MM-DD"
            />
            <Input
              label="New Time"
              value={newTime}
              onChangeText={setNewTime}
              placeholder="HH:MM"
            />
          </View>
        );
      
      case 'duration_change':
        return (
          <View style={styles.formSection}>
            <Text style={styles.formLabel}>New Duration</Text>
            <View style={styles.durationButtons}>
              {[1, 2, 3, 4, 6, 8].map((hours) => (
                <TouchableOpacity
                  key={hours}
                  style={[
                    styles.durationButton,
                    newDuration === hours.toString() && styles.durationButtonSelected
                  ]}
                  onPress={() => setNewDuration(hours.toString())}
                >
                  <Text style={[
                    styles.durationButtonText,
                    newDuration === hours.toString() && styles.durationButtonTextSelected
                  ]}>
                    {hours}h
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
            
            <View style={styles.costComparison}>
              <View style={styles.costRow}>
                <Text style={styles.costLabel}>Original cost:</Text>
                <Text style={styles.costValue}>{formatCurrency(booking.total_amount)}</Text>
              </View>
              <View style={styles.costRow}>
                <Text style={styles.costLabel}>New cost:</Text>
                <Text style={styles.costValue}>{formatCurrency(calculateNewTotal())}</Text>
              </View>
              <View style={styles.costRow}>
                <Text style={styles.costLabel}>Difference:</Text>
                <Text style={[
                  styles.costValue,
                  { color: calculateNewTotal() > booking.total_amount ? '#EF4444' : '#10B981' }
                ]}>
                  {calculateNewTotal() > booking.total_amount ? '+' : ''}
                  {formatCurrency(calculateNewTotal() - booking.total_amount)}
                </Text>
              </View>
            </View>
          </View>
        );
      
      case 'location_change':
        return (
          <View style={styles.formSection}>
            <Input
              label="New Location"
              value={newLocation}
              onChangeText={setNewLocation}
              placeholder="Enter new meeting location"
              multiline
            />
          </View>
        );
      
      case 'cancellation':
        return (
          <View style={styles.formSection}>
            <View style={styles.warningCard}>
              <AlertTriangle size={20} color="#EF4444" />
              <Text style={styles.warningText}>
                This action cannot be undone. Cancellation fees may apply.
              </Text>
            </View>
            
            <View style={styles.cancellationDetails}>
              <View style={styles.costRow}>
                <Text style={styles.costLabel}>Booking total:</Text>
                <Text style={styles.costValue}>{formatCurrency(booking.total_amount)}</Text>
              </View>
              <View style={styles.costRow}>
                <Text style={styles.costLabel}>Cancellation fee:</Text>
                <Text style={styles.costValue}>{formatCurrency(cancellationFee)}</Text>
              </View>
              <View style={styles.costRow}>
                <Text style={styles.costLabel}>Refund amount:</Text>
                <Text style={[styles.costValue, styles.refundAmount]}>
                  {formatCurrency(booking.total_amount - cancellationFee)}
                </Text>
              </View>
            </View>
          </View>
        );
      
      default:
        return null;
    }
  };

  if (!booking) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Modify Booking</Text>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <X size={24} color="#1A2E4C" />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Current Booking Info */}
          <View style={styles.currentBooking}>
            <Text style={styles.currentBookingTitle}>Current Booking</Text>
            <Text style={styles.currentBookingDetails}>
              {format(parseISO(booking.booking_time), 'EEEE, MMMM d, yyyy')} at{' '}
              {format(parseISO(booking.booking_time), 'h:mm a')}
            </Text>
            <Text style={styles.currentBookingDetails}>
              Duration: {booking.duration_hours} hours • {formatCurrency(booking.total_amount)}
            </Text>
            {booking.location && (
              <Text style={styles.currentBookingDetails}>
                Location: {booking.location}
              </Text>
            )}
          </View>

          {/* Modification Type Selection */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>What would you like to change?</Text>
            <View style={styles.typeGrid}>
              {modificationTypes.map((type) => {
                const IconComponent = type.icon;
                return (
                  <TouchableOpacity
                    key={type.id}
                    style={[
                      styles.typeCard,
                      modificationType === type.id && styles.typeCardSelected,
                      type.danger && styles.typeCardDanger,
                      modificationType === type.id && type.danger && styles.typeCardDangerSelected,
                    ]}
                    onPress={() => setModificationType(type.id as any)}
                  >
                    <IconComponent 
                      size={20} 
                      color={
                        modificationType === type.id 
                          ? (type.danger ? '#FFFFFF' : '#B76E79')
                          : (type.danger ? '#EF4444' : '#6B7280')
                      } 
                    />
                    <Text style={[
                      styles.typeLabel,
                      modificationType === type.id && styles.typeLabelSelected,
                      type.danger && styles.typeLabelDanger,
                      modificationType === type.id && type.danger && styles.typeLabelDangerSelected,
                    ]}>
                      {type.label}
                    </Text>
                    <Text style={[
                      styles.typeDescription,
                      modificationType === type.id && styles.typeDescriptionSelected,
                    ]}>
                      {type.description}
                    </Text>
                  </TouchableOpacity>
                );
              })}
            </View>
          </View>

          {/* Modification Form */}
          {renderModificationForm()}

          {/* Reason */}
          <View style={styles.section}>
            <Input
              label="Reason for change (optional)"
              value={reason}
              onChangeText={setReason}
              placeholder="Please explain why you need to make this change..."
              multiline
              style={styles.reasonInput}
            />
          </View>

          {/* Fees */}
          {modificationType !== 'cancellation' && getModificationFee() > 0 && (
            <View style={styles.feeCard}>
              <DollarSign size={16} color="#F59E0B" />
              <Text style={styles.feeText}>
                A modification fee of {formatCurrency(getModificationFee())} may apply for changes within 24 hours.
              </Text>
            </View>
          )}
        </ScrollView>

        {/* Actions */}
        <View style={styles.actions}>
          <Button
            title="Cancel"
            variant="outline"
            onPress={onClose}
            style={styles.cancelButton}
          />
          <Button
            title={modificationType === 'cancellation' ? 'Cancel Booking' : 'Request Change'}
            onPress={handleSubmit}
            style={[
              styles.submitButton,
              modificationType === 'cancellation' && styles.dangerButton
            ]}
          />
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F3E9',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  currentBooking: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  currentBookingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 8,
  },
  currentBookingDetails: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 12,
  },
  typeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  typeCard: {
    width: '48%',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    alignItems: 'center',
  },
  typeCardSelected: {
    borderColor: '#B76E79',
    backgroundColor: '#FDF2F8',
  },
  typeCardDanger: {
    borderColor: '#FEE2E2',
  },
  typeCardDangerSelected: {
    borderColor: '#EF4444',
    backgroundColor: '#EF4444',
  },
  typeLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1A2E4C',
    marginTop: 8,
    marginBottom: 4,
    textAlign: 'center',
  },
  typeLabelSelected: {
    color: '#B76E79',
  },
  typeLabelDanger: {
    color: '#EF4444',
  },
  typeLabelDangerSelected: {
    color: '#FFFFFF',
  },
  typeDescription: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'center',
  },
  typeDescriptionSelected: {
    color: '#B76E79',
  },
  formSection: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  formLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4B5563',
    marginBottom: 8,
  },
  durationButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  durationButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  durationButtonSelected: {
    borderColor: '#B76E79',
    backgroundColor: '#B76E79',
  },
  durationButtonText: {
    fontSize: 14,
    color: '#6B7280',
  },
  durationButtonTextSelected: {
    color: '#FFFFFF',
  },
  costComparison: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 12,
  },
  costRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  costLabel: {
    fontSize: 14,
    color: '#6B7280',
  },
  costValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1A2E4C',
  },
  refundAmount: {
    color: '#10B981',
    fontWeight: '600',
  },
  warningCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEE2E2',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  warningText: {
    fontSize: 14,
    color: '#991B1B',
    marginLeft: 8,
    flex: 1,
  },
  cancellationDetails: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 12,
  },
  reasonInput: {
    height: 80,
    textAlignVertical: 'top',
  },
  feeCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF3C7',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  feeText: {
    fontSize: 14,
    color: '#92400E',
    marginLeft: 8,
    flex: 1,
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  cancelButton: {
    flex: 1,
  },
  submitButton: {
    flex: 2,
  },
  dangerButton: {
    backgroundColor: '#EF4444',
  },
});