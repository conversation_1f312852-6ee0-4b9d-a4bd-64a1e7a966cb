import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { Users, Calendar, Clock, MapPin, DollarSign } from 'lucide-react-native';
import { format, parseISO } from 'date-fns';
import { formatCurrency } from '@/lib/utils/formatters';

interface GroupBookingCardProps {
  groupBooking: {
    id: string;
    booking_time: string;
    duration_hours: number;
    max_participants: number;
    current_participants: number;
    cost_per_person: number;
    status: string;
    description?: string;
    location?: string;
    companions: {
      profiles: {
        full_name: string;
        avatar_url?: string;
      };
    };
    group_booking_participants: Array<{
      profiles: {
        full_name: string;
        avatar_url?: string;
      };
    }>;
  };
  onPress: () => void;
  onJoin?: () => void;
  userParticipating?: boolean;
}

export function GroupBookingCard({ 
  groupBooking, 
  onPress, 
  onJoin, 
  userParticipating = false 
}: GroupBookingCardProps) {
  const bookingDate = parseISO(groupBooking.booking_time);
  const companion = groupBooking.companions.profiles;
  const participants = groupBooking.group_booking_participants;
  
  const getStatusColor = () => {
    switch (groupBooking.status) {
      case 'open':
        return '#10B981';
      case 'full':
        return '#F59E0B';
      case 'confirmed':
        return '#3B82F6';
      case 'completed':
        return '#6B7280';
      case 'cancelled':
        return '#EF4444';
      default:
        return '#6B7280';
    }
  };

  const getStatusText = () => {
    switch (groupBooking.status) {
      case 'open':
        return `${groupBooking.current_participants}/${groupBooking.max_participants} joined`;
      case 'full':
        return 'Group Full';
      case 'confirmed':
        return 'Confirmed';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return groupBooking.status;
    }
  };

  const spotsRemaining = groupBooking.max_participants - groupBooking.current_participants;

  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <View style={styles.header}>
        <View style={styles.companionInfo}>
          {companion.avatar_url ? (
            <Image source={{ uri: companion.avatar_url }} style={styles.avatar} />
          ) : (
            <View style={styles.avatarPlaceholder}>
              <Text style={styles.avatarText}>
                {companion.full_name?.charAt(0) || '?'}
              </Text>
            </View>
          )}
          <View style={styles.companionDetails}>
            <Text style={styles.companionName}>{companion.full_name}</Text>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor() + '20' }]}>
              <Text style={[styles.statusText, { color: getStatusColor() }]}>
                {getStatusText()}
              </Text>
            </View>
          </View>
        </View>
        
        <View style={styles.priceContainer}>
          <Text style={styles.price}>{formatCurrency(groupBooking.cost_per_person)}</Text>
          <Text style={styles.priceLabel}>per person</Text>
        </View>
      </View>

      {groupBooking.description && (
        <Text style={styles.description} numberOfLines={2}>
          {groupBooking.description}
        </Text>
      )}

      <View style={styles.details}>
        <View style={styles.detailRow}>
          <Calendar size={16} color="#B76E79" />
          <Text style={styles.detailText}>
            {format(bookingDate, 'EEEE, MMMM d, yyyy')}
          </Text>
        </View>
        
        <View style={styles.detailRow}>
          <Clock size={16} color="#B76E79" />
          <Text style={styles.detailText}>
            {format(bookingDate, 'h:mm a')} • {groupBooking.duration_hours} hours
          </Text>
        </View>
        
        {groupBooking.location && (
          <View style={styles.detailRow}>
            <MapPin size={16} color="#B76E79" />
            <Text style={styles.detailText}>{groupBooking.location}</Text>
          </View>
        )}
        
        <View style={styles.detailRow}>
          <Users size={16} color="#B76E79" />
          <Text style={styles.detailText}>
            {groupBooking.current_participants} of {groupBooking.max_participants} spots filled
          </Text>
        </View>
      </View>

      {/* Participants Preview */}
      {participants.length > 0 && (
        <View style={styles.participantsSection}>
          <Text style={styles.participantsTitle}>Participants</Text>
          <View style={styles.participantsList}>
            {participants.slice(0, 4).map((participant, index) => (
              <View key={index} style={styles.participantAvatar}>
                {participant.profiles.avatar_url ? (
                  <Image 
                    source={{ uri: participant.profiles.avatar_url }} 
                    style={styles.participantImage} 
                  />
                ) : (
                  <View style={styles.participantPlaceholder}>
                    <Text style={styles.participantText}>
                      {participant.profiles.full_name?.charAt(0) || '?'}
                    </Text>
                  </View>
                )}
              </View>
            ))}
            {participants.length > 4 && (
              <View style={styles.moreParticipants}>
                <Text style={styles.moreText}>+{participants.length - 4}</Text>
              </View>
            )}
          </View>
        </View>
      )}

      {/* Action Button */}
      {!userParticipating && groupBooking.status === 'open' && spotsRemaining > 0 && onJoin && (
        <TouchableOpacity style={styles.joinButton} onPress={onJoin}>
          <Users size={16} color="#FFFFFF" />
          <Text style={styles.joinButtonText}>
            Join Group ({spotsRemaining} spots left)
          </Text>
        </TouchableOpacity>
      )}

      {userParticipating && (
        <View style={styles.participatingBadge}>
          <Text style={styles.participatingText}>You're participating</Text>
        </View>
      )}

      {groupBooking.status === 'full' && (
        <View style={styles.fullBadge}>
          <Text style={styles.fullText}>Group is full</Text>
        </View>
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  companionInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  avatarPlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#B76E79',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  avatarText: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: '600',
  },
  companionDetails: {
    flex: 1,
  },
  companionName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 4,
  },
  statusBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  priceContainer: {
    alignItems: 'flex-end',
  },
  price: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1A2E4C',
  },
  priceLabel: {
    fontSize: 12,
    color: '#6B7280',
  },
  description: {
    fontSize: 14,
    color: '#4B5563',
    lineHeight: 20,
    marginBottom: 12,
  },
  details: {
    gap: 8,
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailText: {
    fontSize: 14,
    color: '#4B5563',
    marginLeft: 8,
  },
  participantsSection: {
    marginBottom: 16,
  },
  participantsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 8,
  },
  participantsList: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  participantAvatar: {
    marginRight: 8,
  },
  participantImage: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  participantPlaceholder: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#B76E79',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  participantText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  moreParticipants: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  moreText: {
    fontSize: 10,
    color: '#6B7280',
    fontWeight: '600',
  },
  joinButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#B76E79',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  joinButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  participatingBadge: {
    backgroundColor: '#D1FAE5',
    borderRadius: 8,
    paddingVertical: 8,
    alignItems: 'center',
  },
  participatingText: {
    color: '#065F46',
    fontSize: 14,
    fontWeight: '500',
  },
  fullBadge: {
    backgroundColor: '#FEF3C7',
    borderRadius: 8,
    paddingVertical: 8,
    alignItems: 'center',
  },
  fullText: {
    color: '#92400E',
    fontSize: 14,
    fontWeight: '500',
  },
});