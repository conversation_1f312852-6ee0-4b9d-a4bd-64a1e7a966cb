import React from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, Image } from 'react-native';
import { MapPin, Star, Shield, Navigation } from 'lucide-react-native';
import { formatCurrency } from '@/lib/utils/formatters';

interface NearbyCompanion {
  id: string;
  name: string;
  distance: number;
  rating: number;
  hourlyRate: number;
  isVerified: boolean;
  avatarUrl?: string;
  location: string;
  isOnline: boolean;
}

interface NearbyCompanionsListProps {
  companions: NearbyCompanion[];
  onCompanionPress: (companionId: string) => void;
  onGetDirections: (companion: NearbyCompanion) => void;
}

export function NearbyCompanionsList({ 
  companions, 
  onCompanionPress, 
  onGetDirections 
}: NearbyCompanionsListProps) {
  const CompanionCard = ({ companion }: { companion: NearbyCompanion }) => (
    <TouchableOpacity
      style={styles.companionCard}
      onPress={() => onCompanionPress(companion.id)}
    >
      <View style={styles.companionHeader}>
        <View style={styles.avatarContainer}>
          {companion.avatarUrl ? (
            <Image source={{ uri: companion.avatarUrl }} style={styles.avatar} />
          ) : (
            <View style={styles.avatarPlaceholder}>
              <Text style={styles.avatarText}>
                {companion.name.charAt(0)}
              </Text>
            </View>
          )}
          {companion.isOnline && <View style={styles.onlineIndicator} />}
        </View>
        
        <View style={styles.companionInfo}>
          <View style={styles.nameRow}>
            <Text style={styles.companionName}>{companion.name}</Text>
            {companion.isVerified && (
              <Shield size={16} color="#10B981" />
            )}
          </View>
          
          <View style={styles.locationRow}>
            <MapPin size={14} color="#6B7280" />
            <Text style={styles.locationText}>
              {companion.distance.toFixed(1)} km away • {companion.location}
            </Text>
          </View>
          
          <View style={styles.ratingRow}>
            <Star size={14} color="#FFB800" fill="#FFB800" />
            <Text style={styles.ratingText}>
              {companion.rating.toFixed(1)} rating
            </Text>
            <Text style={styles.rateText}>
              {formatCurrency(companion.hourlyRate)}/hr
            </Text>
          </View>
        </View>
      </View>
      
      <View style={styles.companionActions}>
        <TouchableOpacity
          style={styles.directionsButton}
          onPress={() => onGetDirections(companion)}
        >
          <Navigation size={16} color="#3B82F6" />
          <Text style={styles.directionsText}>Directions</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.viewButton}>
          <Text style={styles.viewButtonText}>View Profile</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  if (companions.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <MapPin size={48} color="#D1D5DB" />
        <Text style={styles.emptyTitle}>No companions nearby</Text>
        <Text style={styles.emptySubtitle}>
          Try expanding your search radius or check back later
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Nearby Companions</Text>
        <Text style={styles.count}>({companions.length} found)</Text>
      </View>
      
      <FlatList
        data={companions}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => <CompanionCard companion={item} />}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContent}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
  },
  count: {
    fontSize: 14,
    color: '#6B7280',
    marginLeft: 8,
  },
  listContent: {
    paddingBottom: 16,
  },
  companionCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  companionHeader: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 12,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  avatarPlaceholder: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#B76E79',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: '600',
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#10B981',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  companionInfo: {
    flex: 1,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  companionName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
    marginRight: 8,
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  locationText: {
    fontSize: 14,
    color: '#6B7280',
    marginLeft: 4,
  },
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 14,
    color: '#4B5563',
    marginLeft: 4,
    marginRight: 12,
  },
  rateText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#B76E79',
  },
  companionActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  directionsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EBF8FF',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  directionsText: {
    fontSize: 14,
    color: '#3B82F6',
    marginLeft: 4,
    fontWeight: '500',
  },
  viewButton: {
    backgroundColor: '#B76E79',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  viewButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
  },
});