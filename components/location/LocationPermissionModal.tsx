import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal } from 'react-native';
import { MapPin, Shield, Users, AlertTriangle } from 'lucide-react-native';
import { Button } from '@/components/ui/Button';

interface LocationPermissionModalProps {
  visible: boolean;
  onRequestPermission: () => void;
  onClose: () => void;
}

export function LocationPermissionModal({ 
  visible, 
  onRequestPermission, 
  onClose 
}: LocationPermissionModalProps) {
  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.modal}>
          <View style={styles.header}>
            <MapPin size={48} color="#B76E79" />
            <Text style={styles.title}>Location Access</Text>
            <Text style={styles.subtitle}>
              Enable location services to unlock powerful safety and discovery features
            </Text>
          </View>
          
          <View style={styles.features}>
            <View style={styles.feature}>
              <Users size={24} color="#3B82F6" />
              <View style={styles.featureText}>
                <Text style={styles.featureTitle}>Find Nearby Companions</Text>
                <Text style={styles.featureDescription}>
                  Discover companions in your area for spontaneous meetups
                </Text>
              </View>
            </View>
            
            <View style={styles.feature}>
              <Shield size={24} color="#10B981" />
              <View style={styles.featureText}>
                <Text style={styles.featureTitle}>Safety Features</Text>
                <Text style={styles.featureDescription}>
                  Emergency SOS, location sharing, and safety zone alerts
                </Text>
              </View>
            </View>
            
            <View style={styles.feature}>
              <AlertTriangle size={24} color="#F59E0B" />
              <View style={styles.featureText}>
                <Text style={styles.featureTitle}>Emergency Assistance</Text>
                <Text style={styles.featureDescription}>
                  Automatic location sharing during emergencies
                </Text>
              </View>
            </View>
          </View>
          
          <View style={styles.privacy}>
            <Text style={styles.privacyTitle}>Your Privacy Matters</Text>
            <Text style={styles.privacyText}>
              • Location data is encrypted and secure{'\n'}
              • You control when and with whom to share{'\n'}
              • Can be disabled at any time in settings
            </Text>
          </View>
          
          <View style={styles.actions}>
            <Button
              title="Enable Location"
              onPress={onRequestPermission}
              style={styles.enableButton}
            />
            <TouchableOpacity style={styles.skipButton} onPress={onClose}>
              <Text style={styles.skipText}>Skip for now</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modal: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 24,
    width: '100%',
    maxWidth: 400,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1A2E4C',
    marginTop: 16,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 22,
  },
  features: {
    marginBottom: 24,
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  featureText: {
    marginLeft: 12,
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 18,
  },
  privacy: {
    backgroundColor: '#F7F3E9',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  privacyTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    marginBottom: 8,
  },
  privacyText: {
    fontSize: 14,
    color: '#4B5563',
    lineHeight: 20,
  },
  actions: {
    gap: 12,
  },
  enableButton: {
    backgroundColor: '#B76E79',
  },
  skipButton: {
    paddingVertical: 12,
    alignItems: 'center',
  },
  skipText: {
    fontSize: 16,
    color: '#6B7280',
  },
});