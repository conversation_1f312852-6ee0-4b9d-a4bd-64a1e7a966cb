import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Phone, Mail, Edit3, Trash2, Star } from 'lucide-react-native';
import { EmergencyContact } from '@/lib/emergency/emergency-service';

interface EmergencyContactCardProps {
  contact: EmergencyContact;
  onEdit: (contact: EmergencyContact) => void;
  onDelete: (contactId: string) => void;
  onCall: (phoneNumber: string) => void;
}

export function EmergencyContactCard({ 
  contact, 
  onEdit, 
  onDelete, 
  onCall 
}: EmergencyContactCardProps) {
  const getRelationshipColor = (relationship: string) => {
    switch (relationship) {
      case 'family':
        return '#10B981';
      case 'medical':
        return '#EF4444';
      case 'partner':
        return '#F59E0B';
      case 'friend':
        return '#3B82F6';
      default:
        return '#6B7280';
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.contactInfo}>
          <View style={styles.nameRow}>
            <Text style={styles.name}>{contact.name}</Text>
            {contact.isPrimary && (
              <View style={styles.primaryBadge}>
                <Star size={12} color="#F59E0B" fill="#F59E0B" />
                <Text style={styles.primaryText}>Primary</Text>
              </View>
            )}
          </View>
          <View style={[
            styles.relationshipBadge,
            { backgroundColor: getRelationshipColor(contact.relationship) + '20' }
          ]}>
            <Text style={[
              styles.relationshipText,
              { color: getRelationshipColor(contact.relationship) }
            ]}>
              {contact.relationship}
            </Text>
          </View>
        </View>
        
        <View style={styles.actions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => onEdit(contact)}
          >
            <Edit3 size={16} color="#6B7280" />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.actionButton, styles.deleteButton]}
            onPress={() => onDelete(contact.id)}
          >
            <Trash2 size={16} color="#EF4444" />
          </TouchableOpacity>
        </View>
      </View>
      
      <View style={styles.contactDetails}>
        <TouchableOpacity
          style={styles.contactMethod}
          onPress={() => onCall(contact.phoneNumber)}
        >
          <Phone size={16} color="#3B82F6" />
          <Text style={styles.contactMethodText}>{contact.phoneNumber}</Text>
        </TouchableOpacity>
        
        {contact.email && (
          <View style={styles.contactMethod}>
            <Mail size={16} color="#6B7280" />
            <Text style={styles.contactMethodText}>{contact.email}</Text>
          </View>
        )}
      </View>
      
      {contact.notes && (
        <View style={styles.notes}>
          <Text style={styles.notesText}>{contact.notes}</Text>
        </View>
      )}
      
      {contact.isMedical && (
        <View style={styles.medicalBadge}>
          <Text style={styles.medicalText}>Medical Contact</Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  contactInfo: {
    flex: 1,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  name: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
    marginRight: 8,
  },
  primaryBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF3C7',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  primaryText: {
    fontSize: 10,
    color: '#92400E',
    fontWeight: '500',
    marginLeft: 4,
  },
  relationshipBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  relationshipText: {
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  actions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  deleteButton: {
    backgroundColor: '#FEE2E2',
  },
  contactDetails: {
    gap: 8,
    marginBottom: 12,
  },
  contactMethod: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  contactMethodText: {
    fontSize: 14,
    color: '#4B5563',
    marginLeft: 8,
  },
  notes: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  notesText: {
    fontSize: 14,
    color: '#6B7280',
    fontStyle: 'italic',
  },
  medicalBadge: {
    backgroundColor: '#FEE2E2',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: 'flex-start',
  },
  medicalText: {
    fontSize: 12,
    color: '#991B1B',
    fontWeight: '500',
  },
});