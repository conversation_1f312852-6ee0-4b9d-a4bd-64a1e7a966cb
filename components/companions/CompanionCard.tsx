import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, Dimensions } from 'react-native';
import { Heart, Star, MapPin } from 'lucide-react-native';
import { VerificationBadge } from '@/components/verification/VerificationBadge';

interface CompanionCardProps {
  companion: any;
}

const { width } = Dimensions.get('window');
const CARD_WIDTH = width - 32; // Full width with padding

export function CompanionCard({ companion }: CompanionCardProps) {
  const profile = companion.profiles;
  
  return (
    <View style={styles.container}>
      <View style={styles.imageContainer}>
        <Image
          source={{ uri: profile.avatar_url || 'https://images.pexels.com/photos/1065084/pexels-photo-1065084.jpeg' }}
          style={styles.image}
        />
        <TouchableOpacity style={styles.favoriteButton}>
          <Heart size={18} color="#FFFFFF" />
        </TouchableOpacity>
        
        <View style={styles.priceTag}>
          <Text style={styles.priceText}>${companion.hourly_rate || 60}/hr</Text>
        </View>
        
        {/* Verification badges */}
        {companion.verification_status === 'verified' && (
          <View style={styles.verificationBadge}>
            <VerificationBadge type="verified" size="small" showText={false} />
          </View>
        )}
      </View>
      
      <View style={styles.contentContainer}>
        <View style={styles.header}>
          <Text style={styles.name}>{profile.full_name}</Text>
          <View style={styles.ratingContainer}>
            <Star size={14} color="#FFB800" fill="#FFB800" />
            <Text style={styles.rating}>{companion.rating || 4.7}</Text>
          </View>
        </View>
        
        <View style={styles.locationContainer}>
          <MapPin size={14} color="#B76E79" />
          <Text style={styles.location}>{companion.location || 'New York, NY'}</Text>
        </View>
        
        <Text style={styles.bio} numberOfLines={2}>
          {companion.bio || 'Friendly and outgoing companion available for dinner dates, events, and meaningful conversations.'}
        </Text>
        
        <View style={styles.tagsContainer}>
          {(companion.interests_array || ['Movies', 'Music', 'Hiking']).slice(0, 3).map((interest: string, index: number) => (
            <View key={index} style={styles.tag}>
              <Text style={styles.tagText}>{interest}</Text>
            </View>
          ))}
        </View>
        
        {/* Verification status */}
        <View style={styles.verificationContainer}>
          {companion.verification_status === 'verified' && (
            <VerificationBadge type="verified\" size="small" />
          )}
          {companion.is_verified && (
            <VerificationBadge type="background_checked\" size="small" />
          )}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: CARD_WIDTH,
    borderRadius: 12,
    backgroundColor: '#FFFFFF',
    overflow: 'hidden',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  imageContainer: {
    position: 'relative',
    height: 200,
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  favoriteButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  priceTag: {
    position: 'absolute',
    bottom: 12,
    right: 12,
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#1A2E4C',
    borderRadius: 16,
  },
  priceText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  verificationBadge: {
    position: 'absolute',
    top: 12,
    left: 12,
  },
  contentContainer: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  name: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A2E4C',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rating: {
    fontSize: 14,
    color: '#4B5563',
    marginLeft: 4,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  location: {
    fontSize: 14,
    color: '#6B7280',
    marginLeft: 4,
  },
  bio: {
    fontSize: 14,
    lineHeight: 20,
    color: '#4B5563',
    marginBottom: 12,
  },
  tagsContainer: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  tag: {
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 16,
    marginRight: 8,
  },
  tagText: {
    fontSize: 12,
    color: '#4B5563',
  },
  verificationContainer: {
    flexDirection: 'row',
    gap: 8,
  },
});