import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { FileText, Eye, ThumbsUp } from 'lucide-react-native';

interface HelpArticleCardProps {
  article: {
    id: string;
    title: string;
    category: string;
    view_count: number;
    helpful_count: number;
    tags: string[];
  };
  onPress: () => void;
}

export function HelpArticleCard({ article, onPress }: HelpArticleCardProps) {
  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <View style={styles.header}>
        <FileText size={20} color="#B76E79" />
        <View style={styles.category}>
          <Text style={styles.categoryText}>{article.category}</Text>
        </View>
      </View>
      
      <Text style={styles.title} numberOfLines={2}>
        {article.title}
      </Text>
      
      <View style={styles.tags}>
        {article.tags.slice(0, 3).map((tag, index) => (
          <View key={index} style={styles.tag}>
            <Text style={styles.tagText}>{tag}</Text>
          </View>
        ))}
      </View>
      
      <View style={styles.footer}>
        <View style={styles.stat}>
          <Eye size={14} color="#6B7280" />
          <Text style={styles.statText}>{article.view_count}</Text>
        </View>
        <View style={styles.stat}>
          <ThumbsUp size={14} color="#6B7280" />
          <Text style={styles.statText}>{article.helpful_count}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  category: {
    backgroundColor: '#F7F3E9',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  categoryText: {
    fontSize: 12,
    color: '#B76E79',
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    lineHeight: 22,
    marginBottom: 12,
  },
  tags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 12,
  },
  tag: {
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    marginRight: 6,
    marginBottom: 4,
  },
  tagText: {
    fontSize: 12,
    color: '#6B7280',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  stat: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    fontSize: 12,
    color: '#6B7280',
    marginLeft: 4,
  },
});