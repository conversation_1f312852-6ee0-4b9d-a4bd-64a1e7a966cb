import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { <PERSON>, CircleCheck as CheckCircle, CircleAlert as AlertCircle, Circle as XCircle } from 'lucide-react-native';
import { format } from 'date-fns';

interface SupportTicketCardProps {
  ticket: {
    id: string;
    title: string;
    description: string;
    category: string;
    priority: string;
    status: string;
    created_at: string;
    updated_at: string;
  };
  onPress: () => void;
}

export function SupportTicketCard({ ticket, onPress }: SupportTicketCardProps) {
  const getStatusIcon = () => {
    switch (ticket.status) {
      case 'open':
        return <Clock size={16} color="#3B82F6" />;
      case 'in_progress':
        return <Clock size={16} color="#F59E0B" />;
      case 'resolved':
        return <CheckCircle size={16} color="#10B981" />;
      case 'closed':
        return <XCircle size={16} color="#6B7280" />;
      default:
        return <AlertCircle size={16} color="#6B7280" />;
    }
  };

  const getStatusColor = () => {
    switch (ticket.status) {
      case 'open':
        return '#3B82F6';
      case 'in_progress':
        return '#F59E0B';
      case 'resolved':
        return '#10B981';
      case 'closed':
        return '#6B7280';
      default:
        return '#6B7280';
    }
  };

  const getPriorityColor = () => {
    switch (ticket.priority) {
      case 'urgent':
        return '#EF4444';
      case 'high':
        return '#F59E0B';
      case 'medium':
        return '#3B82F6';
      case 'low':
        return '#6B7280';
      default:
        return '#6B7280';
    }
  };

  const getCategoryLabel = () => {
    switch (ticket.category) {
      case 'technical':
        return 'Technical';
      case 'billing':
        return 'Billing';
      case 'safety':
        return 'Safety';
      case 'account':
        return 'Account';
      case 'booking':
        return 'Booking';
      case 'general':
        return 'General';
      default:
        return ticket.category;
    }
  };

  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <View style={styles.header}>
        <Text style={styles.title} numberOfLines={1}>
          {ticket.title}
        </Text>
        <View style={styles.status}>
          {getStatusIcon()}
          <Text style={[styles.statusText, { color: getStatusColor() }]}>
            {ticket.status.replace('_', ' ')}
          </Text>
        </View>
      </View>
      
      <Text style={styles.description} numberOfLines={2}>
        {ticket.description}
      </Text>
      
      <View style={styles.footer}>
        <View style={styles.badges}>
          <View style={[styles.badge, styles.categoryBadge]}>
            <Text style={styles.categoryText}>{getCategoryLabel()}</Text>
          </View>
          <View style={[styles.badge, { backgroundColor: getPriorityColor() + '20' }]}>
            <Text style={[styles.priorityText, { color: getPriorityColor() }]}>
              {ticket.priority}
            </Text>
          </View>
        </View>
        
        <Text style={styles.date}>
          {format(new Date(ticket.created_at), 'MMM d, yyyy')}
        </Text>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A2E4C',
    flex: 1,
    marginRight: 12,
  },
  status: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
    textTransform: 'capitalize',
  },
  description: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
    marginBottom: 12,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  badges: {
    flexDirection: 'row',
    flex: 1,
  },
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
  },
  categoryBadge: {
    backgroundColor: '#F3F4F6',
  },
  categoryText: {
    fontSize: 12,
    color: '#4B5563',
    fontWeight: '500',
  },
  priorityText: {
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  date: {
    fontSize: 12,
    color: '#9CA3AF',
  },
});