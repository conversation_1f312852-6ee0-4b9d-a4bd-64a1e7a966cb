import { createClient } from '@supabase/supabase-js';

export interface ActivityPreference {
  id: string;
  name: string;
  category: 'dining' | 'entertainment' | 'outdoor' | 'cultural' | 'sports' | 'nightlife' | 'relaxation' | 'adventure' | 'educational' | 'social';
  description: string;
  icon: string;
  isPopular: boolean;
}

export interface DateTypePreference {
  id: string;
  name: string;
  description: string;
  duration: 'short' | 'medium' | 'long' | 'extended';
  priceRange: 'budget' | 'moderate' | 'premium' | 'luxury';
  activities: string[];
}

export interface CustomizationProfile {
  id: string;
  userId: string;
  preferredActivities: string[];
  preferredDateTypes: string[];
  specialRequests: string[];
  dietaryRestrictions: string[];
  accessibilityNeeds: string[];
  communicationStyle: 'casual' | 'formal' | 'friendly' | 'professional';
  interactionLevel: 'minimal' | 'moderate' | 'high' | 'very_high';
  privacyLevel: 'public' | 'friends' | 'private' | 'anonymous';
  customInstructions: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface EventPlanningRequest {
  id: string;
  userId: string;
  companionId?: string;
  eventType: 'birthday' | 'anniversary' | 'date_night' | 'business' | 'celebration' | 'vacation' | 'custom';
  eventDate: Date;
  duration: number; // hours
  budget: number;
  guestCount: number;
  specialRequests: string[];
  venuePreferences: string[];
  activityPreferences: string[];
  status: 'draft' | 'submitted' | 'planning' | 'confirmed' | 'completed' | 'cancelled';
  plannerNotes: string;
  createdAt: Date;
}

export class CustomizationService {
  private static instance: CustomizationService;
  private supabase: any;

  static getInstance(): CustomizationService {
    if (!CustomizationService.instance) {
      CustomizationService.instance = new CustomizationService();
    }
    return CustomizationService.instance;
  }

  initialize(supabaseClient: any): void {
    this.supabase = supabaseClient;
  }

  // Activity Preferences
  async getAvailableActivities(): Promise<ActivityPreference[]> {
    try {
      const { data, error } = await this.supabase
        .from('activity_preferences')
        .select('*')
        .order('is_popular', { ascending: false })
        .order('name');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching activities:', error);
      return this.getDefaultActivities();
    }
  }

  private getDefaultActivities(): ActivityPreference[] {
    return [
      { id: '1', name: 'Fine Dining', category: 'dining', description: 'Upscale restaurant experiences', icon: '🍽️', isPopular: true },
      { id: '2', name: 'Movies', category: 'entertainment', description: 'Cinema and film experiences', icon: '🎬', isPopular: true },
      { id: '3', name: 'Museums', category: 'cultural', description: 'Art and history museums', icon: '🏛️', isPopular: false },
      { id: '4', name: 'Hiking', category: 'outdoor', description: 'Nature walks and hiking trails', icon: '🥾', isPopular: false },
      { id: '5', name: 'Concerts', category: 'entertainment', description: 'Live music performances', icon: '🎵', isPopular: true },
      { id: '6', name: 'Shopping', category: 'social', description: 'Retail therapy and browsing', icon: '🛍️', isPopular: true },
      { id: '7', name: 'Spa & Wellness', category: 'relaxation', description: 'Relaxation and wellness activities', icon: '💆', isPopular: false },
      { id: '8', name: 'Sports Events', category: 'sports', description: 'Live sporting events', icon: '⚽', isPopular: false },
      { id: '9', name: 'Nightlife', category: 'nightlife', description: 'Bars, clubs, and nighttime entertainment', icon: '🍸', isPopular: true },
      { id: '10', name: 'Adventure Sports', category: 'adventure', description: 'Thrilling outdoor activities', icon: '🏄', isPopular: false }
    ];
  }

  // Date Type Preferences
  async getAvailableDateTypes(): Promise<DateTypePreference[]> {
    try {
      const { data, error } = await this.supabase
        .from('date_type_preferences')
        .select('*')
        .order('name');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching date types:', error);
      return this.getDefaultDateTypes();
    }
  }

  private getDefaultDateTypes(): DateTypePreference[] {
    return [
      {
        id: '1',
        name: 'Coffee Date',
        description: 'Casual coffee meetup',
        duration: 'short',
        priceRange: 'budget',
        activities: ['coffee', 'conversation']
      },
      {
        id: '2',
        name: 'Dinner Date',
        description: 'Romantic dinner experience',
        duration: 'medium',
        priceRange: 'moderate',
        activities: ['dining', 'conversation']
      },
      {
        id: '3',
        name: 'Adventure Date',
        description: 'Outdoor activities and exploration',
        duration: 'long',
        priceRange: 'moderate',
        activities: ['outdoor', 'adventure', 'sports']
      },
      {
        id: '4',
        name: 'Cultural Experience',
        description: 'Museums, galleries, and cultural sites',
        duration: 'medium',
        priceRange: 'moderate',
        activities: ['cultural', 'educational']
      },
      {
        id: '5',
        name: 'Luxury Experience',
        description: 'High-end dining and entertainment',
        duration: 'extended',
        priceRange: 'luxury',
        activities: ['dining', 'entertainment', 'luxury']
      }
    ];
  }

  // Customization Profile Management
  async getCustomizationProfile(userId: string): Promise<CustomizationProfile | null> {
    try {
      const { data, error } = await this.supabase
        .from('customization_profiles')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return data;
    } catch (error) {
      console.error('Error fetching customization profile:', error);
      return null;
    }
  }

  async updateCustomizationProfile(userId: string, profile: Partial<CustomizationProfile>): Promise<CustomizationProfile> {
    try {
      const { data, error } = await this.supabase
        .from('customization_profiles')
        .upsert({
          user_id: userId,
          ...profile,
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating customization profile:', error);
      throw error;
    }
  }

  // Event Planning
  async createEventPlanningRequest(request: Omit<EventPlanningRequest, 'id' | 'createdAt'>): Promise<EventPlanningRequest> {
    try {
      const { data, error } = await this.supabase
        .from('event_planning_requests')
        .insert({
          ...request,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating event planning request:', error);
      throw error;
    }
  }

  async getEventPlanningRequests(userId: string): Promise<EventPlanningRequest[]> {
    try {
      const { data, error } = await this.supabase
        .from('event_planning_requests')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching event planning requests:', error);
      return [];
    }
  }

  async updateEventPlanningRequest(requestId: string, updates: Partial<EventPlanningRequest>): Promise<EventPlanningRequest> {
    try {
      const { data, error } = await this.supabase
        .from('event_planning_requests')
        .update(updates)
        .eq('id', requestId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating event planning request:', error);
      throw error;
    }
  }

  // Activity Recommendations
  async getPersonalizedActivityRecommendations(userId: string, companionId?: string): Promise<ActivityPreference[]> {
    try {
      const profile = await this.getCustomizationProfile(userId);
      const activities = await this.getAvailableActivities();

      if (!profile) return activities.filter(a => a.isPopular);

      // Filter based on user preferences
      const recommended = activities.filter(activity => {
        return profile.preferredActivities.includes(activity.id) ||
               profile.preferredActivities.some(pref => activity.category === pref);
      });

      // If companion is specified, consider their preferences too
      if (companionId) {
        // This would integrate with companion preferences
        // For now, return user preferences
      }

      return recommended.length > 0 ? recommended : activities.filter(a => a.isPopular);
    } catch (error) {
      console.error('Error getting activity recommendations:', error);
      return [];
    }
  }

  // Anonymous Browsing Support
  async enableAnonymousMode(userId: string): Promise<void> {
    try {
      await this.updateCustomizationProfile(userId, {
        privacyLevel: 'anonymous'
      });
    } catch (error) {
      console.error('Error enabling anonymous mode:', error);
      throw error;
    }
  }

  async disableAnonymousMode(userId: string): Promise<void> {
    try {
      await this.updateCustomizationProfile(userId, {
        privacyLevel: 'private'
      });
    } catch (error) {
      console.error('Error disabling anonymous mode:', error);
      throw error;
    }
  }

  async isAnonymousModeEnabled(userId: string): Promise<boolean> {
    try {
      const profile = await this.getCustomizationProfile(userId);
      return profile?.privacyLevel === 'anonymous';
    } catch (error) {
      console.error('Error checking anonymous mode:', error);
      return false;
    }
  }
}
