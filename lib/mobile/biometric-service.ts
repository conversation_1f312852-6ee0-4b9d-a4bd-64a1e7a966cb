import { Platform } from 'react-native';
import * as LocalAuthentication from 'expo-local-authentication';
import * as SecureStore from 'expo-secure-store';

export interface BiometricCapabilities {
  isAvailable: boolean;
  supportedTypes: string[];
  isEnrolled: boolean;
  securityLevel: 'none' | 'biometric' | 'passcode';
}

export interface BiometricSettings {
  enabled: boolean;
  biometricType?: string;
  deviceId?: string;
  lastUsed?: Date;
}

export class BiometricService {
  private static instance: BiometricService;
  private supabase: any;

  static getInstance(): BiometricService {
    if (!BiometricService.instance) {
      BiometricService.instance = new BiometricService();
    }
    return BiometricService.instance;
  }

  initialize(supabaseClient: any): void {
    this.supabase = supabaseClient;
  }

  async checkBiometricCapabilities(): Promise<BiometricCapabilities> {
    if (Platform.OS === 'web') {
      return {
        isAvailable: false,
        supportedTypes: [],
        isEnrolled: false,
        securityLevel: 'none',
      };
    }

    try {
      const isAvailable = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      const supportedTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();
      const securityLevel = await LocalAuthentication.getEnrolledLevelAsync();

      const typeNames = supportedTypes.map(type => {
        switch (type) {
          case LocalAuthentication.AuthenticationType.FINGERPRINT:
            return 'fingerprint';
          case LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION:
            return 'face_id';
          case LocalAuthentication.AuthenticationType.IRIS:
            return 'iris';
          default:
            return 'unknown';
        }
      });

      return {
        isAvailable,
        supportedTypes: typeNames,
        isEnrolled,
        securityLevel: this.mapSecurityLevel(securityLevel),
      };
    } catch (error) {
      console.error('Error checking biometric capabilities:', error);
      return {
        isAvailable: false,
        supportedTypes: [],
        isEnrolled: false,
        securityLevel: 'none',
      };
    }
  }

  private mapSecurityLevel(level: LocalAuthentication.SecurityLevel): 'none' | 'biometric' | 'passcode' {
    switch (level) {
      case LocalAuthentication.SecurityLevel.NONE:
        return 'none';
      case LocalAuthentication.SecurityLevel.SECRET:
      case LocalAuthentication.SecurityLevel.BIOMETRIC:
        return 'biometric';
      default:
        return 'passcode';
    }
  }

  async enableBiometricAuth(userId: string): Promise<boolean> {
    if (Platform.OS === 'web') {
      console.warn('Biometric authentication not available on web');
      return false;
    }

    try {
      const capabilities = await this.checkBiometricCapabilities();
      
      if (!capabilities.isAvailable || !capabilities.isEnrolled) {
        throw new Error('Biometric authentication not available or not set up');
      }

      // Test biometric authentication
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Enable biometric authentication for HourlyGF',
        cancelLabel: 'Cancel',
        fallbackLabel: 'Use passcode',
      });

      if (result.success) {
        // Store biometric preference
        await SecureStore.setItemAsync('biometric_enabled', 'true');
        
        // Update database
        await this.supabase
          .from('biometric_settings')
          .upsert({
            user_id: userId,
            biometric_enabled: true,
            biometric_type: capabilities.supportedTypes[0],
            device_id: await this.getDeviceId(),
            updated_at: new Date().toISOString(),
          });

        return true;
      }

      return false;
    } catch (error) {
      console.error('Error enabling biometric auth:', error);
      return false;
    }
  }

  async disableBiometricAuth(userId: string): Promise<void> {
    try {
      await SecureStore.deleteItemAsync('biometric_enabled');
      
      await this.supabase
        .from('biometric_settings')
        .update({
          biometric_enabled: false,
          updated_at: new Date().toISOString(),
        })
        .eq('user_id', userId);
    } catch (error) {
      console.error('Error disabling biometric auth:', error);
    }
  }

  async authenticateWithBiometrics(): Promise<boolean> {
    if (Platform.OS === 'web') {
      return false;
    }

    try {
      const isEnabled = await SecureStore.getItemAsync('biometric_enabled');
      if (isEnabled !== 'true') {
        return false;
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Authenticate to access HourlyGF',
        cancelLabel: 'Cancel',
        fallbackLabel: 'Use passcode',
      });

      if (result.success) {
        // Update last used timestamp
        const userId = await this.getCurrentUserId();
        if (userId) {
          await this.supabase
            .from('biometric_settings')
            .update({
              last_used_at: new Date().toISOString(),
            })
            .eq('user_id', userId);
        }
      }

      return result.success;
    } catch (error) {
      console.error('Error authenticating with biometrics:', error);
      return false;
    }
  }

  async isBiometricEnabled(userId: string): Promise<boolean> {
    try {
      if (Platform.OS === 'web') {
        return false;
      }

      const localSetting = await SecureStore.getItemAsync('biometric_enabled');
      if (localSetting !== 'true') {
        return false;
      }

      const { data, error } = await this.supabase
        .from('biometric_settings')
        .select('biometric_enabled')
        .eq('user_id', userId)
        .maybeSingle();

      if (error && error.code !== 'PGRST116') throw error;
      return data?.biometric_enabled || false;
    } catch (error) {
      console.error('Error checking biometric status:', error);
      return false;
    }
  }

  private async getDeviceId(): Promise<string> {
    try {
      let deviceId = await SecureStore.getItemAsync('device_id');
      if (!deviceId) {
        deviceId = `device_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        await SecureStore.setItemAsync('device_id', deviceId);
      }
      return deviceId;
    } catch (error) {
      console.error('Error getting device ID:', error);
      return 'unknown_device';
    }
  }

  private async getCurrentUserId(): Promise<string | null> {
    try {
      const { data: { session } } = await this.supabase.auth.getSession();
      return session?.user?.id || null;
    } catch (error) {
      console.error('Error getting current user ID:', error);
      return null;
    }
  }
}