import { Platform } from 'react-native';
import * as Linking from 'expo-linking';

export interface DeepLink {
  id: string;
  linkType: 'companion_profile' | 'booking_invite' | 'referral' | 'group_booking';
  targetId: string;
  shortCode: string;
  clickCount: number;
  uniqueClicks: number;
  conversionCount: number;
  expiresAt?: Date;
}

export interface AppShortcut {
  id: string;
  type: 'quick_book' | 'emergency_contact' | 'favorite_companion' | 'recent_chat' | 'custom';
  title: string;
  subtitle?: string;
  iconName: string;
  actionData: any;
  usageCount: number;
}

export class DeepLinkService {
  private static instance: DeepLinkService;
  private supabase: any;
  private baseUrl = 'https://hourlygf.com';

  static getInstance(): DeepLinkService {
    if (!DeepLinkService.instance) {
      DeepLinkService.instance = new DeepLinkService();
    }
    return DeepLinkService.instance;
  }

  initialize(supabaseClient: any): void {
    this.supabase = supabaseClient;
    this.setupDeepLinkHandling();
  }

  private setupDeepLinkHandling(): void {
    if (Platform.OS !== 'web') {
      // Handle initial URL when app is opened from a deep link
      Linking.getInitialURL().then(url => {
        if (url) {
          this.handleDeepLink(url);
        }
      });

      // Handle deep links when app is already running
      const subscription = Linking.addEventListener('url', ({ url }) => {
        this.handleDeepLink(url);
      });

      return () => subscription?.remove();
    }
  }

  private async handleDeepLink(url: string): Promise<void> {
    try {
      const { hostname, path, queryParams } = Linking.parse(url);
      
      if (hostname !== 'hourlygf.com' && hostname !== 'app.hourlygf.com') {
        return;
      }

      // Track deep link click
      await this.trackDeepLinkClick(url);

      // Route based on path
      if (path?.startsWith('/companion/')) {
        const companionId = path.split('/')[2];
        this.navigateToCompanion(companionId);
      } else if (path?.startsWith('/booking/')) {
        const bookingId = path.split('/')[2];
        this.navigateToBooking(bookingId);
      } else if (path?.startsWith('/group/')) {
        const groupId = path.split('/')[2];
        this.navigateToGroupBooking(groupId);
      } else if (path?.startsWith('/referral/')) {
        const referralCode = queryParams?.code as string;
        this.handleReferral(referralCode);
      }
    } catch (error) {
      console.error('Error handling deep link:', error);
    }
  }

  // Create Deep Links
  async createCompanionProfileLink(
    companionId: string,
    createdBy?: string,
    expiresIn?: number
  ): Promise<string> {
    try {
      const shortCode = this.generateShortCode();
      const expiresAt = expiresIn ? new Date(Date.now() + expiresIn * 1000) : null;

      await this.supabase
        .from('deep_links')
        .insert({
          link_type: 'companion_profile',
          target_id: companionId,
          short_code: shortCode,
          created_by: createdBy,
          expires_at: expiresAt?.toISOString(),
        });

      return `${this.baseUrl}/c/${shortCode}`;
    } catch (error) {
      console.error('Error creating companion profile link:', error);
      throw error;
    }
  }

  async createGroupBookingInvite(
    groupBookingId: string,
    createdBy: string,
    expiresIn = 86400 // 24 hours
  ): Promise<string> {
    try {
      const shortCode = this.generateShortCode();
      const expiresAt = new Date(Date.now() + expiresIn * 1000);

      await this.supabase
        .from('deep_links')
        .insert({
          link_type: 'group_booking',
          target_id: groupBookingId,
          short_code: shortCode,
          created_by: createdBy,
          expires_at: expiresAt.toISOString(),
        });

      return `${this.baseUrl}/group/${shortCode}`;
    } catch (error) {
      console.error('Error creating group booking invite:', error);
      throw error;
    }
  }

  async createReferralLink(userId: string, referralCode: string): Promise<string> {
    try {
      const shortCode = this.generateShortCode();

      await this.supabase
        .from('deep_links')
        .insert({
          link_type: 'referral',
          target_id: userId,
          short_code: shortCode,
          created_by: userId,
          metadata: { referral_code: referralCode },
        });

      return `${this.baseUrl}/join/${shortCode}?code=${referralCode}`;
    } catch (error) {
      console.error('Error creating referral link:', error);
      throw error;
    }
  }

  private generateShortCode(): string {
    return Math.random().toString(36).substring(2, 8).toUpperCase();
  }

  private async trackDeepLinkClick(url: string): Promise<void> {
    try {
      const shortCode = this.extractShortCode(url);
      if (shortCode) {
        await this.supabase
          .from('deep_links')
          .update({
            click_count: this.supabase.raw('click_count + 1'),
          })
          .eq('short_code', shortCode);
      }
    } catch (error) {
      console.error('Error tracking deep link click:', error);
    }
  }

  private extractShortCode(url: string): string | null {
    const match = url.match(/\/[a-zA-Z]+\/([A-Z0-9]{6})/);
    return match ? match[1] : null;
  }

  // Navigation handlers (these would integrate with your navigation system)
  private navigateToCompanion(companionId: string): void {
    console.log('Navigate to companion:', companionId);
    // Implement navigation to companion profile
  }

  private navigateToBooking(bookingId: string): void {
    console.log('Navigate to booking:', bookingId);
    // Implement navigation to booking details
  }

  private navigateToGroupBooking(groupId: string): void {
    console.log('Navigate to group booking:', groupId);
    // Implement navigation to group booking
  }

  private handleReferral(referralCode: string): void {
    console.log('Handle referral:', referralCode);
    // Implement referral code processing
  }

  // App Shortcuts Management
  async createAppShortcut(
    userId: string,
    type: AppShortcut['type'],
    title: string,
    actionData: any,
    subtitle?: string,
    iconName?: string
  ): Promise<AppShortcut> {
    try {
      const { data, error } = await this.supabase
        .from('app_shortcuts')
        .insert({
          user_id: userId,
          shortcut_type: type,
          title,
          subtitle,
          icon_name: iconName || this.getDefaultIcon(type),
          action_data: actionData,
        })
        .select()
        .single();

      if (error) throw error;

      return {
        id: data.id,
        type: data.shortcut_type,
        title: data.title,
        subtitle: data.subtitle,
        iconName: data.icon_name,
        actionData: data.action_data,
        usageCount: data.usage_count,
      };
    } catch (error) {
      console.error('Error creating app shortcut:', error);
      throw error;
    }
  }

  async getUserShortcuts(userId: string): Promise<AppShortcut[]> {
    try {
      const { data, error } = await this.supabase
        .from('app_shortcuts')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .order('usage_count', { ascending: false })
        .limit(4); // Most mobile platforms support 4 shortcuts

      if (error) throw error;

      return data?.map((shortcut: any) => ({
        id: shortcut.id,
        type: shortcut.shortcut_type,
        title: shortcut.title,
        subtitle: shortcut.subtitle,
        iconName: shortcut.icon_name,
        actionData: shortcut.action_data,
        usageCount: shortcut.usage_count,
      })) || [];
    } catch (error) {
      console.error('Error getting user shortcuts:', error);
      return [];
    }
  }

  async updateShortcutUsage(shortcutId: string): Promise<void> {
    try {
      await this.supabase
        .from('app_shortcuts')
        .update({
          usage_count: this.supabase.raw('usage_count + 1'),
          last_used_at: new Date().toISOString(),
        })
        .eq('id', shortcutId);
    } catch (error) {
      console.error('Error updating shortcut usage:', error);
    }
  }

  private getDefaultIcon(type: string): string {
    switch (type) {
      case 'quick_book':
        return 'calendar';
      case 'emergency_contact':
        return 'phone';
      case 'favorite_companion':
        return 'heart';
      case 'recent_chat':
        return 'message-circle';
      default:
        return 'star';
    }
  }

  // Share functionality
  async shareCompanionProfile(companionId: string, userId?: string): Promise<string> {
    try {
      const link = await this.createCompanionProfileLink(companionId, userId);
      
      if (Platform.OS !== 'web') {
        const { Share } = await import('react-native');
        await Share.share({
          message: `Check out this amazing companion on HourlyGF: ${link}`,
          url: link,
          title: 'HourlyGF Companion Profile',
        });
      }

      return link;
    } catch (error) {
      console.error('Error sharing companion profile:', error);
      throw error;
    }
  }

  async shareGroupBooking(groupBookingId: string, userId: string): Promise<string> {
    try {
      const link = await this.createGroupBookingInvite(groupBookingId, userId);
      
      if (Platform.OS !== 'web') {
        const { Share } = await import('react-native');
        await Share.share({
          message: `Join my group booking on HourlyGF: ${link}`,
          url: link,
          title: 'HourlyGF Group Booking Invite',
        });
      }

      return link;
    } catch (error) {
      console.error('Error sharing group booking:', error);
      throw error;
    }
  }
}