import { createClient } from '@supabase/supabase-js';

export interface LoyaltyTier {
  id: string;
  name: string;
  minPoints: number;
  maxPoints?: number;
  benefits: string[];
  discountPercentage: number;
  priorityBooking: boolean;
  exclusiveCompanions: boolean;
  freeUpgrades: boolean;
  personalConcierge: boolean;
  color: string;
  icon: string;
}

export interface LoyaltyPoints {
  id: string;
  userId: string;
  points: number;
  tier: string;
  lifetimePoints: number;
  pointsThisMonth: number;
  pointsThisYear: number;
  nextTierPoints: number;
  updatedAt: Date;
}

export interface PointsTransaction {
  id: string;
  userId: string;
  type: 'earned' | 'redeemed' | 'expired' | 'bonus' | 'penalty';
  points: number;
  reason: string;
  bookingId?: string;
  companionId?: string;
  referralId?: string;
  expiresAt?: Date;
  createdAt: Date;
}

export interface Reward {
  id: string;
  name: string;
  description: string;
  pointsCost: number;
  type: 'discount' | 'upgrade' | 'exclusive_access' | 'gift' | 'experience';
  value: number;
  isActive: boolean;
  tierRequired?: string;
  expiryDays: number;
  maxRedemptions?: number;
  currentRedemptions: number;
}

export interface UserReward {
  id: string;
  userId: string;
  rewardId: string;
  pointsSpent: number;
  status: 'active' | 'used' | 'expired';
  redeemedAt: Date;
  expiresAt: Date;
  usedAt?: Date;
  bookingId?: string;
}

export class LoyaltyService {
  private static instance: LoyaltyService;
  private supabase: any;

  static getInstance(): LoyaltyService {
    if (!LoyaltyService.instance) {
      LoyaltyService.instance = new LoyaltyService();
    }
    return LoyaltyService.instance;
  }

  initialize(supabaseClient: any): void {
    this.supabase = supabaseClient;
  }

  // Loyalty Tiers
  getLoyaltyTiers(): LoyaltyTier[] {
    return [
      {
        id: 'bronze',
        name: 'Bronze',
        minPoints: 0,
        maxPoints: 999,
        benefits: ['Basic customer support', 'Standard booking'],
        discountPercentage: 0,
        priorityBooking: false,
        exclusiveCompanions: false,
        freeUpgrades: false,
        personalConcierge: false,
        color: '#CD7F32',
        icon: '🥉'
      },
      {
        id: 'silver',
        name: 'Silver',
        minPoints: 1000,
        maxPoints: 4999,
        benefits: ['Priority customer support', '5% discount on bookings', 'Extended cancellation window'],
        discountPercentage: 5,
        priorityBooking: true,
        exclusiveCompanions: false,
        freeUpgrades: false,
        personalConcierge: false,
        color: '#C0C0C0',
        icon: '🥈'
      },
      {
        id: 'gold',
        name: 'Gold',
        minPoints: 5000,
        maxPoints: 14999,
        benefits: ['VIP customer support', '10% discount on bookings', 'Free booking upgrades', 'Access to exclusive companions'],
        discountPercentage: 10,
        priorityBooking: true,
        exclusiveCompanions: true,
        freeUpgrades: true,
        personalConcierge: false,
        color: '#FFD700',
        icon: '🥇'
      },
      {
        id: 'platinum',
        name: 'Platinum',
        minPoints: 15000,
        benefits: ['Personal concierge', '15% discount on bookings', 'Complimentary upgrades', 'Exclusive events access', 'Priority booking'],
        discountPercentage: 15,
        priorityBooking: true,
        exclusiveCompanions: true,
        freeUpgrades: true,
        personalConcierge: true,
        color: '#E5E4E2',
        icon: '💎'
      }
    ];
  }

  // Points Management
  async getUserLoyaltyPoints(userId: string): Promise<LoyaltyPoints | null> {
    try {
      const { data, error } = await this.supabase
        .from('loyalty_points')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') throw error;

      if (!data) {
        // Create initial loyalty record
        return await this.initializeLoyaltyPoints(userId);
      }

      // Update tier and next tier points
      const tier = this.calculateTier(data.points);
      const nextTierPoints = this.calculateNextTierPoints(data.points);

      if (data.tier !== tier.id) {
        await this.updateUserTier(userId, tier.id);
        data.tier = tier.id;
      }

      data.nextTierPoints = nextTierPoints;
      return data;
    } catch (error) {
      console.error('Error fetching loyalty points:', error);
      return null;
    }
  }

  private async initializeLoyaltyPoints(userId: string): Promise<LoyaltyPoints> {
    const { data, error } = await this.supabase
      .from('loyalty_points')
      .insert({
        user_id: userId,
        points: 0,
        tier: 'bronze',
        lifetime_points: 0,
        points_this_month: 0,
        points_this_year: 0,
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async awardPoints(
    userId: string,
    points: number,
    reason: string,
    bookingId?: string,
    companionId?: string
  ): Promise<void> {
    try {
      // Record transaction
      await this.supabase
        .from('points_transactions')
        .insert({
          user_id: userId,
          type: 'earned',
          points,
          reason,
          booking_id: bookingId,
          companion_id: companionId,
          created_at: new Date().toISOString()
        });

      // Update user points
      const { error } = await this.supabase.rpc('add_loyalty_points', {
        user_id: userId,
        points_to_add: points
      });

      if (error) throw error;

      // Check for tier upgrade
      await this.checkTierUpgrade(userId);
    } catch (error) {
      console.error('Error awarding points:', error);
      throw error;
    }
  }

  async redeemPoints(userId: string, points: number, reason: string): Promise<boolean> {
    try {
      const userPoints = await this.getUserLoyaltyPoints(userId);
      if (!userPoints || userPoints.points < points) {
        return false;
      }

      // Record transaction
      await this.supabase
        .from('points_transactions')
        .insert({
          user_id: userId,
          type: 'redeemed',
          points: -points,
          reason,
          created_at: new Date().toISOString()
        });

      // Update user points
      const { error } = await this.supabase.rpc('subtract_loyalty_points', {
        user_id: userId,
        points_to_subtract: points
      });

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error redeeming points:', error);
      return false;
    }
  }

  private calculateTier(points: number): LoyaltyTier {
    const tiers = this.getLoyaltyTiers();
    return tiers.reverse().find(tier => points >= tier.minPoints) || tiers[0];
  }

  private calculateNextTierPoints(points: number): number {
    const tiers = this.getLoyaltyTiers();
    const nextTier = tiers.find(tier => points < tier.minPoints);
    return nextTier ? nextTier.minPoints - points : 0;
  }

  private async updateUserTier(userId: string, newTier: string): Promise<void> {
    await this.supabase
      .from('loyalty_points')
      .update({ tier: newTier })
      .eq('user_id', userId);
  }

  private async checkTierUpgrade(userId: string): Promise<void> {
    const userPoints = await this.getUserLoyaltyPoints(userId);
    if (!userPoints) return;

    const newTier = this.calculateTier(userPoints.points);
    if (newTier.id !== userPoints.tier) {
      await this.updateUserTier(userId, newTier.id);
      
      // Award tier upgrade bonus
      await this.awardTierUpgradeBonus(userId, newTier);
    }
  }

  private async awardTierUpgradeBonus(userId: string, tier: LoyaltyTier): Promise<void> {
    const bonusPoints = tier.minPoints * 0.1; // 10% of tier minimum as bonus
    await this.awardPoints(userId, bonusPoints, `Tier upgrade bonus to ${tier.name}`);
  }

  // Rewards Management
  async getAvailableRewards(userId: string): Promise<Reward[]> {
    try {
      const userPoints = await this.getUserLoyaltyPoints(userId);
      if (!userPoints) return [];

      const { data, error } = await this.supabase
        .from('rewards')
        .select('*')
        .eq('is_active', true)
        .lte('points_cost', userPoints.points)
        .order('points_cost');

      if (error) throw error;

      // Filter by tier requirements
      const userTier = this.calculateTier(userPoints.points);
      const tiers = this.getLoyaltyTiers();
      const userTierIndex = tiers.findIndex(t => t.id === userTier.id);

      return (data || []).filter(reward => {
        if (!reward.tier_required) return true;
        const requiredTierIndex = tiers.findIndex(t => t.id === reward.tier_required);
        return userTierIndex >= requiredTierIndex;
      });
    } catch (error) {
      console.error('Error fetching available rewards:', error);
      return [];
    }
  }

  async redeemReward(userId: string, rewardId: string): Promise<UserReward | null> {
    try {
      // Get reward details
      const { data: reward } = await this.supabase
        .from('rewards')
        .select('*')
        .eq('id', rewardId)
        .single();

      if (!reward) throw new Error('Reward not found');

      // Check if user has enough points
      const success = await this.redeemPoints(userId, reward.points_cost, `Redeemed reward: ${reward.name}`);
      if (!success) throw new Error('Insufficient points');

      // Create user reward
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + reward.expiry_days);

      const { data: userReward, error } = await this.supabase
        .from('user_rewards')
        .insert({
          user_id: userId,
          reward_id: rewardId,
          points_spent: reward.points_cost,
          status: 'active',
          redeemed_at: new Date().toISOString(),
          expires_at: expiresAt.toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      // Update reward redemption count
      await this.supabase
        .from('rewards')
        .update({
          current_redemptions: reward.current_redemptions + 1
        })
        .eq('id', rewardId);

      return userReward;
    } catch (error) {
      console.error('Error redeeming reward:', error);
      return null;
    }
  }

  async getUserRewards(userId: string): Promise<UserReward[]> {
    try {
      const { data, error } = await this.supabase
        .from('user_rewards')
        .select(`
          *,
          rewards(*)
        `)
        .eq('user_id', userId)
        .order('redeemed_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching user rewards:', error);
      return [];
    }
  }

  // Points earning rules
  getPointsForBooking(bookingAmount: number, tier: string): number {
    const basePoints = Math.floor(bookingAmount / 10); // 1 point per $10
    const tierMultiplier = this.getTierMultiplier(tier);
    return Math.floor(basePoints * tierMultiplier);
  }

  private getTierMultiplier(tier: string): number {
    const multipliers = {
      bronze: 1,
      silver: 1.2,
      gold: 1.5,
      platinum: 2
    };
    return multipliers[tier as keyof typeof multipliers] || 1;
  }

  async getPointsTransactionHistory(userId: string, limit = 50): Promise<PointsTransaction[]> {
    try {
      const { data, error } = await this.supabase
        .from('points_transactions')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching points history:', error);
      return [];
    }
  }
}
