import { Platform } from 'react-native';

// EzyBackend SDK types and interfaces
export interface EzyBackendConfig {
  serverUrl: string;
  appId: string;
  appKey: string;
  masterKey?: string;
}

export interface CloudObject {
  _id?: string;
  _type: string;
  createdAt?: Date;
  updatedAt?: Date;
  ACL?: any;
  [key: string]: any;
}

export interface QueryOptions {
  limit?: number;
  skip?: number;
  sort?: { [key: string]: 1 | -1 };
  select?: string[];
  include?: string[];
}

export interface AuthUser {
  id: string;
  email: string;
  username?: string;
  profile?: any;
  sessionToken: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface RealtimeSubscription {
  id: string;
  tableName: string;
  callback: (data: any) => void;
  unsubscribe: () => void;
}

export class EzyBackendClient {
  private config: EzyBackendConfig;
  private currentUser: AuthUser | null = null;
  private sessionToken: string | null = null;
  private realtimeSubscriptions: Map<string, RealtimeSubscription> = new Map();
  private websocket: WebSocket | null = null;

  constructor(config: EzyBackendConfig) {
    this.config = config;
  }

  // Authentication
  async signUp(email: string, password: string, userData?: any): Promise<AuthUser> {
    try {
      const response = await this.makeRequest('/auth/signup', 'POST', {
        email,
        password,
        ...userData
      });

      if (response.error) {
        throw new Error(response.error.message || 'Sign up failed');
      }

      this.currentUser = response.user;
      this.sessionToken = response.sessionToken;
      
      // Store session locally
      if (Platform.OS !== 'web') {
        // Use AsyncStorage for React Native
        // await AsyncStorage.setItem('ezy_session_token', this.sessionToken);
      } else {
        localStorage.setItem('ezy_session_token', this.sessionToken);
      }

      return this.currentUser;
    } catch (error) {
      console.error('Sign up error:', error);
      throw error;
    }
  }

  async signIn(email: string, password: string): Promise<AuthUser> {
    try {
      const response = await this.makeRequest('/auth/signin', 'POST', {
        email,
        password
      });

      if (response.error) {
        throw new Error(response.error.message || 'Sign in failed');
      }

      this.currentUser = response.user;
      this.sessionToken = response.sessionToken;
      
      // Store session locally
      if (Platform.OS !== 'web') {
        // Use AsyncStorage for React Native
        // await AsyncStorage.setItem('ezy_session_token', this.sessionToken);
      } else {
        localStorage.setItem('ezy_session_token', this.sessionToken);
      }

      return this.currentUser;
    } catch (error) {
      console.error('Sign in error:', error);
      throw error;
    }
  }

  async signOut(): Promise<void> {
    try {
      if (this.sessionToken) {
        await this.makeRequest('/auth/signout', 'POST');
      }
    } catch (error) {
      console.error('Sign out error:', error);
    } finally {
      this.currentUser = null;
      this.sessionToken = null;
      
      // Clear stored session
      if (Platform.OS !== 'web') {
        // await AsyncStorage.removeItem('ezy_session_token');
      } else {
        localStorage.removeItem('ezy_session_token');
      }

      // Close realtime connections
      this.disconnectRealtime();
    }
  }

  async getCurrentUser(): Promise<AuthUser | null> {
    if (this.currentUser) {
      return this.currentUser;
    }

    // Try to restore session
    let storedToken: string | null = null;
    if (Platform.OS !== 'web') {
      // storedToken = await AsyncStorage.getItem('ezy_session_token');
    } else {
      storedToken = localStorage.getItem('ezy_session_token');
    }

    if (storedToken) {
      try {
        this.sessionToken = storedToken;
        const response = await this.makeRequest('/auth/me', 'GET');
        if (response.user) {
          this.currentUser = response.user;
          return this.currentUser;
        }
      } catch (error) {
        console.error('Session restore error:', error);
        // Clear invalid session
        this.sessionToken = null;
        if (Platform.OS !== 'web') {
          // await AsyncStorage.removeItem('ezy_session_token');
        } else {
          localStorage.removeItem('ezy_session_token');
        }
      }
    }

    return null;
  }

  // Database Operations
  async create(tableName: string, data: any): Promise<CloudObject> {
    try {
      const response = await this.makeRequest(`/data/${tableName}`, 'POST', data);
      if (response.error) {
        throw new Error(response.error.message || 'Create failed');
      }
      return response.data;
    } catch (error) {
      console.error('Create error:', error);
      throw error;
    }
  }

  async findById(tableName: string, id: string): Promise<CloudObject | null> {
    try {
      const response = await this.makeRequest(`/data/${tableName}/${id}`, 'GET');
      if (response.error) {
        throw new Error(response.error.message || 'Find failed');
      }
      return response.data;
    } catch (error) {
      console.error('Find by ID error:', error);
      throw error;
    }
  }

  async find(tableName: string, query: any = {}, options: QueryOptions = {}): Promise<CloudObject[]> {
    try {
      const queryParams = new URLSearchParams();
      if (Object.keys(query).length > 0) {
        queryParams.append('query', JSON.stringify(query));
      }
      if (options.limit) queryParams.append('limit', options.limit.toString());
      if (options.skip) queryParams.append('skip', options.skip.toString());
      if (options.sort) queryParams.append('sort', JSON.stringify(options.sort));
      if (options.select) queryParams.append('select', options.select.join(','));

      const url = `/data/${tableName}${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
      const response = await this.makeRequest(url, 'GET');
      
      if (response.error) {
        throw new Error(response.error.message || 'Find failed');
      }
      return response.data || [];
    } catch (error) {
      console.error('Find error:', error);
      throw error;
    }
  }

  async update(tableName: string, id: string, data: any): Promise<CloudObject> {
    try {
      const response = await this.makeRequest(`/data/${tableName}/${id}`, 'PUT', data);
      if (response.error) {
        throw new Error(response.error.message || 'Update failed');
      }
      return response.data;
    } catch (error) {
      console.error('Update error:', error);
      throw error;
    }
  }

  async delete(tableName: string, id: string): Promise<void> {
    try {
      const response = await this.makeRequest(`/data/${tableName}/${id}`, 'DELETE');
      if (response.error) {
        throw new Error(response.error.message || 'Delete failed');
      }
    } catch (error) {
      console.error('Delete error:', error);
      throw error;
    }
  }

  // Realtime Subscriptions
  async subscribe(
    tableName: string,
    callback: (data: any) => void,
    query?: any
  ): Promise<RealtimeSubscription> {
    const subscriptionId = `${tableName}_${Date.now()}_${Math.random()}`;
    
    // Initialize WebSocket connection if not exists
    if (!this.websocket) {
      await this.connectRealtime();
    }

    const subscription: RealtimeSubscription = {
      id: subscriptionId,
      tableName,
      callback,
      unsubscribe: () => {
        this.realtimeSubscriptions.delete(subscriptionId);
        if (this.websocket) {
          this.websocket.send(JSON.stringify({
            type: 'unsubscribe',
            subscriptionId
          }));
        }
      }
    };

    this.realtimeSubscriptions.set(subscriptionId, subscription);

    // Send subscription request
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify({
        type: 'subscribe',
        subscriptionId,
        tableName,
        query: query || {},
        sessionToken: this.sessionToken
      }));
    }

    return subscription;
  }

  private async connectRealtime(): Promise<void> {
    return new Promise((resolve, reject) => {
      const wsUrl = this.config.serverUrl.replace('http', 'ws') + '/realtime';
      this.websocket = new WebSocket(wsUrl);

      this.websocket.onopen = () => {
        console.log('EzyBackend realtime connected');
        resolve();
      };

      this.websocket.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          this.handleRealtimeMessage(message);
        } catch (error) {
          console.error('Realtime message parse error:', error);
        }
      };

      this.websocket.onerror = (error) => {
        console.error('WebSocket error:', error);
        reject(error);
      };

      this.websocket.onclose = () => {
        console.log('EzyBackend realtime disconnected');
        this.websocket = null;
        // Attempt to reconnect after 5 seconds
        setTimeout(() => {
          if (this.realtimeSubscriptions.size > 0) {
            this.connectRealtime();
          }
        }, 5000);
      };
    });
  }

  private handleRealtimeMessage(message: any): void {
    if (message.subscriptionId && this.realtimeSubscriptions.has(message.subscriptionId)) {
      const subscription = this.realtimeSubscriptions.get(message.subscriptionId);
      if (subscription) {
        subscription.callback(message.data);
      }
    }
  }

  private disconnectRealtime(): void {
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }
    this.realtimeSubscriptions.clear();
  }

  // HTTP Request Helper
  private async makeRequest(endpoint: string, method: string, data?: any): Promise<any> {
    const url = `${this.config.serverUrl}${endpoint}`;
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'X-CloudApp-Id': this.config.appId,
      'X-CloudApp-Key': this.config.appKey
    };

    if (this.sessionToken) {
      headers['X-CloudApp-SessionToken'] = this.sessionToken;
    }

    const requestOptions: RequestInit = {
      method,
      headers,
    };

    if (data && (method === 'POST' || method === 'PUT')) {
      requestOptions.body = JSON.stringify(data);
    }

    const response = await fetch(url, requestOptions);
    return await response.json();
  }

  // File Upload
  async uploadFile(file: File | Blob, fileName: string): Promise<string> {
    try {
      const formData = new FormData();
      formData.append('file', file, fileName);

      const headers: Record<string, string> = {
        'X-CloudApp-Id': this.config.appId,
        'X-CloudApp-Key': this.config.appKey
      };

      if (this.sessionToken) {
        headers['X-CloudApp-SessionToken'] = this.sessionToken;
      }

      const response = await fetch(`${this.config.serverUrl}/files`, {
        method: 'POST',
        headers,
        body: formData
      });

      const result = await response.json();
      if (result.error) {
        throw new Error(result.error.message || 'File upload failed');
      }

      return result.url;
    } catch (error) {
      console.error('File upload error:', error);
      throw error;
    }
  }
}
