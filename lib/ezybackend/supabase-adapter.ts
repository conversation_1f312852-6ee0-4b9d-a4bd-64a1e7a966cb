import { EzyBackendClient, CloudObject, QueryOptions } from './ezybackend-client';

/**
 * Adapter to provide Supabase-like API using EzyBackend
 * This helps with migration by maintaining the same interface
 */
export class SupabaseAdapter {
  private ezyBackend: EzyBackendClient;

  constructor(ezyBackend: EzyBackendClient) {
    this.ezyBackend = ezyBackend;
  }

  // Auth methods
  get auth() {
    return {
      signUp: async (credentials: { email: string; password: string; options?: any }) => {
        try {
          const user = await this.ezyBackend.signUp(
            credentials.email,
            credentials.password,
            credentials.options?.data
          );
          return { data: { user }, error: null };
        } catch (error: any) {
          return { data: { user: null }, error: { message: error.message } };
        }
      },

      signInWithPassword: async (credentials: { email: string; password: string }) => {
        try {
          const user = await this.ezyBackend.signIn(credentials.email, credentials.password);
          return { data: { user }, error: null };
        } catch (error: any) {
          return { data: { user: null }, error: { message: error.message } };
        }
      },

      signOut: async () => {
        try {
          await this.ezyBackend.signOut();
          return { error: null };
        } catch (error: any) {
          return { error: { message: error.message } };
        }
      },

      getSession: async () => {
        try {
          const user = await this.ezyBackend.getCurrentUser();
          return { data: { session: user ? { user } : null }, error: null };
        } catch (error: any) {
          return { data: { session: null }, error: { message: error.message } };
        }
      },

      onAuthStateChange: (callback: (event: string, session: any) => void) => {
        // EzyBackend doesn't have built-in auth state change events
        // This would need to be implemented with polling or custom events
        console.warn('onAuthStateChange not fully implemented in EzyBackend adapter');
        return { data: { subscription: { unsubscribe: () => {} } } };
      }
    };
  }

  // Database methods
  from(tableName: string) {
    return new SupabaseQueryBuilder(this.ezyBackend, tableName);
  }

  // Storage methods (basic implementation)
  get storage() {
    return {
      from: (bucket: string) => ({
        upload: async (path: string, file: File | Blob) => {
          try {
            const url = await this.ezyBackend.uploadFile(file, path);
            return { data: { path, fullPath: url }, error: null };
          } catch (error: any) {
            return { data: null, error: { message: error.message } };
          }
        },
        
        getPublicUrl: (path: string) => {
          // EzyBackend typically returns full URLs from upload
          return { data: { publicUrl: path } };
        }
      })
    };
  }

  // Realtime subscriptions
  channel(channelName: string) {
    return {
      on: (
        event: string,
        config: { event: string; schema: string; table: string; filter?: string },
        callback: (payload: any) => void
      ) => {
        // Convert Supabase realtime config to EzyBackend subscription
        const query = config.filter ? this.parseFilter(config.filter) : {};
        this.ezyBackend.subscribe(config.table, callback, query);
        return this;
      },
      
      subscribe: () => {
        // EzyBackend subscriptions are automatically active
        return this;
      }
    };
  }

  removeChannel(channel: any) {
    // EzyBackend subscriptions are managed differently
    // This would need custom implementation
  }

  // Helper method to parse Supabase filters
  private parseFilter(filter: string): any {
    // Basic filter parsing - would need to be more comprehensive
    // Example: "session_id=eq.123" -> { session_id: "123" }
    const parts = filter.split('=');
    if (parts.length === 3) {
      const [field, operator, value] = parts;
      if (operator === 'eq') {
        return { [field]: value };
      }
    }
    return {};
  }
}

/**
 * Query builder to mimic Supabase's query interface
 */
class SupabaseQueryBuilder {
  private ezyBackend: EzyBackendClient;
  private tableName: string;
  private query: any = {};
  private options: QueryOptions = {};
  private selectFields: string[] = [];

  constructor(ezyBackend: EzyBackendClient, tableName: string) {
    this.ezyBackend = ezyBackend;
    this.tableName = tableName;
  }

  select(fields?: string) {
    if (fields) {
      this.selectFields = fields.split(',').map(f => f.trim());
      this.options.select = this.selectFields;
    }
    return this;
  }

  eq(column: string, value: any) {
    this.query[column] = value;
    return this;
  }

  neq(column: string, value: any) {
    this.query[column] = { $ne: value };
    return this;
  }

  gt(column: string, value: any) {
    this.query[column] = { $gt: value };
    return this;
  }

  gte(column: string, value: any) {
    this.query[column] = { $gte: value };
    return this;
  }

  lt(column: string, value: any) {
    this.query[column] = { $lt: value };
    return this;
  }

  lte(column: string, value: any) {
    this.query[column] = { $lte: value };
    return this;
  }

  like(column: string, pattern: string) {
    this.query[column] = { $regex: pattern.replace(/%/g, '.*'), $options: 'i' };
    return this;
  }

  ilike(column: string, pattern: string) {
    return this.like(column, pattern);
  }

  in(column: string, values: any[]) {
    this.query[column] = { $in: values };
    return this;
  }

  not(column: string, operator: string, value: any) {
    // Handle NOT operations
    if (operator === 'in') {
      this.query[column] = { $nin: value };
    } else {
      this.query[column] = { $not: { [operator]: value } };
    }
    return this;
  }

  is(column: string, value: any) {
    if (value === null) {
      this.query[column] = null;
    }
    return this;
  }

  overlaps(column: string, values: any[]) {
    // For array overlap operations
    this.query[column] = { $in: values };
    return this;
  }

  order(column: string, options?: { ascending?: boolean }) {
    const direction = options?.ascending !== false ? 1 : -1;
    this.options.sort = { [column]: direction };
    return this;
  }

  limit(count: number) {
    this.options.limit = count;
    return this;
  }

  range(from: number, to: number) {
    this.options.skip = from;
    this.options.limit = to - from + 1;
    return this;
  }

  single() {
    this.options.limit = 1;
    return this;
  }

  maybeSingle() {
    this.options.limit = 1;
    return this;
  }

  // Execute query
  async execute(): Promise<{ data: any; error: any }> {
    try {
      const results = await this.ezyBackend.find(this.tableName, this.query, this.options);
      
      if (this.options.limit === 1) {
        return { data: results[0] || null, error: null };
      }
      
      return { data: results, error: null };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  // Shorthand methods that auto-execute
  then(onResolve: (result: { data: any; error: any }) => void, onReject?: (error: any) => void) {
    return this.execute().then(onResolve, onReject);
  }

  // Insert operations
  async insert(data: any | any[]) {
    try {
      if (Array.isArray(data)) {
        const results = await Promise.all(
          data.map(item => this.ezyBackend.create(this.tableName, item))
        );
        return { data: results, error: null };
      } else {
        const result = await this.ezyBackend.create(this.tableName, data);
        return { data: result, error: null };
      }
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  // Update operations
  async update(data: any) {
    try {
      // EzyBackend requires ID for updates, so we need to find records first
      const records = await this.ezyBackend.find(this.tableName, this.query, this.options);
      const results = await Promise.all(
        records.map(record => this.ezyBackend.update(this.tableName, record._id, data))
      );
      return { data: results, error: null };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  // Delete operations
  async delete() {
    try {
      // Find records first, then delete them
      const records = await this.ezyBackend.find(this.tableName, this.query, this.options);
      await Promise.all(
        records.map(record => this.ezyBackend.delete(this.tableName, record._id))
      );
      return { data: records, error: null };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  // Upsert operations
  async upsert(data: any | any[]) {
    try {
      // EzyBackend doesn't have native upsert, so we simulate it
      if (Array.isArray(data)) {
        const results = [];
        for (const item of data) {
          if (item._id) {
            const result = await this.ezyBackend.update(this.tableName, item._id, item);
            results.push(result);
          } else {
            const result = await this.ezyBackend.create(this.tableName, item);
            results.push(result);
          }
        }
        return { data: results, error: null };
      } else {
        let result;
        if (data._id) {
          result = await this.ezyBackend.update(this.tableName, data._id, data);
        } else {
          result = await this.ezyBackend.create(this.tableName, data);
        }
        return { data: result, error: null };
      }
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  // RPC calls (stored procedures)
  async rpc(functionName: string, params: any = {}) {
    try {
      // EzyBackend would need custom function support
      // For now, return empty result
      console.warn(`RPC function ${functionName} not implemented in EzyBackend adapter`);
      return { data: null, error: null };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }
}
