import { Platform } from 'react-native';
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import Constants from 'expo-constants';

export interface NotificationData {
  type: 'message' | 'booking' | 'emergency' | 'system' | 'location' | 'payment';
  title: string;
  body: string;
  data?: any;
  priority?: 'low' | 'normal' | 'high' | 'max';
  sound?: boolean;
  vibrate?: boolean;
  badge?: number;
}

export interface PushNotificationToken {
  token: string;
  platform: string;
  deviceId: string;
}

export class NotificationService {
  private static instance: NotificationService;
  private pushToken: string | null = null;
  private isInitialized = false;

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Configure notification behavior
      await Notifications.setNotificationHandler({
        handleNotification: async (notification) => {
          const data = notification.request.content.data;
          
          return {
            shouldShowAlert: true,
            shouldPlaySound: data?.sound !== false,
            shouldSetBadge: data?.badge !== undefined,
            priority: this.mapPriorityToAndroid(data?.priority || 'normal'),
          };
        },
      });

      // Request permissions
      await this.requestPermissions();

      // Register for push notifications
      if (Platform.OS !== 'web') {
        await this.registerForPushNotifications();
      }

      // Set up notification listeners
      this.setupNotificationListeners();

      this.isInitialized = true;
      console.log('Notification service initialized');
    } catch (error) {
      console.error('Error initializing notification service:', error);
      throw error;
    }
  }

  private async requestPermissions(): Promise<boolean> {
    try {
      if (Platform.OS === 'web') {
        // Web notifications
        if ('Notification' in window) {
          const permission = await Notification.requestPermission();
          return permission === 'granted';
        }
        return false;
      }

      // Mobile notifications
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.warn('Push notification permissions not granted');
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      return false;
    }
  }

  private async registerForPushNotifications(): Promise<void> {
    try {
      if (!Device.isDevice) {
        console.warn('Push notifications only work on physical devices');
        return;
      }

      const projectId = Constants.expoConfig?.extra?.eas?.projectId;
      if (!projectId) {
        console.warn('No EAS project ID found for push notifications');
        return;
      }

      const tokenData = await Notifications.getExpoPushTokenAsync({
        projectId,
      });

      this.pushToken = tokenData.data;
      console.log('Push token obtained:', this.pushToken);

      // Store token in backend
      await this.storePushToken(this.pushToken);
    } catch (error) {
      console.error('Error registering for push notifications:', error);
    }
  }

  private setupNotificationListeners(): void {
    // Notification received while app is in foreground
    Notifications.addNotificationReceivedListener((notification) => {
      console.log('Notification received:', notification);
      this.handleNotificationReceived(notification);
    });

    // Notification tapped/clicked
    Notifications.addNotificationResponseReceivedListener((response) => {
      console.log('Notification response:', response);
      this.handleNotificationResponse(response);
    });
  }

  private handleNotificationReceived(notification: Notifications.Notification): void {
    const data = notification.request.content.data;
    
    // Handle different notification types
    switch (data?.type) {
      case 'message':
        this.handleMessageNotification(data);
        break;
      case 'booking':
        this.handleBookingNotification(data);
        break;
      case 'emergency':
        this.handleEmergencyNotification(data);
        break;
      case 'location':
        this.handleLocationNotification(data);
        break;
      default:
        console.log('Unknown notification type:', data?.type);
    }
  }

  private handleNotificationResponse(response: Notifications.NotificationResponse): void {
    const data = response.notification.request.content.data;
    
    // Navigate based on notification type
    switch (data?.type) {
      case 'message':
        // Navigate to chat screen
        if (data.conversationId) {
          // Use your navigation service to navigate
          console.log('Navigate to conversation:', data.conversationId);
        }
        break;
      case 'booking':
        // Navigate to booking details
        if (data.bookingId) {
          console.log('Navigate to booking:', data.bookingId);
        }
        break;
      case 'emergency':
        // Navigate to emergency screen
        console.log('Navigate to emergency screen');
        break;
    }
  }

  private handleMessageNotification(data: any): void {
    // Update unread message count
    // Trigger real-time UI updates
    console.log('New message notification:', data);
  }

  private handleBookingNotification(data: any): void {
    // Update booking status in UI
    console.log('Booking notification:', data);
  }

  private handleEmergencyNotification(data: any): void {
    // Show emergency alert
    // Trigger emergency protocols
    console.log('Emergency notification:', data);
  }

  private handleLocationNotification(data: any): void {
    // Handle location-based notifications
    console.log('Location notification:', data);
  }

  // Send local notification
  async sendLocalNotification(notificationData: NotificationData): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        await this.sendWebNotification(notificationData);
      } else {
        await this.sendMobileNotification(notificationData);
      }
    } catch (error) {
      console.error('Error sending local notification:', error);
    }
  }

  private async sendWebNotification(data: NotificationData): Promise<void> {
    if ('Notification' in window && Notification.permission === 'granted') {
      const notification = new Notification(data.title, {
        body: data.body,
        icon: '/icon.png',
        badge: '/icon.png',
        data: data.data,
        requireInteraction: data.priority === 'high' || data.priority === 'max',
      });

      notification.onclick = () => {
        window.focus();
        notification.close();
        // Handle notification click
      };

      // Auto-close after 5 seconds for non-critical notifications
      if (data.priority !== 'high' && data.priority !== 'max') {
        setTimeout(() => notification.close(), 5000);
      }
    }
  }

  private async sendMobileNotification(data: NotificationData): Promise<void> {
    await Notifications.scheduleNotificationAsync({
      content: {
        title: data.title,
        body: data.body,
        data: data.data,
        sound: data.sound !== false,
        badge: data.badge,
        priority: this.mapPriorityToAndroid(data.priority || 'normal'),
      },
      trigger: null, // Send immediately
    });
  }

  private mapPriorityToAndroid(priority: string): Notifications.AndroidNotificationPriority {
    switch (priority) {
      case 'low':
        return Notifications.AndroidNotificationPriority.LOW;
      case 'high':
        return Notifications.AndroidNotificationPriority.HIGH;
      case 'max':
        return Notifications.AndroidNotificationPriority.MAX;
      default:
        return Notifications.AndroidNotificationPriority.DEFAULT;
    }
  }

  // Store push token in backend
  private async storePushToken(token: string): Promise<void> {
    try {
      // This would typically send to your backend API
      const response = await fetch('/api/notifications/register-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token,
          platform: Platform.OS,
          deviceId: Device.osInternalBuildId || 'unknown',
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to store push token');
      }

      console.log('Push token stored successfully');
    } catch (error) {
      console.error('Error storing push token:', error);
    }
  }

  // Badge management
  async setBadgeCount(count: number): Promise<void> {
    try {
      if (Platform.OS !== 'web') {
        await Notifications.setBadgeCountAsync(count);
      }
    } catch (error) {
      console.error('Error setting badge count:', error);
    }
  }

  async clearBadge(): Promise<void> {
    await this.setBadgeCount(0);
  }

  // Cancel notifications
  async cancelNotification(notificationId: string): Promise<void> {
    try {
      if (Platform.OS !== 'web') {
        await Notifications.cancelScheduledNotificationAsync(notificationId);
      }
    } catch (error) {
      console.error('Error canceling notification:', error);
    }
  }

  async cancelAllNotifications(): Promise<void> {
    try {
      if (Platform.OS !== 'web') {
        await Notifications.cancelAllScheduledNotificationsAsync();
      }
    } catch (error) {
      console.error('Error canceling all notifications:', error);
    }
  }

  // Get push token
  getPushToken(): string | null {
    return this.pushToken;
  }

  // Check if notifications are enabled
  async areNotificationsEnabled(): Promise<boolean> {
    try {
      if (Platform.OS === 'web') {
        return 'Notification' in window && Notification.permission === 'granted';
      }

      const { status } = await Notifications.getPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error checking notification permissions:', error);
      return false;
    }
  }
}