import { Platform } from 'react-native';
import { LocationData } from '@/lib/location/location-service';

export interface EmergencyContact {
  id: string;
  name: string;
  phoneNumber: string;
  email?: string;
  relationship: 'family' | 'friend' | 'partner' | 'colleague' | 'medical' | 'legal' | 'other';
  isPrimary: boolean;
  isMedical: boolean;
  notes?: string;
}

export interface EmergencyIncident {
  id: string;
  userId: string;
  incidentType: 'sos' | 'medical' | 'safety' | 'harassment' | 'theft' | 'accident' | 'other';
  status: 'active' | 'resolved' | 'false_alarm' | 'escalated';
  priority: 'low' | 'medium' | 'high' | 'critical';
  location?: LocationData;
  address?: string;
  description?: string;
  audioRecordingUrl?: string;
  videoRecordingUrl?: string;
  photos?: string[];
  contactsNotified: string[];
  authoritiesContacted: boolean;
  createdAt: Date;
  resolvedAt?: Date;
}

export interface SOSConfiguration {
  enableAutoCall: boolean;
  autoCallDelay: number; // seconds
  enableLocationSharing: boolean;
  enableAudioRecording: boolean;
  enableVideoRecording: boolean;
  enablePhotoCapture: boolean;
  primaryContactId?: string;
}

export class EmergencyService {
  private static instance: EmergencyService;
  private sosConfiguration: SOSConfiguration = {
    enableAutoCall: false,
    autoCallDelay: 10,
    enableLocationSharing: true,
    enableAudioRecording: true,
    enableVideoRecording: false,
    enablePhotoCapture: true,
  };
  private activeIncident: EmergencyIncident | null = null;
  private emergencyContacts: EmergencyContact[] = [];

  static getInstance(): EmergencyService {
    if (!EmergencyService.instance) {
      EmergencyService.instance = new EmergencyService();
    }
    return EmergencyService.instance;
  }

  async triggerSOS(
    location?: LocationData,
    description?: string,
    incidentType: EmergencyIncident['incidentType'] = 'sos'
  ): Promise<EmergencyIncident> {
    try {
      // Create incident record
      const incident: EmergencyIncident = {
        id: this.generateIncidentId(),
        userId: 'current-user-id', // This would come from auth context
        incidentType,
        status: 'active',
        priority: incidentType === 'sos' ? 'critical' : 'high',
        location,
        description,
        contactsNotified: [],
        authoritiesContacted: false,
        createdAt: new Date(),
      };

      this.activeIncident = incident;

      // Start emergency protocol
      await this.executeEmergencyProtocol(incident);

      return incident;
    } catch (error) {
      console.error('Error triggering SOS:', error);
      throw error;
    }
  }

  private async executeEmergencyProtocol(incident: EmergencyIncident): Promise<void> {
    try {
      // 1. Notify emergency contacts
      await this.notifyEmergencyContacts(incident);

      // 2. Start location sharing
      if (this.sosConfiguration.enableLocationSharing) {
        await this.startEmergencyLocationSharing(incident);
      }

      // 3. Start audio recording if enabled
      if (this.sosConfiguration.enableAudioRecording) {
        await this.startAudioRecording(incident);
      }

      // 4. Auto-call authorities if configured
      if (this.sosConfiguration.enableAutoCall) {
        setTimeout(() => {
          this.callEmergencyServices(incident);
        }, this.sosConfiguration.autoCallDelay * 1000);
      }

      // 5. Send to backend
      await this.reportIncidentToBackend(incident);
    } catch (error) {
      console.error('Error executing emergency protocol:', error);
    }
  }

  private async notifyEmergencyContacts(incident: EmergencyIncident): Promise<void> {
    try {
      // Sort contacts by priority (primary first, then medical)
      const sortedContacts = [...this.emergencyContacts].sort((a, b) => {
        if (a.isPrimary && !b.isPrimary) return -1;
        if (!a.isPrimary && b.isPrimary) return 1;
        if (a.isMedical && !b.isMedical) return -1;
        if (!a.isMedical && b.isMedical) return 1;
        return 0;
      });

      for (const contact of sortedContacts) {
        await this.sendEmergencyNotification(contact, incident);
        incident.contactsNotified.push(contact.id);
      }
    } catch (error) {
      console.error('Error notifying emergency contacts:', error);
    }
  }

  private async sendEmergencyNotification(
    contact: EmergencyContact,
    incident: EmergencyIncident
  ): Promise<void> {
    try {
      const message = this.generateEmergencyMessage(incident);
      
      if (Platform.OS === 'web') {
        // Web notification
        if ('Notification' in window && Notification.permission === 'granted') {
          new Notification('Emergency Alert', {
            body: `Emergency notification sent to ${contact.name}`,
            icon: '/icon.png',
          });
        }
        console.log(`Emergency notification sent to ${contact.name}: ${message}`);
      } else {
        // Mobile SMS/Call integration would go here
        // For now, we'll simulate the notification
        console.log(`SMS sent to ${contact.phoneNumber}: ${message}`);
      }
    } catch (error) {
      console.error('Error sending emergency notification:', error);
    }
  }

  private generateEmergencyMessage(incident: EmergencyIncident): string {
    const locationText = incident.location
      ? `Location: ${incident.location.latitude}, ${incident.location.longitude}`
      : 'Location: Unknown';

    return `🚨 EMERGENCY ALERT 🚨
${incident.description || 'Emergency situation reported'}
${locationText}
Time: ${incident.createdAt.toLocaleString()}
Please respond immediately or contact authorities.`;
  }

  private async startEmergencyLocationSharing(incident: EmergencyIncident): Promise<void> {
    try {
      // This would integrate with the location service to share location
      // with emergency contacts and authorities
      console.log('Emergency location sharing started for incident:', incident.id);
    } catch (error) {
      console.error('Error starting emergency location sharing:', error);
    }
  }

  private async startAudioRecording(incident: EmergencyIncident): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        // Web audio recording
        if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
          const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
          // Start recording logic here
          console.log('Audio recording started for incident:', incident.id);
        }
      } else {
        // Mobile audio recording would use expo-av
        console.log('Audio recording started for incident:', incident.id);
      }
    } catch (error) {
      console.error('Error starting audio recording:', error);
    }
  }

  private async callEmergencyServices(incident: EmergencyIncident): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        // Web can't make phone calls directly
        window.open('tel:911', '_self');
      } else {
        // Mobile would use Linking API
        console.log('Calling emergency services for incident:', incident.id);
      }
      
      incident.authoritiesContacted = true;
    } catch (error) {
      console.error('Error calling emergency services:', error);
    }
  }

  private async reportIncidentToBackend(incident: EmergencyIncident): Promise<void> {
    try {
      // This would send the incident to your backend API
      console.log('Incident reported to backend:', incident);
    } catch (error) {
      console.error('Error reporting incident to backend:', error);
    }
  }

  async resolveIncident(
    incidentId: string,
    resolution: 'resolved' | 'false_alarm',
    notes?: string
  ): Promise<void> {
    try {
      if (this.activeIncident?.id === incidentId) {
        this.activeIncident.status = resolution;
        this.activeIncident.resolvedAt = new Date();
        
        // Stop any ongoing emergency protocols
        await this.stopEmergencyProtocols();
        
        // Notify contacts of resolution
        await this.notifyIncidentResolution(this.activeIncident, notes);
        
        this.activeIncident = null;
      }
    } catch (error) {
      console.error('Error resolving incident:', error);
    }
  }

  private async stopEmergencyProtocols(): Promise<void> {
    try {
      // Stop location sharing, recording, etc.
      console.log('Emergency protocols stopped');
    } catch (error) {
      console.error('Error stopping emergency protocols:', error);
    }
  }

  private async notifyIncidentResolution(
    incident: EmergencyIncident,
    notes?: string
  ): Promise<void> {
    try {
      const message = `Emergency resolved. ${notes || 'All clear.'}`;
      
      for (const contactId of incident.contactsNotified) {
        const contact = this.emergencyContacts.find(c => c.id === contactId);
        if (contact) {
          console.log(`Resolution notification sent to ${contact.name}: ${message}`);
        }
      }
    } catch (error) {
      console.error('Error notifying incident resolution:', error);
    }
  }

  async addEmergencyContact(contact: Omit<EmergencyContact, 'id'>): Promise<EmergencyContact> {
    const newContact: EmergencyContact = {
      ...contact,
      id: this.generateContactId(),
    };
    
    this.emergencyContacts.push(newContact);
    return newContact;
  }

  async removeEmergencyContact(contactId: string): Promise<void> {
    this.emergencyContacts = this.emergencyContacts.filter(
      contact => contact.id !== contactId
    );
  }

  async updateEmergencyContact(
    contactId: string,
    updates: Partial<EmergencyContact>
  ): Promise<EmergencyContact | null> {
    const contactIndex = this.emergencyContacts.findIndex(c => c.id === contactId);
    if (contactIndex === -1) return null;

    this.emergencyContacts[contactIndex] = {
      ...this.emergencyContacts[contactIndex],
      ...updates,
    };

    return this.emergencyContacts[contactIndex];
  }

  getEmergencyContacts(): EmergencyContact[] {
    return [...this.emergencyContacts];
  }

  updateSOSConfiguration(config: Partial<SOSConfiguration>): void {
    this.sosConfiguration = {
      ...this.sosConfiguration,
      ...config,
    };
  }

  getSOSConfiguration(): SOSConfiguration {
    return { ...this.sosConfiguration };
  }

  getActiveIncident(): EmergencyIncident | null {
    return this.activeIncident;
  }

  async testEmergencySystem(): Promise<boolean> {
    try {
      // Test all emergency system components
      const testResults = {
        locationAccess: await this.testLocationAccess(),
        contactsReachable: await this.testContactsReachable(),
        recordingCapability: await this.testRecordingCapability(),
        networkConnectivity: await this.testNetworkConnectivity(),
      };

      return Object.values(testResults).every(result => result);
    } catch (error) {
      console.error('Error testing emergency system:', error);
      return false;
    }
  }

  private async testLocationAccess(): Promise<boolean> {
    try {
      if (Platform.OS === 'web') {
        return 'geolocation' in navigator;
      }
      // Test location access on mobile
      return true;
    } catch {
      return false;
    }
  }

  private async testContactsReachable(): Promise<boolean> {
    // Test if emergency contacts can be reached
    return this.emergencyContacts.length > 0;
  }

  private async testRecordingCapability(): Promise<boolean> {
    try {
      if (Platform.OS === 'web') {
        return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
      }
      return true;
    } catch {
      return false;
    }
  }

  private async testNetworkConnectivity(): Promise<boolean> {
    try {
      if (Platform.OS === 'web') {
        return navigator.onLine;
      }
      // Test network connectivity on mobile
      return true;
    } catch {
      return false;
    }
  }

  private generateIncidentId(): string {
    return `incident_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateContactId(): string {
    return `contact_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  async requestEmergencyPermissions(): Promise<boolean> {
    try {
      if (Platform.OS === 'web') {
        // Request notification permission
        if ('Notification' in window) {
          const permission = await Notification.requestPermission();
          return permission === 'granted';
        }
        return false;
      }

      // Request mobile permissions (location, microphone, etc.)
      return true;
    } catch (error) {
      console.error('Error requesting emergency permissions:', error);
      return false;
    }
  }
}