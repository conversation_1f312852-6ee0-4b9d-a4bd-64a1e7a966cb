import { createClient } from '@supabase/supabase-js';
import { Platform } from 'react-native';

export interface SocialAccount {
  id: string;
  userId: string;
  provider: 'facebook' | 'instagram' | 'twitter' | 'linkedin' | 'tiktok' | 'snapchat';
  providerId: string;
  username: string;
  displayName: string;
  profileUrl: string;
  avatarUrl?: string;
  isVerified: boolean;
  isPublic: boolean;
  followerCount?: number;
  verifiedAt?: Date;
  connectedAt: Date;
  lastSyncAt?: Date;
}

export interface SocialVerification {
  id: string;
  userId: string;
  provider: string;
  verificationMethod: 'profile_link' | 'post_verification' | 'api_verification';
  verificationData: any;
  status: 'pending' | 'verified' | 'failed' | 'expired';
  verifiedAt?: Date;
  expiresAt?: Date;
}

export interface SocialProfile {
  id: string;
  userId: string;
  connectedAccounts: SocialAccount[];
  privacySettings: {
    showSocialProfiles: boolean;
    allowSocialVerification: boolean;
    shareActivityOnSocial: boolean;
  };
  verificationScore: number;
  lastUpdated: Date;
}

export class SocialIntegrationService {
  private static instance: SocialIntegrationService;
  private supabase: any;

  static getInstance(): SocialIntegrationService {
    if (!SocialIntegrationService.instance) {
      SocialIntegrationService.instance = new SocialIntegrationService();
    }
    return SocialIntegrationService.instance;
  }

  initialize(supabaseClient: any): void {
    this.supabase = supabaseClient;
  }

  // Social Account Management
  async connectSocialAccount(
    userId: string,
    provider: SocialAccount['provider'],
    accessToken: string
  ): Promise<SocialAccount> {
    try {
      // Get profile data from provider
      const profileData = await this.fetchProviderProfile(provider, accessToken);

      // Check if account is already connected
      const { data: existingAccount } = await this.supabase
        .from('social_accounts')
        .select('*')
        .eq('provider', provider)
        .eq('provider_id', profileData.id)
        .single();

      if (existingAccount && existingAccount.user_id !== userId) {
        throw new Error('This social account is already connected to another user');
      }

      // Create or update social account
      const { data, error } = await this.supabase
        .from('social_accounts')
        .upsert({
          user_id: userId,
          provider,
          provider_id: profileData.id,
          username: profileData.username,
          display_name: profileData.displayName,
          profile_url: profileData.profileUrl,
          avatar_url: profileData.avatarUrl,
          follower_count: profileData.followerCount,
          is_verified: false,
          is_public: true,
          connected_at: new Date().toISOString(),
          last_sync_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      // Start verification process
      await this.initiateVerification(userId, provider, data.id);

      return data;
    } catch (error) {
      console.error('Error connecting social account:', error);
      throw error;
    }
  }

  async disconnectSocialAccount(userId: string, accountId: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('social_accounts')
        .delete()
        .eq('id', accountId)
        .eq('user_id', userId);

      if (error) throw error;

      // Remove related verifications
      await this.supabase
        .from('social_verifications')
        .delete()
        .eq('user_id', userId)
        .eq('social_account_id', accountId);
    } catch (error) {
      console.error('Error disconnecting social account:', error);
      throw error;
    }
  }

  async getSocialProfile(userId: string): Promise<SocialProfile | null> {
    try {
      const { data: accounts, error: accountsError } = await this.supabase
        .from('social_accounts')
        .select('*')
        .eq('user_id', userId);

      if (accountsError) throw accountsError;

      const { data: settings, error: settingsError } = await this.supabase
        .from('social_privacy_settings')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (settingsError && settingsError.code !== 'PGRST116') throw settingsError;

      const verificationScore = this.calculateVerificationScore(accounts || []);

      return {
        id: userId,
        userId,
        connectedAccounts: accounts || [],
        privacySettings: settings || {
          showSocialProfiles: false,
          allowSocialVerification: true,
          shareActivityOnSocial: false
        },
        verificationScore,
        lastUpdated: new Date()
      };
    } catch (error) {
      console.error('Error fetching social profile:', error);
      return null;
    }
  }

  // Social Verification
  private async initiateVerification(
    userId: string,
    provider: string,
    accountId: string
  ): Promise<void> {
    try {
      // Create verification record
      const { data, error } = await this.supabase
        .from('social_verifications')
        .insert({
          user_id: userId,
          provider,
          social_account_id: accountId,
          verification_method: 'profile_link',
          status: 'pending',
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      // Start verification process based on provider
      await this.performVerification(data.id, provider);
    } catch (error) {
      console.error('Error initiating verification:', error);
    }
  }

  private async performVerification(verificationId: string, provider: string): Promise<void> {
    try {
      // This would integrate with actual social media APIs
      // For now, simulate verification
      setTimeout(async () => {
        const isVerified = Math.random() > 0.3; // 70% success rate for demo

        await this.supabase
          .from('social_verifications')
          .update({
            status: isVerified ? 'verified' : 'failed',
            verified_at: isVerified ? new Date().toISOString() : null
          })
          .eq('id', verificationId);

        if (isVerified) {
          // Update social account verification status
          await this.supabase
            .from('social_accounts')
            .update({
              is_verified: true,
              verified_at: new Date().toISOString()
            })
            .eq('id', verificationId);
        }
      }, 5000); // 5 second delay to simulate verification
    } catch (error) {
      console.error('Error performing verification:', error);
    }
  }

  // Provider Profile Fetching (Mock implementations)
  private async fetchProviderProfile(
    provider: SocialAccount['provider'],
    accessToken: string
  ): Promise<any> {
    // In a real implementation, these would make actual API calls
    switch (provider) {
      case 'facebook':
        return this.fetchFacebookProfile(accessToken);
      case 'instagram':
        return this.fetchInstagramProfile(accessToken);
      case 'twitter':
        return this.fetchTwitterProfile(accessToken);
      case 'linkedin':
        return this.fetchLinkedInProfile(accessToken);
      default:
        throw new Error(`Provider ${provider} not supported`);
    }
  }

  private async fetchFacebookProfile(accessToken: string): Promise<any> {
    // Mock Facebook API response
    return {
      id: 'fb_' + Math.random().toString(36).substr(2, 9),
      username: 'user_' + Math.random().toString(36).substr(2, 6),
      displayName: 'John Doe',
      profileUrl: 'https://facebook.com/johndoe',
      avatarUrl: 'https://graph.facebook.com/v12.0/me/picture',
      followerCount: Math.floor(Math.random() * 1000)
    };
  }

  private async fetchInstagramProfile(accessToken: string): Promise<any> {
    // Mock Instagram API response
    return {
      id: 'ig_' + Math.random().toString(36).substr(2, 9),
      username: 'user_' + Math.random().toString(36).substr(2, 6),
      displayName: 'John Doe',
      profileUrl: 'https://instagram.com/johndoe',
      avatarUrl: 'https://instagram.com/johndoe/profile.jpg',
      followerCount: Math.floor(Math.random() * 5000)
    };
  }

  private async fetchTwitterProfile(accessToken: string): Promise<any> {
    // Mock Twitter API response
    return {
      id: 'tw_' + Math.random().toString(36).substr(2, 9),
      username: 'user_' + Math.random().toString(36).substr(2, 6),
      displayName: 'John Doe',
      profileUrl: 'https://twitter.com/johndoe',
      avatarUrl: 'https://pbs.twimg.com/profile_images/johndoe.jpg',
      followerCount: Math.floor(Math.random() * 2000)
    };
  }

  private async fetchLinkedInProfile(accessToken: string): Promise<any> {
    // Mock LinkedIn API response
    return {
      id: 'li_' + Math.random().toString(36).substr(2, 9),
      username: 'john-doe-' + Math.random().toString(36).substr(2, 6),
      displayName: 'John Doe',
      profileUrl: 'https://linkedin.com/in/johndoe',
      avatarUrl: 'https://media.licdn.com/dms/image/johndoe.jpg',
      followerCount: Math.floor(Math.random() * 500)
    };
  }

  // Verification Score Calculation
  private calculateVerificationScore(accounts: SocialAccount[]): number {
    let score = 0;
    const weights = {
      facebook: 20,
      instagram: 25,
      twitter: 15,
      linkedin: 30,
      tiktok: 10,
      snapchat: 5
    };

    accounts.forEach(account => {
      if (account.isVerified) {
        score += weights[account.provider] || 10;
        
        // Bonus for high follower count
        if (account.followerCount) {
          if (account.followerCount > 10000) score += 10;
          else if (account.followerCount > 1000) score += 5;
          else if (account.followerCount > 100) score += 2;
        }
      }
    });

    return Math.min(100, score);
  }

  // Privacy Settings
  async updatePrivacySettings(
    userId: string,
    settings: SocialProfile['privacySettings']
  ): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('social_privacy_settings')
        .upsert({
          user_id: userId,
          show_social_profiles: settings.showSocialProfiles,
          allow_social_verification: settings.allowSocialVerification,
          share_activity_on_social: settings.shareActivityOnSocial,
          updated_at: new Date().toISOString()
        });

      if (error) throw error;
    } catch (error) {
      console.error('Error updating privacy settings:', error);
      throw error;
    }
  }

  // Social Sharing
  async shareActivity(
    userId: string,
    activityType: 'booking' | 'review' | 'milestone',
    activityData: any
  ): Promise<void> {
    try {
      const profile = await this.getSocialProfile(userId);
      if (!profile?.privacySettings.shareActivityOnSocial) return;

      const message = this.generateShareMessage(activityType, activityData);
      
      // Share to connected platforms
      for (const account of profile.connectedAccounts) {
        if (account.isVerified && account.isPublic) {
          await this.shareToProvider(account.provider, message, activityData);
        }
      }
    } catch (error) {
      console.error('Error sharing activity:', error);
    }
  }

  private generateShareMessage(activityType: string, activityData: any): string {
    switch (activityType) {
      case 'booking':
        return `Just booked an amazing companion experience! #HourlyGF #CompanionshipApp`;
      case 'review':
        return `Left a great review for my companion experience! ⭐⭐⭐⭐⭐ #HourlyGF`;
      case 'milestone':
        return `Reached ${activityData.milestone} on HourlyGF! 🎉 #HourlyGF #Milestone`;
      default:
        return `Having a great time with HourlyGF! #HourlyGF`;
    }
  }

  private async shareToProvider(provider: string, message: string, data: any): Promise<void> {
    // In a real implementation, this would post to the actual social media platforms
    console.log(`Sharing to ${provider}: ${message}`);
  }

  // Account Sync
  async syncSocialAccounts(userId: string): Promise<void> {
    try {
      const { data: accounts } = await this.supabase
        .from('social_accounts')
        .select('*')
        .eq('user_id', userId);

      if (!accounts) return;

      for (const account of accounts) {
        // In a real implementation, this would refresh profile data from each provider
        await this.supabase
          .from('social_accounts')
          .update({
            last_sync_at: new Date().toISOString()
          })
          .eq('id', account.id);
      }
    } catch (error) {
      console.error('Error syncing social accounts:', error);
    }
  }

  // Get verification status
  async getVerificationStatus(userId: string): Promise<SocialVerification[]> {
    try {
      const { data, error } = await this.supabase
        .from('social_verifications')
        .select(`
          *,
          social_accounts(provider, username, display_name)
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching verification status:', error);
      return [];
    }
  }
}
