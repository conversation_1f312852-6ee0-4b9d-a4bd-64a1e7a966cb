import { Platform } from 'react-native';
import * as Location from 'expo-location';

export interface LocationData {
  latitude: number;
  longitude: number;
  accuracy?: number;
  altitude?: number;
  heading?: number;
  speed?: number;
  timestamp: number;
}

export interface LocationPermissions {
  granted: boolean;
  canAskAgain: boolean;
  status: Location.PermissionStatus;
}

export interface GeofenceRegion {
  identifier: string;
  latitude: number;
  longitude: number;
  radius: number;
  notifyOnEnter?: boolean;
  notifyOnExit?: boolean;
}

export interface ProximityAlert {
  id: string;
  targetUserId: string;
  distance: number;
  message: string;
}

export class LocationService {
  private static instance: LocationService;
  private watchSubscription: Location.LocationSubscription | null = null;
  private isTracking = false;
  private lastKnownLocation: LocationData | null = null;
  private geofenceRegions: GeofenceRegion[] = [];
  private proximityAlerts: ProximityAlert[] = [];

  static getInstance(): LocationService {
    if (!LocationService.instance) {
      LocationService.instance = new LocationService();
    }
    return LocationService.instance;
  }

  async requestPermissions(): Promise<LocationPermissions> {
    try {
      if (Platform.OS === 'web') {
        // Web geolocation API
        if (!navigator.geolocation) {
          return {
            granted: false,
            canAskAgain: false,
            status: Location.PermissionStatus.DENIED,
          };
        }

        return new Promise((resolve) => {
          navigator.geolocation.getCurrentPosition(
            () => resolve({
              granted: true,
              canAskAgain: true,
              status: Location.PermissionStatus.GRANTED,
            }),
            () => resolve({
              granted: false,
              canAskAgain: true,
              status: Location.PermissionStatus.DENIED,
            })
          );
        });
      }

      // Mobile permissions
      const { status } = await Location.requestForegroundPermissionsAsync();
      
      return {
        granted: status === Location.PermissionStatus.GRANTED,
        canAskAgain: status !== Location.PermissionStatus.DENIED,
        status,
      };
    } catch (error) {
      console.error('Error requesting location permissions:', error);
      return {
        granted: false,
        canAskAgain: false,
        status: Location.PermissionStatus.DENIED,
      };
    }
  }

  async requestBackgroundPermissions(): Promise<LocationPermissions> {
    if (Platform.OS === 'web') {
      // Background location not available on web
      return {
        granted: false,
        canAskAgain: false,
        status: Location.PermissionStatus.DENIED,
      };
    }

    try {
      const { status } = await Location.requestBackgroundPermissionsAsync();
      
      return {
        granted: status === Location.PermissionStatus.GRANTED,
        canAskAgain: status !== Location.PermissionStatus.DENIED,
        status,
      };
    } catch (error) {
      console.error('Error requesting background location permissions:', error);
      return {
        granted: false,
        canAskAgain: false,
        status: Location.PermissionStatus.DENIED,
      };
    }
  }

  async getCurrentLocation(): Promise<LocationData | null> {
    try {
      if (Platform.OS === 'web') {
        return new Promise((resolve, reject) => {
          navigator.geolocation.getCurrentPosition(
            (position) => {
              const location: LocationData = {
                latitude: position.coords.latitude,
                longitude: position.coords.longitude,
                accuracy: position.coords.accuracy,
                altitude: position.coords.altitude || undefined,
                heading: position.coords.heading || undefined,
                speed: position.coords.speed || undefined,
                timestamp: position.timestamp,
              };
              this.lastKnownLocation = location;
              resolve(location);
            },
            (error) => {
              console.error('Web geolocation error:', error);
              reject(error);
            },
            {
              enableHighAccuracy: true,
              timeout: 10000,
              maximumAge: 60000,
            }
          );
        });
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
        timeInterval: 5000,
        distanceInterval: 10,
      });

      const locationData: LocationData = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        accuracy: location.coords.accuracy,
        altitude: location.coords.altitude,
        heading: location.coords.heading,
        speed: location.coords.speed,
        timestamp: location.timestamp,
      };

      this.lastKnownLocation = locationData;
      return locationData;
    } catch (error) {
      console.error('Error getting current location:', error);
      return null;
    }
  }

  async startLocationTracking(
    callback: (location: LocationData) => void,
    options?: {
      accuracy?: Location.Accuracy;
      timeInterval?: number;
      distanceInterval?: number;
    }
  ): Promise<boolean> {
    try {
      if (this.isTracking) {
        await this.stopLocationTracking();
      }

      if (Platform.OS === 'web') {
        // Web geolocation watching
        const watchId = navigator.geolocation.watchPosition(
          (position) => {
            const location: LocationData = {
              latitude: position.coords.latitude,
              longitude: position.coords.longitude,
              accuracy: position.coords.accuracy,
              altitude: position.coords.altitude || undefined,
              heading: position.coords.heading || undefined,
              speed: position.coords.speed || undefined,
              timestamp: position.timestamp,
            };
            this.lastKnownLocation = location;
            callback(location);
          },
          (error) => {
            console.error('Web geolocation watch error:', error);
          },
          {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 30000,
          }
        );

        // Store watch ID for cleanup
        this.watchSubscription = { remove: () => navigator.geolocation.clearWatch(watchId) } as any;
        this.isTracking = true;
        return true;
      }

      this.watchSubscription = await Location.watchPositionAsync(
        {
          accuracy: options?.accuracy || Location.Accuracy.High,
          timeInterval: options?.timeInterval || 10000,
          distanceInterval: options?.distanceInterval || 10,
        },
        (location) => {
          const locationData: LocationData = {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
            accuracy: location.coords.accuracy,
            altitude: location.coords.altitude,
            heading: location.coords.heading,
            speed: location.coords.speed,
            timestamp: location.timestamp,
          };
          this.lastKnownLocation = locationData;
          callback(locationData);
        }
      );

      this.isTracking = true;
      return true;
    } catch (error) {
      console.error('Error starting location tracking:', error);
      return false;
    }
  }

  async stopLocationTracking(): Promise<void> {
    if (this.watchSubscription) {
      this.watchSubscription.remove();
      this.watchSubscription = null;
    }
    this.isTracking = false;
  }

  calculateDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) *
        Math.cos(this.toRadians(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  async reverseGeocode(latitude: number, longitude: number): Promise<string | null> {
    try {
      if (Platform.OS === 'web') {
        // Use a web-compatible geocoding service
        // For production, you'd use Google Maps API or similar
        return `${latitude.toFixed(4)}, ${longitude.toFixed(4)}`;
      }

      const result = await Location.reverseGeocodeAsync({
        latitude,
        longitude,
      });

      if (result.length > 0) {
        const address = result[0];
        return [
          address.streetNumber,
          address.street,
          address.city,
          address.region,
          address.postalCode,
        ]
          .filter(Boolean)
          .join(', ');
      }

      return null;
    } catch (error) {
      console.error('Error reverse geocoding:', error);
      return null;
    }
  }

  async geocode(address: string): Promise<LocationData | null> {
    try {
      if (Platform.OS === 'web') {
        // Web geocoding would require external API
        console.warn('Geocoding not available on web platform');
        return null;
      }

      const result = await Location.geocodeAsync(address);
      
      if (result.length > 0) {
        const location = result[0];
        return {
          latitude: location.latitude,
          longitude: location.longitude,
          timestamp: Date.now(),
        };
      }

      return null;
    } catch (error) {
      console.error('Error geocoding address:', error);
      return null;
    }
  }

  addGeofenceRegion(region: GeofenceRegion): void {
    this.geofenceRegions.push(region);
  }

  removeGeofenceRegion(identifier: string): void {
    this.geofenceRegions = this.geofenceRegions.filter(
      (region) => region.identifier !== identifier
    );
  }

  checkGeofences(location: LocationData): GeofenceRegion[] {
    const triggeredRegions: GeofenceRegion[] = [];

    for (const region of this.geofenceRegions) {
      const distance = this.calculateDistance(
        location.latitude,
        location.longitude,
        region.latitude,
        region.longitude
      );

      if (distance * 1000 <= region.radius) {
        // Within geofence
        triggeredRegions.push(region);
      }
    }

    return triggeredRegions;
  }

  addProximityAlert(alert: ProximityAlert): void {
    this.proximityAlerts.push(alert);
  }

  removeProximityAlert(id: string): void {
    this.proximityAlerts = this.proximityAlerts.filter(
      (alert) => alert.id !== id
    );
  }

  checkProximityAlerts(
    userLocation: LocationData,
    targetLocations: Map<string, LocationData>
  ): ProximityAlert[] {
    const triggeredAlerts: ProximityAlert[] = [];

    for (const alert of this.proximityAlerts) {
      const targetLocation = targetLocations.get(alert.targetUserId);
      if (!targetLocation) continue;

      const distance = this.calculateDistance(
        userLocation.latitude,
        userLocation.longitude,
        targetLocation.latitude,
        targetLocation.longitude
      );

      if (distance * 1000 <= alert.distance) {
        triggeredAlerts.push(alert);
      }
    }

    return triggeredAlerts;
  }

  getLastKnownLocation(): LocationData | null {
    return this.lastKnownLocation;
  }

  isLocationTrackingActive(): boolean {
    return this.isTracking;
  }

  async startBackgroundLocationTracking(): Promise<boolean> {
    if (Platform.OS === 'web') {
      console.warn('Background location tracking not available on web');
      return false;
    }

    try {
      await Location.startLocationUpdatesAsync('background-location-task', {
        accuracy: Location.Accuracy.High,
        timeInterval: 30000, // 30 seconds
        distanceInterval: 50, // 50 meters
        foregroundService: {
          notificationTitle: 'HourlyGF Location Tracking',
          notificationBody: 'Location tracking active for safety',
        },
      });

      return true;
    } catch (error) {
      console.error('Error starting background location tracking:', error);
      return false;
    }
  }

  async stopBackgroundLocationTracking(): Promise<void> {
    if (Platform.OS === 'web') return;

    try {
      await Location.stopLocationUpdatesAsync('background-location-task');
    } catch (error) {
      console.error('Error stopping background location tracking:', error);
    }
  }
}