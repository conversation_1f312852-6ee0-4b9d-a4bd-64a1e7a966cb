import { createClient } from '@supabase/supabase-js';

export interface ContentArticle {
  id: string;
  title: string;
  slug: string;
  excerpt?: string;
  content: string;
  featuredImageUrl?: string;
  category: string;
  tags: string[];
  authorId?: string;
  isPublished: boolean;
  isFeatured: boolean;
  viewCount: number;
  likeCount: number;
  shareCount: number;
  estimatedReadTime: number;
  publishedAt?: Date;
  createdAt: Date;
}

export interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
  subcategory?: string;
  tags: string[];
  isFeatured: boolean;
  viewCount: number;
  helpfulCount: number;
  notHelpfulCount: number;
  displayOrder: number;
}

export interface LegalDocument {
  id: string;
  documentType: string;
  title: string;
  content: string;
  version: string;
  isCurrent: boolean;
  effectiveDate: Date;
  requiresAcceptance: boolean;
}

export class ContentService {
  private static instance: ContentService;
  private supabase: any;

  static getInstance(): ContentService {
    if (!ContentService.instance) {
      ContentService.instance = new ContentService();
    }
    return ContentService.instance;
  }

  initialize(supabaseClient: any): void {
    this.supabase = supabaseClient;
  }

  // Blog/News Articles
  async getPublishedArticles(
    category?: string,
    limit = 20,
    offset = 0
  ): Promise<ContentArticle[]> {
    try {
      let query = this.supabase
        .from('content_articles')
        .select('*')
        .eq('is_published', true)
        .order('published_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (category) {
        query = query.eq('category', category);
      }

      const { data, error } = await query;
      if (error) throw error;

      return data?.map((article: any) => ({
        id: article.id,
        title: article.title,
        slug: article.slug,
        excerpt: article.excerpt,
        content: article.content,
        featuredImageUrl: article.featured_image_url,
        category: article.category,
        tags: article.tags,
        authorId: article.author_id,
        isPublished: article.is_published,
        isFeatured: article.is_featured,
        viewCount: article.view_count,
        likeCount: article.like_count,
        shareCount: article.share_count,
        estimatedReadTime: article.estimated_read_time,
        publishedAt: article.published_at ? new Date(article.published_at) : undefined,
        createdAt: new Date(article.created_at),
      })) || [];
    } catch (error) {
      console.error('Error getting published articles:', error);
      return [];
    }
  }

  async getFeaturedArticles(limit = 5): Promise<ContentArticle[]> {
    try {
      const { data, error } = await this.supabase
        .from('content_articles')
        .select('*')
        .eq('is_published', true)
        .eq('is_featured', true)
        .order('published_at', { ascending: false })
        .limit(limit);

      if (error) throw error;

      return data?.map((article: any) => ({
        id: article.id,
        title: article.title,
        slug: article.slug,
        excerpt: article.excerpt,
        content: article.content,
        featuredImageUrl: article.featured_image_url,
        category: article.category,
        tags: article.tags,
        authorId: article.author_id,
        isPublished: article.is_published,
        isFeatured: article.is_featured,
        viewCount: article.view_count,
        likeCount: article.like_count,
        shareCount: article.share_count,
        estimatedReadTime: article.estimated_read_time,
        publishedAt: article.published_at ? new Date(article.published_at) : undefined,
        createdAt: new Date(article.created_at),
      })) || [];
    } catch (error) {
      console.error('Error getting featured articles:', error);
      return [];
    }
  }

  async getArticleBySlug(slug: string): Promise<ContentArticle | null> {
    try {
      const { data, error } = await this.supabase
        .from('content_articles')
        .select('*')
        .eq('slug', slug)
        .eq('is_published', true)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      if (!data) return null;

      // Increment view count
      await this.incrementArticleViews(data.id);

      return {
        id: data.id,
        title: data.title,
        slug: data.slug,
        excerpt: data.excerpt,
        content: data.content,
        featuredImageUrl: data.featured_image_url,
        category: data.category,
        tags: data.tags,
        authorId: data.author_id,
        isPublished: data.is_published,
        isFeatured: data.is_featured,
        viewCount: data.view_count,
        likeCount: data.like_count,
        shareCount: data.share_count,
        estimatedReadTime: data.estimated_read_time,
        publishedAt: data.published_at ? new Date(data.published_at) : undefined,
        createdAt: new Date(data.created_at),
      };
    } catch (error) {
      console.error('Error getting article by slug:', error);
      return null;
    }
  }

  private async incrementArticleViews(articleId: string): Promise<void> {
    try {
      await this.supabase
        .from('content_articles')
        .update({
          view_count: this.supabase.raw('view_count + 1'),
          updated_at: new Date().toISOString(),
        })
        .eq('id', articleId);
    } catch (error) {
      console.error('Error incrementing article views:', error);
    }
  }

  // FAQ System
  async getFAQItems(category?: string): Promise<FAQItem[]> {
    try {
      let query = this.supabase
        .from('faq_items')
        .select('*')
        .eq('is_published', true)
        .order('display_order', { ascending: true });

      if (category) {
        query = query.eq('category', category);
      }

      const { data, error } = await query;
      if (error) throw error;

      return data?.map((item: any) => ({
        id: item.id,
        question: item.question,
        answer: item.answer,
        category: item.category,
        subcategory: item.subcategory,
        tags: item.tags,
        isFeatured: item.is_featured,
        viewCount: item.view_count,
        helpfulCount: item.helpful_count,
        notHelpfulCount: item.not_helpful_count,
        displayOrder: item.display_order,
      })) || [];
    } catch (error) {
      console.error('Error getting FAQ items:', error);
      return [];
    }
  }

  async getFeaturedFAQs(limit = 10): Promise<FAQItem[]> {
    try {
      const { data, error } = await this.supabase
        .from('faq_items')
        .select('*')
        .eq('is_published', true)
        .eq('is_featured', true)
        .order('display_order', { ascending: true })
        .limit(limit);

      if (error) throw error;

      return data?.map((item: any) => ({
        id: item.id,
        question: item.question,
        answer: item.answer,
        category: item.category,
        subcategory: item.subcategory,
        tags: item.tags,
        isFeatured: item.is_featured,
        viewCount: item.view_count,
        helpfulCount: item.helpful_count,
        notHelpfulCount: item.not_helpful_count,
        displayOrder: item.display_order,
      })) || [];
    } catch (error) {
      console.error('Error getting featured FAQs:', error);
      return [];
    }
  }

  async searchFAQs(query: string): Promise<FAQItem[]> {
    try {
      const { data, error } = await this.supabase
        .from('faq_items')
        .select('*')
        .eq('is_published', true)
        .or(`question.ilike.%${query}%,answer.ilike.%${query}%,search_keywords.ilike.%${query}%`)
        .order('helpful_count', { ascending: false })
        .limit(20);

      if (error) throw error;

      return data?.map((item: any) => ({
        id: item.id,
        question: item.question,
        answer: item.answer,
        category: item.category,
        subcategory: item.subcategory,
        tags: item.tags,
        isFeatured: item.is_featured,
        viewCount: item.view_count,
        helpfulCount: item.helpful_count,
        notHelpfulCount: item.not_helpful_count,
        displayOrder: item.display_order,
      })) || [];
    } catch (error) {
      console.error('Error searching FAQs:', error);
      return [];
    }
  }

  async markFAQHelpful(faqId: string, isHelpful: boolean): Promise<void> {
    try {
      const field = isHelpful ? 'helpful_count' : 'not_helpful_count';
      
      await this.supabase
        .from('faq_items')
        .update({
          [field]: this.supabase.raw(`${field} + 1`),
          updated_at: new Date().toISOString(),
        })
        .eq('id', faqId);
    } catch (error) {
      console.error('Error marking FAQ helpful:', error);
    }
  }

  // Legal Documents
  async getCurrentLegalDocuments(): Promise<LegalDocument[]> {
    try {
      const { data, error } = await this.supabase
        .from('legal_documents')
        .select('*')
        .eq('is_current', true)
        .order('document_type', { ascending: true });

      if (error) throw error;

      return data?.map((doc: any) => ({
        id: doc.id,
        documentType: doc.document_type,
        title: doc.title,
        content: doc.content,
        version: doc.version,
        isCurrent: doc.is_current,
        effectiveDate: new Date(doc.effective_date),
        requiresAcceptance: doc.requires_acceptance,
      })) || [];
    } catch (error) {
      console.error('Error getting legal documents:', error);
      return [];
    }
  }

  async getLegalDocument(documentType: string): Promise<LegalDocument | null> {
    try {
      const { data, error } = await this.supabase
        .from('legal_documents')
        .select('*')
        .eq('document_type', documentType)
        .eq('is_current', true)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      if (!data) return null;

      return {
        id: data.id,
        documentType: data.document_type,
        title: data.title,
        content: data.content,
        version: data.version,
        isCurrent: data.is_current,
        effectiveDate: new Date(data.effective_date),
        requiresAcceptance: data.requires_acceptance,
      };
    } catch (error) {
      console.error('Error getting legal document:', error);
      return null;
    }
  }

  async acceptLegalDocument(
    userId: string,
    documentId: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    try {
      await this.supabase
        .from('user_legal_acceptances')
        .insert({
          user_id: userId,
          document_id: documentId,
          ip_address: ipAddress,
          user_agent: userAgent,
        });
    } catch (error) {
      console.error('Error accepting legal document:', error);
      throw error;
    }
  }

  async hasAcceptedDocument(userId: string, documentType: string): Promise<boolean> {
    try {
      const { data, error } = await this.supabase
        .from('user_legal_acceptances')
        .select('id')
        .eq('user_id', userId)
        .eq('document_id', this.supabase.raw(`(
          SELECT id FROM legal_documents 
          WHERE document_type = '${documentType}' AND is_current = true
        )`))
        .maybeSingle();

      if (error && error.code !== 'PGRST116') throw error;
      return !!data;
    } catch (error) {
      console.error('Error checking document acceptance:', error);
      return false;
    }
  }

  // Multi-language Support
  async getTranslations(languageCode: string): Promise<{ [key: string]: string }> {
    try {
      const { data, error } = await this.supabase
        .from('app_translations')
        .select('translation_key, translation_value')
        .eq('language_code', languageCode)
        .eq('is_approved', true);

      if (error) throw error;

      const translations: { [key: string]: string } = {};
      data?.forEach((item: any) => {
        translations[item.translation_key] = item.translation_value;
      });

      return translations;
    } catch (error) {
      console.error('Error getting translations:', error);
      return {};
    }
  }

  async getSupportedLanguages(): Promise<string[]> {
    try {
      const { data, error } = await this.supabase
        .from('app_translations')
        .select('language_code')
        .eq('is_approved', true)
        .group('language_code');

      if (error) throw error;

      return data?.map((item: any) => item.language_code) || ['en'];
    } catch (error) {
      console.error('Error getting supported languages:', error);
      return ['en'];
    }
  }

  // Content Analytics
  async trackContentView(
    contentType: 'article' | 'faq',
    contentId: string,
    userId?: string
  ): Promise<void> {
    try {
      // Track in analytics
      await this.supabase
        .from('analytics_events')
        .insert({
          user_id: userId,
          event_name: `${contentType}_viewed`,
          event_category: 'content',
          event_properties: {
            content_id: contentId,
            content_type: contentType,
          },
        });

      // Update view count
      const table = contentType === 'article' ? 'content_articles' : 'faq_items';
      await this.supabase
        .from(table)
        .update({
          view_count: this.supabase.raw('view_count + 1'),
          updated_at: new Date().toISOString(),
        })
        .eq('id', contentId);
    } catch (error) {
      console.error('Error tracking content view:', error);
    }
  }

  async likeArticle(articleId: string, userId: string): Promise<void> {
    try {
      // Track like event
      await this.supabase
        .from('analytics_events')
        .insert({
          user_id: userId,
          event_name: 'article_liked',
          event_category: 'engagement',
          event_properties: {
            article_id: articleId,
          },
        });

      // Update like count
      await this.supabase
        .from('content_articles')
        .update({
          like_count: this.supabase.raw('like_count + 1'),
          updated_at: new Date().toISOString(),
        })
        .eq('id', articleId);
    } catch (error) {
      console.error('Error liking article:', error);
    }
  }

  async shareArticle(articleId: string, platform: string, userId?: string): Promise<void> {
    try {
      // Track share event
      await this.supabase
        .from('analytics_events')
        .insert({
          user_id: userId,
          event_name: 'article_shared',
          event_category: 'social',
          event_properties: {
            article_id: articleId,
            platform,
          },
        });

      // Update share count
      await this.supabase
        .from('content_articles')
        .update({
          share_count: this.supabase.raw('share_count + 1'),
          updated_at: new Date().toISOString(),
        })
        .eq('id', articleId);
    } catch (error) {
      console.error('Error tracking article share:', error);
    }
  }

  // Content Categories
  getArticleCategories(): Array<{ id: string; label: string; description: string }> {
    return [
      { id: 'dating_tips', label: 'Dating Tips', description: 'Expert advice for successful dates' },
      { id: 'safety', label: 'Safety', description: 'Safety guidelines and best practices' },
      { id: 'company_news', label: 'Company News', description: 'Latest updates and announcements' },
      { id: 'success_stories', label: 'Success Stories', description: 'Real user experiences and testimonials' },
      { id: 'guides', label: 'Guides', description: 'How-to guides and tutorials' },
    ];
  }

  getFAQCategories(): Array<{ id: string; label: string; description: string }> {
    return [
      { id: 'getting_started', label: 'Getting Started', description: 'New user help and basics' },
      { id: 'booking', label: 'Booking', description: 'Booking process and management' },
      { id: 'payments', label: 'Payments', description: 'Payment methods and billing' },
      { id: 'safety', label: 'Safety', description: 'Safety features and guidelines' },
      { id: 'account', label: 'Account', description: 'Account management and settings' },
      { id: 'technical', label: 'Technical', description: 'Technical issues and troubleshooting' },
      { id: 'companions', label: 'For Companions', description: 'Companion-specific help' },
    ];
  }
}