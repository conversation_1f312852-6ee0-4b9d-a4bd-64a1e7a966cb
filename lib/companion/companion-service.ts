import { createClient } from '@supabase/supabase-js';

export interface CompanionAvailability {
  date: string;
  timeSlots: Array<{
    startTime: string;
    endTime: string;
    isAvailable: boolean;
    specialPricing?: number;
    notes?: string;
  }>;
}

export interface CompanionEarnings {
  id: string;
  bookingId: string;
  grossAmount: number;
  platformFeePercentage: number;
  platformFeeAmount: number;
  netAmount: number;
  status: 'pending' | 'available' | 'paid' | 'on_hold' | 'disputed';
  availableAt?: Date;
  paidAt?: Date;
}

export interface PayoutRequest {
  id: string;
  amount: number;
  payoutMethod: 'bank_transfer' | 'paypal' | 'stripe' | 'check';
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  processingFee: number;
  netAmount: number;
  referenceNumber?: string;
}

export interface CompanionAnalytics {
  totalEarnings: number;
  totalBookings: number;
  averageRating: number;
  responseRate: number;
  completionRate: number;
  repeatCustomerRate: number;
  monthlyTrends: any[];
}

export class CompanionService {
  private static instance: CompanionService;
  private supabase: any;

  static getInstance(): CompanionService {
    if (!CompanionService.instance) {
      CompanionService.instance = new CompanionService();
    }
    return CompanionService.instance;
  }

  initialize(supabaseClient: any): void {
    this.supabase = supabaseClient;
  }

  // Availability Management
  async setAvailability(
    companionId: string,
    date: string,
    timeSlots: CompanionAvailability['timeSlots']
  ): Promise<void> {
    try {
      await this.supabase
        .from('companion_availability')
        .upsert({
          companion_id: companionId,
          date,
          time_slots: timeSlots,
          is_available: timeSlots.some(slot => slot.isAvailable),
          updated_at: new Date().toISOString(),
        });
    } catch (error) {
      console.error('Error setting availability:', error);
      throw error;
    }
  }

  async getAvailability(
    companionId: string,
    startDate: string,
    endDate: string
  ): Promise<CompanionAvailability[]> {
    try {
      const { data, error } = await this.supabase
        .from('companion_availability')
        .select('*')
        .eq('companion_id', companionId)
        .gte('date', startDate)
        .lte('date', endDate)
        .order('date', { ascending: true });

      if (error) throw error;

      return data?.map((item: any) => ({
        date: item.date,
        timeSlots: item.time_slots,
      })) || [];
    } catch (error) {
      console.error('Error getting availability:', error);
      return [];
    }
  }

  async bulkUpdateAvailability(
    companionId: string,
    availabilityData: CompanionAvailability[]
  ): Promise<void> {
    try {
      const updates = availabilityData.map(availability => ({
        companion_id: companionId,
        date: availability.date,
        time_slots: availability.timeSlots,
        is_available: availability.timeSlots.some(slot => slot.isAvailable),
        updated_at: new Date().toISOString(),
      }));

      const { error } = await this.supabase
        .from('companion_availability')
        .upsert(updates);

      if (error) throw error;
    } catch (error) {
      console.error('Error bulk updating availability:', error);
      throw error;
    }
  }

  // Earnings Management
  async getEarnings(
    companionId: string,
    status?: string,
    limit = 50
  ): Promise<CompanionEarnings[]> {
    try {
      let query = this.supabase
        .from('companion_earnings')
        .select('*')
        .eq('companion_id', companionId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (status) {
        query = query.eq('status', status);
      }

      const { data, error } = await query;
      if (error) throw error;

      return data?.map((earning: any) => ({
        id: earning.id,
        bookingId: earning.booking_id,
        grossAmount: parseFloat(earning.gross_amount),
        platformFeePercentage: parseFloat(earning.platform_fee_percentage),
        platformFeeAmount: parseFloat(earning.platform_fee_amount),
        netAmount: parseFloat(earning.net_amount),
        status: earning.status,
        availableAt: earning.available_at ? new Date(earning.available_at) : undefined,
        paidAt: earning.paid_at ? new Date(earning.paid_at) : undefined,
      })) || [];
    } catch (error) {
      console.error('Error getting earnings:', error);
      return [];
    }
  }

  async getAvailableEarnings(companionId: string): Promise<number> {
    try {
      const { data, error } = await this.supabase
        .from('companion_earnings')
        .select('net_amount')
        .eq('companion_id', companionId)
        .eq('status', 'available')
        .lte('available_at', new Date().toISOString());

      if (error) throw error;

      return data?.reduce((total: number, earning: any) => 
        total + parseFloat(earning.net_amount), 0
      ) || 0;
    } catch (error) {
      console.error('Error getting available earnings:', error);
      return 0;
    }
  }

  // Payout Management
  async requestPayout(
    companionId: string,
    amount: number,
    payoutMethod: PayoutRequest['payoutMethod'],
    payoutDetails: any
  ): Promise<PayoutRequest> {
    try {
      const availableEarnings = await this.getAvailableEarnings(companionId);
      
      if (amount > availableEarnings) {
        throw new Error('Insufficient available earnings');
      }

      const processingFee = this.calculateProcessingFee(amount, payoutMethod);
      const netAmount = amount - processingFee;

      const { data, error } = await this.supabase
        .from('payout_requests')
        .insert({
          companion_id: companionId,
          amount,
          payout_method: payoutMethod,
          processing_fee: processingFee,
          net_amount: netAmount,
          bank_account_info: payoutMethod === 'bank_transfer' ? payoutDetails : null,
          paypal_email: payoutMethod === 'paypal' ? payoutDetails.email : null,
        })
        .select()
        .single();

      if (error) throw error;

      // Mark earnings as on_hold
      await this.supabase
        .from('companion_earnings')
        .update({ status: 'on_hold' })
        .eq('companion_id', companionId)
        .eq('status', 'available')
        .lte('available_at', new Date().toISOString());

      return {
        id: data.id,
        amount: parseFloat(data.amount),
        payoutMethod: data.payout_method,
        status: data.status,
        processingFee: parseFloat(data.processing_fee),
        netAmount: parseFloat(data.net_amount),
        referenceNumber: data.reference_number,
      };
    } catch (error) {
      console.error('Error requesting payout:', error);
      throw error;
    }
  }

  private calculateProcessingFee(amount: number, method: string): number {
    switch (method) {
      case 'bank_transfer':
        return Math.max(1, amount * 0.01); // 1% or $1 minimum
      case 'paypal':
        return amount * 0.029 + 0.30; // PayPal fees
      case 'stripe':
        return amount * 0.029 + 0.30; // Stripe fees
      case 'check':
        return 5; // Flat $5 for check processing
      default:
        return 0;
    }
  }

  async getPayoutHistory(companionId: string): Promise<PayoutRequest[]> {
    try {
      const { data, error } = await this.supabase
        .from('payout_requests')
        .select('*')
        .eq('companion_id', companionId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return data?.map((payout: any) => ({
        id: payout.id,
        amount: parseFloat(payout.amount),
        payoutMethod: payout.payout_method,
        status: payout.status,
        processingFee: parseFloat(payout.processing_fee),
        netAmount: parseFloat(payout.net_amount),
        referenceNumber: payout.reference_number,
      })) || [];
    } catch (error) {
      console.error('Error getting payout history:', error);
      return [];
    }
  }

  // Analytics
  async getCompanionAnalytics(
    companionId: string,
    startDate: Date,
    endDate: Date
  ): Promise<CompanionAnalytics> {
    try {
      // Get earnings data
      const { data: earningsData } = await this.supabase
        .from('companion_earnings')
        .select('net_amount, created_at')
        .eq('companion_id', companionId)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString());

      // Get bookings data
      const { data: bookingsData } = await this.supabase
        .from('bookings')
        .select('*')
        .eq('companion_id', companionId)
        .gte('booking_time', startDate.toISOString())
        .lte('booking_time', endDate.toISOString());

      // Get reviews data
      const { data: reviewsData } = await this.supabase
        .from('user_reviews')
        .select('overall_rating')
        .eq('companion_id', companionId);

      const totalEarnings = earningsData?.reduce((sum: number, earning: any) => 
        sum + parseFloat(earning.net_amount), 0
      ) || 0;

      const totalBookings = bookingsData?.length || 0;
      const completedBookings = bookingsData?.filter((b: any) => b.status === 'completed').length || 0;
      const averageRating = reviewsData?.length > 0 
        ? reviewsData.reduce((sum: number, review: any) => sum + review.overall_rating, 0) / reviewsData.length
        : 0;

      // Calculate repeat customer rate
      const uniqueCustomers = new Set(bookingsData?.map((b: any) => b.user_id)).size;
      const repeatCustomerRate = uniqueCustomers > 0 ? ((totalBookings - uniqueCustomers) / totalBookings) * 100 : 0;

      return {
        totalEarnings,
        totalBookings,
        averageRating,
        responseRate: 95, // This would be calculated from message response times
        completionRate: totalBookings > 0 ? (completedBookings / totalBookings) * 100 : 0,
        repeatCustomerRate,
        monthlyTrends: this.generateMonthlyTrends(earningsData || []),
      };
    } catch (error) {
      console.error('Error getting companion analytics:', error);
      return {
        totalEarnings: 0,
        totalBookings: 0,
        averageRating: 0,
        responseRate: 0,
        completionRate: 0,
        repeatCustomerRate: 0,
        monthlyTrends: [],
      };
    }
  }

  private generateMonthlyTrends(earningsData: any[]): any[] {
    const monthlyData: { [key: string]: { earnings: number; bookings: number } } = {};

    earningsData.forEach(earning => {
      const month = new Date(earning.created_at).toISOString().substring(0, 7); // YYYY-MM
      if (!monthlyData[month]) {
        monthlyData[month] = { earnings: 0, bookings: 0 };
      }
      monthlyData[month].earnings += parseFloat(earning.net_amount);
      monthlyData[month].bookings += 1;
    });

    return Object.entries(monthlyData).map(([month, data]) => ({
      month,
      earnings: data.earnings,
      bookings: data.bookings,
    }));
  }

  // Photo Management
  async uploadCompanionPhoto(
    companionId: string,
    photoUri: string,
    caption?: string,
    isPrimary = false
  ): Promise<string> {
    try {
      const fileExt = photoUri.split('.').pop();
      const fileName = `${companionId}-${Date.now()}.${fileExt}`;
      const filePath = `companion-photos/${fileName}`;
      
      const response = await fetch(photoUri);
      const blob = await response.blob();
      
      const { error: uploadError } = await this.supabase.storage
        .from('companion-photos')
        .upload(filePath, blob);
        
      if (uploadError) throw uploadError;
      
      const { data: publicUrl } = this.supabase.storage
        .from('companion-photos')
        .getPublicUrl(filePath);

      // If this is primary, unset other primary photos
      if (isPrimary) {
        await this.supabase
          .from('companion_photos')
          .update({ is_primary: false })
          .eq('companion_id', companionId);
      }

      // Save photo record
      await this.supabase
        .from('companion_photos')
        .insert({
          companion_id: companionId,
          photo_url: publicUrl.publicUrl,
          caption,
          is_primary: isPrimary,
        });

      return publicUrl.publicUrl;
    } catch (error) {
      console.error('Error uploading companion photo:', error);
      throw error;
    }
  }

  async getCompanionPhotos(companionId: string): Promise<any[]> {
    try {
      const { data, error } = await this.supabase
        .from('companion_photos')
        .select('*')
        .eq('companion_id', companionId)
        .order('photo_order', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting companion photos:', error);
      return [];
    }
  }

  async deleteCompanionPhoto(photoId: string): Promise<void> {
    try {
      // Get photo details first
      const { data: photo } = await this.supabase
        .from('companion_photos')
        .select('photo_url')
        .eq('id', photoId)
        .single();

      if (photo) {
        // Extract file path from URL
        const filePath = photo.photo_url.split('/').pop();
        
        // Delete from storage
        await this.supabase.storage
          .from('companion-photos')
          .remove([`companion-photos/${filePath}`]);
      }

      // Delete record
      await this.supabase
        .from('companion_photos')
        .delete()
        .eq('id', photoId);
    } catch (error) {
      console.error('Error deleting companion photo:', error);
      throw error;
    }
  }

  // Performance Insights
  async getPerformanceInsights(companionId: string): Promise<any> {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      // Get recent bookings
      const { data: recentBookings } = await this.supabase
        .from('bookings')
        .select('*')
        .eq('companion_id', companionId)
        .gte('created_at', thirtyDaysAgo.toISOString());

      // Get profile views (from analytics)
      const { data: profileViews } = await this.supabase
        .from('analytics_events')
        .select('*')
        .eq('event_name', 'companion_profile_viewed')
        .eq('event_properties->companion_id', companionId)
        .gte('created_at', thirtyDaysAgo.toISOString());

      // Get recent reviews
      const { data: recentReviews } = await this.supabase
        .from('user_reviews')
        .select('overall_rating, created_at')
        .eq('companion_id', companionId)
        .gte('created_at', thirtyDaysAgo.toISOString());

      const bookingCount = recentBookings?.length || 0;
      const viewCount = profileViews?.length || 0;
      const conversionRate = viewCount > 0 ? (bookingCount / viewCount) * 100 : 0;
      const averageRating = recentReviews?.length > 0
        ? recentReviews.reduce((sum: number, review: any) => sum + review.overall_rating, 0) / recentReviews.length
        : 0;

      return {
        profileViews: viewCount,
        bookings: bookingCount,
        conversionRate: conversionRate.toFixed(1),
        averageRating: averageRating.toFixed(1),
        responseTime: '2.5 hours', // This would be calculated from message response times
        bookingTrends: this.generateBookingTrends(recentBookings || []),
        topPerformingDays: this.getTopPerformingDays(recentBookings || []),
      };
    } catch (error) {
      console.error('Error getting performance insights:', error);
      return {};
    }
  }

  private generateBookingTrends(bookings: any[]): any[] {
    const dailyBookings: { [key: string]: number } = {};
    
    bookings.forEach(booking => {
      const date = new Date(booking.created_at).toISOString().split('T')[0];
      dailyBookings[date] = (dailyBookings[date] || 0) + 1;
    });

    return Object.entries(dailyBookings).map(([date, count]) => ({
      date,
      bookings: count,
    }));
  }

  private getTopPerformingDays(bookings: any[]): any[] {
    const dayBookings: { [key: string]: number } = {};
    
    bookings.forEach(booking => {
      const day = new Date(booking.booking_time).toLocaleDateString('en-US', { weekday: 'long' });
      dayBookings[day] = (dayBookings[day] || 0) + 1;
    });

    return Object.entries(dayBookings)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([day, count]) => ({ day, bookings: count }));
  }

  // Companion Dashboard Data
  async getDashboardData(companionId: string): Promise<any> {
    try {
      const today = new Date();
      const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);

      // Get this month's data
      const [earnings, bookings, analytics] = await Promise.all([
        this.getEarnings(companionId, undefined, 100),
        this.getMonthlyBookings(companionId, thisMonth),
        this.getCompanionAnalytics(companionId, thisMonth, today),
      ]);

      const thisMonthEarnings = earnings
        .filter(e => new Date(e.availableAt || e.paidAt || 0) >= thisMonth)
        .reduce((sum, e) => sum + e.netAmount, 0);

      const availableEarnings = await this.getAvailableEarnings(companionId);

      return {
        thisMonthEarnings,
        availableEarnings,
        thisMonthBookings: bookings.length,
        averageRating: analytics.averageRating,
        completionRate: analytics.completionRate,
        responseRate: analytics.responseRate,
        upcomingBookings: await this.getUpcomingBookings(companionId),
        recentReviews: await this.getRecentReviews(companionId, 5),
        performanceInsights: await this.getPerformanceInsights(companionId),
      };
    } catch (error) {
      console.error('Error getting dashboard data:', error);
      return {};
    }
  }

  private async getMonthlyBookings(companionId: string, startDate: Date): Promise<any[]> {
    const endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0);
    
    const { data, error } = await this.supabase
      .from('bookings')
      .select('*')
      .eq('companion_id', companionId)
      .gte('booking_time', startDate.toISOString())
      .lte('booking_time', endDate.toISOString());

    if (error) throw error;
    return data || [];
  }

  private async getUpcomingBookings(companionId: string): Promise<any[]> {
    const { data, error } = await this.supabase
      .from('bookings')
      .select(`
        *,
        profiles(full_name, avatar_url)
      `)
      .eq('companion_id', companionId)
      .gte('booking_time', new Date().toISOString())
      .eq('status', 'confirmed')
      .order('booking_time', { ascending: true })
      .limit(5);

    if (error) throw error;
    return data || [];
  }

  private async getRecentReviews(companionId: string, limit: number): Promise<any[]> {
    const { data, error } = await this.supabase
      .from('user_reviews')
      .select(`
        *,
        profiles(full_name, avatar_url)
      `)
      .eq('companion_id', companionId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data || [];
  }
}