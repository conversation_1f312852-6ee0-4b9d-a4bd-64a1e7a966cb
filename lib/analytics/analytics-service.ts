import { Platform } from 'react-native';
import { createClient } from '@supabase/supabase-js';

export interface AnalyticsEvent {
  name: string;
  category: string;
  properties?: Record<string, any>;
  userProperties?: Record<string, any>;
  value?: number;
}

export interface UserSegment {
  name: string;
  value: string;
  confidence?: number;
}

export interface ABTestVariant {
  testName: string;
  variantName: string;
  userId: string;
}

export class AnalyticsService {
  private static instance: AnalyticsService;
  private supabase: any;
  private sessionId: string;
  private userId: string | null = null;
  private eventQueue: AnalyticsEvent[] = [];
  private isOnline = true;

  static getInstance(): AnalyticsService {
    if (!AnalyticsService.instance) {
      AnalyticsService.instance = new AnalyticsService();
    }
    return AnalyticsService.instance;
  }

  initialize(supabaseClient: any, userId?: string): void {
    this.supabase = supabaseClient;
    this.userId = userId || null;
    this.sessionId = this.generateSessionId();
    
    // Monitor network status
    if (Platform.OS === 'web') {
      window.addEventListener('online', () => {
        this.isOnline = true;
        this.flushEventQueue();
      });
      window.addEventListener('offline', () => {
        this.isOnline = false;
      });
    }

    // Flush queue periodically
    setInterval(() => {
      this.flushEventQueue();
    }, 30000); // Every 30 seconds
  }

  // Track user events
  async track(event: AnalyticsEvent): Promise<void> {
    const eventData = {
      user_id: this.userId,
      session_id: this.sessionId,
      event_name: event.name,
      event_category: event.category,
      event_properties: event.properties || {},
      user_properties: event.userProperties || {},
      device_info: this.getDeviceInfo(),
      created_at: new Date().toISOString(),
    };

    if (this.isOnline) {
      try {
        await this.sendEvent(eventData);
      } catch (error) {
        console.error('Error sending analytics event:', error);
        this.eventQueue.push(event);
      }
    } else {
      this.eventQueue.push(event);
    }
  }

  // Track page/screen views
  async trackPageView(pageName: string, properties?: Record<string, any>): Promise<void> {
    await this.track({
      name: 'page_view',
      category: 'navigation',
      properties: {
        page_name: pageName,
        ...properties,
      },
    });
  }

  // Track user actions
  async trackAction(action: string, category: string, properties?: Record<string, any>): Promise<void> {
    await this.track({
      name: action,
      category,
      properties,
    });
  }

  // Track conversion events
  async trackConversion(eventName: string, value?: number, properties?: Record<string, any>): Promise<void> {
    await this.track({
      name: eventName,
      category: 'conversion',
      properties,
      value,
    });

    // Update A/B test conversion if user is in a test
    if (this.userId) {
      await this.updateABTestConversion(eventName, value);
    }
  }

  // Track user properties
  async setUserProperties(properties: Record<string, any>): Promise<void> {
    await this.track({
      name: 'user_properties_updated',
      category: 'user',
      userProperties: properties,
    });
  }

  // User segmentation
  async assignUserToSegment(segment: UserSegment): Promise<void> {
    if (!this.userId) return;

    try {
      await this.supabase
        .from('user_segments')
        .upsert({
          user_id: this.userId,
          segment_name: segment.name,
          segment_value: segment.value,
          confidence_score: segment.confidence || 1.0,
          updated_at: new Date().toISOString(),
        });
    } catch (error) {
      console.error('Error assigning user segment:', error);
    }
  }

  async getUserSegments(): Promise<UserSegment[]> {
    if (!this.userId) return [];

    try {
      const { data, error } = await this.supabase
        .from('user_segments')
        .select('segment_name, segment_value, confidence_score')
        .eq('user_id', this.userId);

      if (error) throw error;

      return data?.map((item: any) => ({
        name: item.segment_name,
        value: item.segment_value,
        confidence: item.confidence_score,
      })) || [];
    } catch (error) {
      console.error('Error getting user segments:', error);
      return [];
    }
  }

  // A/B Testing
  async assignToABTest(testName: string, variants: string[]): Promise<string> {
    if (!this.userId) return variants[0];

    try {
      // Check if user is already assigned
      const { data: existing } = await this.supabase
        .from('ab_test_variants')
        .select('variant_name')
        .eq('test_name', testName)
        .eq('user_id', this.userId)
        .maybeSingle();

      if (existing) {
        return existing.variant_name;
      }

      // Assign random variant
      const variantName = variants[Math.floor(Math.random() * variants.length)];

      await this.supabase
        .from('ab_test_variants')
        .insert({
          test_name: testName,
          variant_name: variantName,
          user_id: this.userId,
        });

      await this.track({
        name: 'ab_test_assigned',
        category: 'experiment',
        properties: {
          test_name: testName,
          variant_name: variantName,
        },
      });

      return variantName;
    } catch (error) {
      console.error('Error assigning A/B test variant:', error);
      return variants[0];
    }
  }

  async getABTestVariant(testName: string): Promise<string | null> {
    if (!this.userId) return null;

    try {
      const { data } = await this.supabase
        .from('ab_test_variants')
        .select('variant_name')
        .eq('test_name', testName)
        .eq('user_id', this.userId)
        .maybeSingle();

      return data?.variant_name || null;
    } catch (error) {
      console.error('Error getting A/B test variant:', error);
      return null;
    }
  }

  private async updateABTestConversion(eventName: string, value?: number): Promise<void> {
    if (!this.userId) return;

    try {
      await this.supabase
        .from('ab_test_variants')
        .update({
          converted: true,
          converted_at: new Date().toISOString(),
          conversion_value: value || 0,
        })
        .eq('user_id', this.userId)
        .eq('converted', false);
    } catch (error) {
      console.error('Error updating A/B test conversion:', error);
    }
  }

  // Analytics queries
  async getEventCounts(
    startDate: Date,
    endDate: Date,
    eventName?: string,
    category?: string
  ): Promise<any[]> {
    try {
      let query = this.supabase
        .from('analytics_events')
        .select('event_name, event_category, created_at')
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString());

      if (eventName) {
        query = query.eq('event_name', eventName);
      }

      if (category) {
        query = query.eq('event_category', category);
      }

      const { data, error } = await query;
      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('Error getting event counts:', error);
      return [];
    }
  }

  async getUserJourney(userId: string, limit = 100): Promise<any[]> {
    try {
      const { data, error } = await this.supabase
        .from('analytics_events')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting user journey:', error);
      return [];
    }
  }

  // Funnel analysis
  async getFunnelAnalysis(steps: string[], timeWindow = 24): Promise<any> {
    try {
      const { data, error } = await this.supabase.rpc('analyze_funnel', {
        funnel_steps: steps,
        time_window_hours: timeWindow,
      });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error analyzing funnel:', error);
      return null;
    }
  }

  // Cohort analysis
  async getCohortAnalysis(startDate: Date, endDate: Date): Promise<any> {
    try {
      const { data, error } = await this.supabase.rpc('analyze_cohorts', {
        start_date: startDate.toISOString(),
        end_date: endDate.toISOString(),
      });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error analyzing cohorts:', error);
      return null;
    }
  }

  // Revenue analytics
  async getRevenueMetrics(startDate: Date, endDate: Date): Promise<any> {
    try {
      const { data, error } = await this.supabase.rpc('get_revenue_metrics', {
        start_date: startDate.toISOString(),
        end_date: endDate.toISOString(),
      });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error getting revenue metrics:', error);
      return null;
    }
  }

  private async sendEvent(eventData: any): Promise<void> {
    const { error } = await this.supabase
      .from('analytics_events')
      .insert([eventData]);

    if (error) throw error;
  }

  private async flushEventQueue(): Promise<void> {
    if (this.eventQueue.length === 0 || !this.isOnline) return;

    const events = [...this.eventQueue];
    this.eventQueue = [];

    try {
      for (const event of events) {
        await this.track(event);
      }
    } catch (error) {
      console.error('Error flushing event queue:', error);
      // Re-add failed events to queue
      this.eventQueue.unshift(...events);
    }
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getDeviceInfo(): Record<string, any> {
    if (Platform.OS === 'web') {
      return {
        platform: 'web',
        user_agent: navigator.userAgent,
        screen_width: window.screen.width,
        screen_height: window.screen.height,
        viewport_width: window.innerWidth,
        viewport_height: window.innerHeight,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      };
    }

    return {
      platform: Platform.OS,
      version: Platform.Version,
    };
  }

  // Cleanup old events (call periodically)
  async cleanupOldEvents(daysToKeep = 90): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      await this.supabase
        .from('analytics_events')
        .delete()
        .lt('created_at', cutoffDate.toISOString());
    } catch (error) {
      console.error('Error cleaning up old events:', error);
    }
  }
}