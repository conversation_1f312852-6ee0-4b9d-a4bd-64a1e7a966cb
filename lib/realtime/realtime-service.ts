import { Platform } from 'react-native';
import { io, Socket } from 'socket.io-client';
import { createClient } from '@supabase/supabase-js';

export interface RealtimeEvent {
  type: string;
  payload: any;
  timestamp: number;
  userId?: string;
  roomId?: string;
}

export interface TypingIndicator {
  userId: string;
  userName: string;
  conversationId: string;
  isTyping: boolean;
}

export interface OnlineStatus {
  userId: string;
  isOnline: boolean;
  lastSeen: Date;
}

export interface LocationUpdate {
  userId: string;
  latitude: number;
  longitude: number;
  accuracy?: number;
  timestamp: number;
  isSharing: boolean;
}

export class RealtimeService {
  private static instance: RealtimeService;
  private socket: Socket | null = null;
  private supabase: any;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private eventListeners: Map<string, Function[]> = new Map();
  private typingTimeouts: Map<string, NodeJS.Timeout> = new Map();
  private heartbeatInterval: NodeJS.Timeout | null = null;

  static getInstance(): RealtimeService {
    if (!RealtimeService.instance) {
      RealtimeService.instance = new RealtimeService();
    }
    return RealtimeService.instance;
  }

  async initialize(supabaseClient: any, userId: string): Promise<void> {
    this.supabase = supabaseClient;
    
    if (Platform.OS === 'web') {
      await this.initializeWebSocket(userId);
    }
    
    await this.initializeSupabaseRealtime(userId);
    await this.updateOnlineStatus(userId, true);
    this.startHeartbeat(userId);
  }

  private async initializeWebSocket(userId: string): Promise<void> {
    try {
      const socketUrl = process.env.EXPO_PUBLIC_SOCKET_URL || 'ws://localhost:3001';
      
      this.socket = io(socketUrl, {
        auth: { userId },
        transports: ['websocket', 'polling'],
        timeout: 20000,
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: 1000,
      });

      this.socket.on('connect', () => {
        console.log('Socket.io connected');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.emit('connection_status', { connected: true });
      });

      this.socket.on('disconnect', (reason) => {
        console.log('Socket.io disconnected:', reason);
        this.isConnected = false;
        this.emit('connection_status', { connected: false, reason });
      });

      this.socket.on('reconnect', (attemptNumber) => {
        console.log('Socket.io reconnected after', attemptNumber, 'attempts');
        this.isConnected = true;
        this.emit('connection_status', { connected: true, reconnected: true });
      });

      this.socket.on('reconnect_error', (error) => {
        this.reconnectAttempts++;
        console.error('Socket.io reconnection error:', error);
        
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
          this.emit('connection_error', { 
            error: 'Max reconnection attempts reached',
            attempts: this.reconnectAttempts 
          });
        }
      });

      // Real-time event handlers
      this.socket.on('typing_indicator', (data: TypingIndicator) => {
        this.emit('typing_indicator', data);
      });

      this.socket.on('user_online_status', (data: OnlineStatus) => {
        this.emit('user_online_status', data);
      });

      this.socket.on('location_update', (data: LocationUpdate) => {
        this.emit('location_update', data);
      });

      this.socket.on('booking_update', (data: any) => {
        this.emit('booking_update', data);
      });

      this.socket.on('message_read', (data: any) => {
        this.emit('message_read', data);
      });

    } catch (error) {
      console.error('Error initializing WebSocket:', error);
      throw error;
    }
  }

  private async initializeSupabaseRealtime(userId: string): Promise<void> {
    try {
      // Subscribe to conversations
      const conversationsChannel = this.supabase
        .channel('conversations')
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'conversations',
            filter: `user_id=eq.${userId}`,
          },
          (payload: any) => {
            this.emit('conversation_update', payload);
          }
        )
        .subscribe();

      // Subscribe to messages
      const messagesChannel = this.supabase
        .channel('messages')
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'messages',
          },
          (payload: any) => {
            this.emit('new_message', payload.new);
          }
        )
        .subscribe();

      // Subscribe to bookings
      const bookingsChannel = this.supabase
        .channel('bookings')
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'bookings',
            filter: `user_id=eq.${userId}`,
          },
          (payload: any) => {
            this.emit('booking_realtime_update', payload);
          }
        )
        .subscribe();

      // Subscribe to emergency incidents
      const emergencyChannel = this.supabase
        .channel('emergency_incidents')
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'emergency_incidents',
            filter: `user_id=eq.${userId}`,
          },
          (payload: any) => {
            this.emit('emergency_update', payload);
          }
        )
        .subscribe();

    } catch (error) {
      console.error('Error initializing Supabase realtime:', error);
    }
  }

  // Event management
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  off(event: string, callback?: Function): void {
    if (!this.eventListeners.has(event)) return;
    
    if (callback) {
      const listeners = this.eventListeners.get(event)!;
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    } else {
      this.eventListeners.delete(event);
    }
  }

  private emit(event: string, data: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => callback(data));
    }
  }

  // Typing indicators
  startTyping(conversationId: string, userId: string, userName: string): void {
    if (!this.socket || !this.isConnected) return;

    this.socket.emit('start_typing', {
      conversationId,
      userId,
      userName,
      timestamp: Date.now(),
    });

    // Clear existing timeout
    const timeoutKey = `${conversationId}_${userId}`;
    if (this.typingTimeouts.has(timeoutKey)) {
      clearTimeout(this.typingTimeouts.get(timeoutKey)!);
    }

    // Auto-stop typing after 3 seconds
    const timeout = setTimeout(() => {
      this.stopTyping(conversationId, userId);
    }, 3000);

    this.typingTimeouts.set(timeoutKey, timeout);
  }

  stopTyping(conversationId: string, userId: string): void {
    if (!this.socket || !this.isConnected) return;

    this.socket.emit('stop_typing', {
      conversationId,
      userId,
      timestamp: Date.now(),
    });

    // Clear timeout
    const timeoutKey = `${conversationId}_${userId}`;
    if (this.typingTimeouts.has(timeoutKey)) {
      clearTimeout(this.typingTimeouts.get(timeoutKey)!);
      this.typingTimeouts.delete(timeoutKey);
    }
  }

  // Online status
  async updateOnlineStatus(userId: string, isOnline: boolean): Promise<void> {
    try {
      // Update in database
      await this.supabase
        .from('user_online_status')
        .upsert({
          user_id: userId,
          is_online: isOnline,
          last_seen: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });

      // Broadcast via socket
      if (this.socket && this.isConnected) {
        this.socket.emit('user_status_update', {
          userId,
          isOnline,
          lastSeen: new Date(),
        });
      }
    } catch (error) {
      console.error('Error updating online status:', error);
    }
  }

  // Location sharing
  shareLocation(userId: string, location: LocationUpdate): void {
    if (!this.socket || !this.isConnected) return;

    this.socket.emit('location_update', {
      userId,
      ...location,
      timestamp: Date.now(),
    });
  }

  // Message read receipts
  markMessageAsRead(messageId: string, conversationId: string, userId: string): void {
    if (!this.socket || !this.isConnected) return;

    this.socket.emit('message_read', {
      messageId,
      conversationId,
      userId,
      readAt: new Date().toISOString(),
    });
  }

  // Join/leave rooms
  joinRoom(roomId: string): void {
    if (!this.socket || !this.isConnected) return;
    this.socket.emit('join_room', { roomId });
  }

  leaveRoom(roomId: string): void {
    if (!this.socket || !this.isConnected) return;
    this.socket.emit('leave_room', { roomId });
  }

  // Heartbeat to maintain connection
  private startHeartbeat(userId: string): void {
    this.heartbeatInterval = setInterval(async () => {
      if (this.isConnected) {
        await this.updateOnlineStatus(userId, true);
      }
    }, 30000); // Every 30 seconds
  }

  // Emergency alerts
  triggerEmergencyAlert(userId: string, incidentData: any): void {
    if (!this.socket || !this.isConnected) return;

    this.socket.emit('emergency_alert', {
      userId,
      ...incidentData,
      timestamp: Date.now(),
      priority: 'critical',
    });
  }

  // Booking updates
  broadcastBookingUpdate(bookingId: string, updateData: any): void {
    if (!this.socket || !this.isConnected) return;

    this.socket.emit('booking_update', {
      bookingId,
      ...updateData,
      timestamp: Date.now(),
    });
  }

  // Connection status
  isSocketConnected(): boolean {
    return this.isConnected && this.socket?.connected === true;
  }

  getConnectionInfo(): any {
    return {
      connected: this.isConnected,
      socketId: this.socket?.id,
      transport: this.socket?.io.engine.transport.name,
      reconnectAttempts: this.reconnectAttempts,
    };
  }

  // Cleanup
  disconnect(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    // Clear all typing timeouts
    this.typingTimeouts.forEach(timeout => clearTimeout(timeout));
    this.typingTimeouts.clear();

    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }

    this.isConnected = false;
    this.eventListeners.clear();
  }
}