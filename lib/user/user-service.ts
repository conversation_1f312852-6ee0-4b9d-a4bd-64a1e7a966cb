import { createClient } from '@supabase/supabase-js';

export interface UserPreferences {
  ageRangeMin: number;
  ageRangeMax: number;
  maxDistanceKm: number;
  priceRangeMin: number;
  priceRangeMax: number;
  preferredLanguages: string[];
  preferredInterests: string[];
  preferredLocations: string[];
  notificationPreferences: Record<string, any>;
  matchingPreferences: Record<string, any>;
  privacySettings: Record<string, any>;
}

export interface UserFavorite {
  id: string;
  type: 'companion' | 'location' | 'search';
  companionId?: string;
  locationData?: any;
  searchCriteria?: any;
  notes?: string;
  createdAt: Date;
}

export interface RecentlyViewed {
  id: string;
  companionId: string;
  viewDuration: number;
  viewedAt: Date;
  companion?: any;
}

export interface SearchFilters {
  ageMin?: number;
  ageMax?: number;
  priceMin?: number;
  priceMax?: number;
  maxDistance?: number;
  languages?: string[];
  interests?: string[];
  location?: string;
  isVerified?: boolean;
  isOnline?: boolean;
  rating?: number;
  availability?: 'now' | 'today' | 'week';
}

export interface SortOption {
  field: 'distance' | 'price' | 'rating' | 'created_at' | 'last_active';
  direction: 'asc' | 'desc';
}

export class UserService {
  private static instance: UserService;
  private supabase: any;

  static getInstance(): UserService {
    if (!UserService.instance) {
      UserService.instance = new UserService();
    }
    return UserService.instance;
  }

  initialize(supabaseClient: any): void {
    this.supabase = supabaseClient;
  }

  // User Preferences
  async getUserPreferences(userId: string): Promise<UserPreferences | null> {
    try {
      const { data, error } = await this.supabase
        .from('user_preferences')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) throw error;

      return {
        ageRangeMin: data.age_range_min,
        ageRangeMax: data.age_range_max,
        maxDistanceKm: data.max_distance_km,
        priceRangeMin: data.price_range_min,
        priceRangeMax: data.price_range_max,
        preferredLanguages: data.preferred_languages,
        preferredInterests: data.preferred_interests,
        preferredLocations: data.preferred_locations,
        notificationPreferences: data.notification_preferences,
        matchingPreferences: data.matching_preferences,
        privacySettings: data.privacy_settings,
      };
    } catch (error) {
      console.error('Error getting user preferences:', error);
      return null;
    }
  }

  async updateUserPreferences(userId: string, preferences: Partial<UserPreferences>): Promise<void> {
    try {
      const updateData: any = {
        updated_at: new Date().toISOString(),
      };

      if (preferences.ageRangeMin !== undefined) updateData.age_range_min = preferences.ageRangeMin;
      if (preferences.ageRangeMax !== undefined) updateData.age_range_max = preferences.ageRangeMax;
      if (preferences.maxDistanceKm !== undefined) updateData.max_distance_km = preferences.maxDistanceKm;
      if (preferences.priceRangeMin !== undefined) updateData.price_range_min = preferences.priceRangeMin;
      if (preferences.priceRangeMax !== undefined) updateData.price_range_max = preferences.priceRangeMax;
      if (preferences.preferredLanguages !== undefined) updateData.preferred_languages = preferences.preferredLanguages;
      if (preferences.preferredInterests !== undefined) updateData.preferred_interests = preferences.preferredInterests;
      if (preferences.preferredLocations !== undefined) updateData.preferred_locations = preferences.preferredLocations;
      if (preferences.notificationPreferences !== undefined) updateData.notification_preferences = preferences.notificationPreferences;
      if (preferences.matchingPreferences !== undefined) updateData.matching_preferences = preferences.matchingPreferences;
      if (preferences.privacySettings !== undefined) updateData.privacy_settings = preferences.privacySettings;

      const { error } = await this.supabase
        .from('user_preferences')
        .update(updateData)
        .eq('user_id', userId);

      if (error) throw error;
    } catch (error) {
      console.error('Error updating user preferences:', error);
      throw error;
    }
  }

  // Favorites System
  async addToFavorites(
    userId: string,
    type: 'companion' | 'location' | 'search',
    data: { companionId?: string; locationData?: any; searchCriteria?: any; notes?: string }
  ): Promise<UserFavorite> {
    try {
      const { data: favorite, error } = await this.supabase
        .from('user_favorites')
        .insert({
          user_id: userId,
          favorite_type: type,
          companion_id: data.companionId,
          location_data: data.locationData,
          search_criteria: data.searchCriteria,
          notes: data.notes,
        })
        .select()
        .single();

      if (error) throw error;

      return {
        id: favorite.id,
        type: favorite.favorite_type,
        companionId: favorite.companion_id,
        locationData: favorite.location_data,
        searchCriteria: favorite.search_criteria,
        notes: favorite.notes,
        createdAt: new Date(favorite.created_at),
      };
    } catch (error) {
      console.error('Error adding to favorites:', error);
      throw error;
    }
  }

  async removeFromFavorites(userId: string, favoriteId: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('user_favorites')
        .delete()
        .eq('id', favoriteId)
        .eq('user_id', userId);

      if (error) throw error;
    } catch (error) {
      console.error('Error removing from favorites:', error);
      throw error;
    }
  }

  async getFavorites(userId: string, type?: 'companion' | 'location' | 'search'): Promise<UserFavorite[]> {
    try {
      let query = this.supabase
        .from('user_favorites')
        .select(`
          *,
          companions(
            *,
            profiles(*)
          )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (type) {
        query = query.eq('favorite_type', type);
      }

      const { data, error } = await query;
      if (error) throw error;

      return data?.map((item: any) => ({
        id: item.id,
        type: item.favorite_type,
        companionId: item.companion_id,
        locationData: item.location_data,
        searchCriteria: item.search_criteria,
        notes: item.notes,
        createdAt: new Date(item.created_at),
        companion: item.companions,
      })) || [];
    } catch (error) {
      console.error('Error getting favorites:', error);
      return [];
    }
  }

  async isFavorite(userId: string, companionId: string): Promise<boolean> {
    try {
      const { data, error } = await this.supabase
        .from('user_favorites')
        .select('id')
        .eq('user_id', userId)
        .eq('favorite_type', 'companion')
        .eq('companion_id', companionId)
        .maybeSingle();

      if (error && error.code !== 'PGRST116') throw error;
      return !!data;
    } catch (error) {
      console.error('Error checking if favorite:', error);
      return false;
    }
  }

  // Recently Viewed
  async trackRecentlyViewed(userId: string, companionId: string, viewDuration = 0): Promise<void> {
    try {
      await this.supabase.rpc('track_recently_viewed', {
        p_user_id: userId,
        p_companion_id: companionId,
      });

      // Update view duration if provided
      if (viewDuration > 0) {
        await this.supabase
          .from('recently_viewed')
          .update({ view_duration_seconds: viewDuration })
          .eq('user_id', userId)
          .eq('companion_id', companionId);
      }
    } catch (error) {
      console.error('Error tracking recently viewed:', error);
    }
  }

  async getRecentlyViewed(userId: string, limit = 20): Promise<RecentlyViewed[]> {
    try {
      const { data, error } = await this.supabase
        .from('recently_viewed')
        .select(`
          *,
          companions(
            *,
            profiles(*)
          )
        `)
        .eq('user_id', userId)
        .order('viewed_at', { ascending: false })
        .limit(limit);

      if (error) throw error;

      return data?.map((item: any) => ({
        id: item.id,
        companionId: item.companion_id,
        viewDuration: item.view_duration_seconds,
        viewedAt: new Date(item.viewed_at),
        companion: item.companions,
      })) || [];
    } catch (error) {
      console.error('Error getting recently viewed:', error);
      return [];
    }
  }

  // Advanced Search
  async searchCompanions(
    filters: SearchFilters,
    sort: SortOption,
    userLocation?: { latitude: number; longitude: number },
    limit = 20,
    offset = 0
  ): Promise<any[]> {
    try {
      let query = this.supabase
        .from('companions')
        .select(`
          *,
          profiles(*),
          companion_photos(photo_url, is_primary),
          user_reviews(overall_rating)
        `)
        .eq('is_active', true);

      // Apply filters
      if (filters.ageMin) query = query.gte('age', filters.ageMin);
      if (filters.ageMax) query = query.lte('age', filters.ageMax);
      if (filters.priceMin) query = query.gte('hourly_rate', filters.priceMin);
      if (filters.priceMax) query = query.lte('hourly_rate', filters.priceMax);
      if (filters.languages?.length) query = query.overlaps('languages', filters.languages);
      if (filters.interests?.length) query = query.overlaps('interests_array', filters.interests);
      if (filters.isVerified) query = query.eq('is_verified', true);
      if (filters.rating) query = query.gte('rating', filters.rating);

      // Apply sorting
      switch (sort.field) {
        case 'price':
          query = query.order('hourly_rate', { ascending: sort.direction === 'asc' });
          break;
        case 'rating':
          query = query.order('rating', { ascending: sort.direction === 'asc' });
          break;
        case 'created_at':
          query = query.order('created_at', { ascending: sort.direction === 'asc' });
          break;
        default:
          query = query.order('created_at', { ascending: false });
      }

      query = query.range(offset, offset + limit - 1);

      const { data, error } = await query;
      if (error) throw error;

      let companions = data || [];

      // Apply distance filtering and sorting if user location provided
      if (userLocation && filters.maxDistance) {
        companions = companions.filter((companion: any) => {
          if (!companion.current_latitude || !companion.current_longitude) return false;
          
          const distance = this.calculateDistance(
            userLocation.latitude,
            userLocation.longitude,
            parseFloat(companion.current_latitude),
            parseFloat(companion.current_longitude)
          );
          
          return distance <= filters.maxDistance!;
        });

        if (sort.field === 'distance') {
          companions.sort((a: any, b: any) => {
            const distanceA = this.calculateDistance(
              userLocation.latitude,
              userLocation.longitude,
              parseFloat(a.current_latitude),
              parseFloat(a.current_longitude)
            );
            const distanceB = this.calculateDistance(
              userLocation.latitude,
              userLocation.longitude,
              parseFloat(b.current_latitude),
              parseFloat(b.current_longitude)
            );
            
            return sort.direction === 'asc' ? distanceA - distanceB : distanceB - distanceA;
          });
        }
      }

      return companions;
    } catch (error) {
      console.error('Error searching companions:', error);
      return [];
    }
  }

  // Personalized recommendations
  async getPersonalizedRecommendations(userId: string, limit = 10): Promise<any[]> {
    try {
      // Get user preferences
      const preferences = await this.getUserPreferences(userId);
      if (!preferences) return [];

      // Get user's booking history for collaborative filtering
      const { data: bookingHistory } = await this.supabase
        .from('bookings')
        .select('companion_id')
        .eq('user_id', userId)
        .eq('status', 'completed');

      const bookedCompanionIds = bookingHistory?.map((b: any) => b.companion_id) || [];

      // Get companions matching preferences
      let query = this.supabase
        .from('companions')
        .select(`
          *,
          profiles(*),
          companion_photos(photo_url, is_primary)
        `)
        .eq('is_active', true)
        .gte('age', preferences.ageRangeMin)
        .lte('age', preferences.ageRangeMax)
        .gte('hourly_rate', preferences.priceRangeMin)
        .lte('hourly_rate', preferences.priceRangeMax);

      if (preferences.preferredLanguages.length > 0) {
        query = query.overlaps('languages', preferences.preferredLanguages);
      }

      if (preferences.preferredInterests.length > 0) {
        query = query.overlaps('interests_array', preferences.preferredInterests);
      }

      // Exclude already booked companions
      if (bookedCompanionIds.length > 0) {
        query = query.not('id', 'in', `(${bookedCompanionIds.join(',')})`);
      }

      query = query
        .order('rating', { ascending: false })
        .limit(limit);

      const { data, error } = await query;
      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('Error getting personalized recommendations:', error);
      return [];
    }
  }

  // Onboarding
  async getOnboardingProgress(userId: string): Promise<Record<string, boolean>> {
    try {
      const { data, error } = await this.supabase
        .from('onboarding_progress')
        .select('step_name, completed')
        .eq('user_id', userId);

      if (error) throw error;

      const progress: Record<string, boolean> = {};
      data?.forEach((item: any) => {
        progress[item.step_name] = item.completed;
      });

      return progress;
    } catch (error) {
      console.error('Error getting onboarding progress:', error);
      return {};
    }
  }

  async updateOnboardingStep(userId: string, stepName: string, completed = true, data?: any): Promise<void> {
    try {
      await this.supabase
        .from('onboarding_progress')
        .upsert({
          user_id: userId,
          step_name: stepName,
          completed,
          completed_at: completed ? new Date().toISOString() : null,
          data: data || {},
        });
    } catch (error) {
      console.error('Error updating onboarding step:', error);
    }
  }

  // Referral System
  async createReferral(referrerId: string, email?: string, phoneNumber?: string): Promise<string> {
    try {
      const { data, error } = await this.supabase
        .from('user_referrals')
        .insert({
          referrer_id: referrerId,
          email,
          phone_number: phoneNumber,
          referral_code: await this.generateReferralCode(),
          reward_amount: 25, // $25 credit
          reward_type: 'credit',
        })
        .select('referral_code')
        .single();

      if (error) throw error;
      return data.referral_code;
    } catch (error) {
      console.error('Error creating referral:', error);
      throw error;
    }
  }

  async processReferral(referralCode: string, newUserId: string): Promise<boolean> {
    try {
      const { data: referral, error: fetchError } = await this.supabase
        .from('user_referrals')
        .select('*')
        .eq('referral_code', referralCode)
        .eq('status', 'pending')
        .gt('expires_at', new Date().toISOString())
        .single();

      if (fetchError || !referral) return false;

      // Update referral status
      const { error: updateError } = await this.supabase
        .from('user_referrals')
        .update({
          referred_id: newUserId,
          status: 'signed_up',
        })
        .eq('id', referral.id);

      if (updateError) throw updateError;

      return true;
    } catch (error) {
      console.error('Error processing referral:', error);
      return false;
    }
  }

  async getUserReferrals(userId: string): Promise<any[]> {
    try {
      const { data, error } = await this.supabase
        .from('user_referrals')
        .select(`
          *,
          referred:profiles!referred_id(full_name, email)
        `)
        .eq('referrer_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting user referrals:', error);
      return [];
    }
  }

  // Utility functions
  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) *
        Math.cos(this.toRadians(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  private async generateReferralCode(): Promise<string> {
    const { data } = await this.supabase.rpc('generate_referral_code');
    return data;
  }
}