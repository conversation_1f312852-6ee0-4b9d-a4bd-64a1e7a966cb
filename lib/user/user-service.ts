import { createClient } from '@supabase/supabase-js';

export interface UserPreferences {
  ageRangeMin: number;
  ageRangeMax: number;
  maxDistanceKm: number;
  priceRangeMin: number;
  priceRangeMax: number;
  preferredLanguages: string[];
  preferredInterests: string[];
  preferredLocations: string[];
  notificationPreferences: Record<string, any>;
  matchingPreferences: Record<string, any>;
  privacySettings: Record<string, any>;
}

export interface UserFavorite {
  id: string;
  type: 'companion' | 'location' | 'search';
  companionId?: string;
  locationData?: any;
  searchCriteria?: any;
  notes?: string;
  createdAt: Date;
}

export interface RecentlyViewed {
  id: string;
  companionId: string;
  viewDuration: number;
  viewedAt: Date;
  companion?: any;
}

export interface SearchFilters {
  ageMin?: number;
  ageMax?: number;
  priceMin?: number;
  priceMax?: number;
  maxDistance?: number;
  languages?: string[];
  interests?: string[];
  location?: string;
  isVerified?: boolean;
  isOnline?: boolean;
  rating?: number;
  availability?: 'now' | 'today' | 'week';
}

export interface SortOption {
  field: 'distance' | 'price' | 'rating' | 'created_at' | 'last_active';
  direction: 'asc' | 'desc';
}

export class UserService {
  private static instance: UserService;
  private supabase: any;

  static getInstance(): UserService {
    if (!UserService.instance) {
      UserService.instance = new UserService();
    }
    return UserService.instance;
  }

  initialize(supabaseClient: any): void {
    this.supabase = supabaseClient;
  }

  // User Preferences
  async getUserPreferences(userId: string): Promise<UserPreferences | null> {
    try {
      const { data, error } = await this.supabase
        .from('user_preferences')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) throw error;

      return {
        ageRangeMin: data.age_range_min,
        ageRangeMax: data.age_range_max,
        maxDistanceKm: data.max_distance_km,
        priceRangeMin: data.price_range_min,
        priceRangeMax: data.price_range_max,
        preferredLanguages: data.preferred_languages,
        preferredInterests: data.preferred_interests,
        preferredLocations: data.preferred_locations,
        notificationPreferences: data.notification_preferences,
        matchingPreferences: data.matching_preferences,
        privacySettings: data.privacy_settings,
      };
    } catch (error) {
      console.error('Error getting user preferences:', error);
      return null;
    }
  }

  async updateUserPreferences(userId: string, preferences: Partial<UserPreferences>): Promise<void> {
    try {
      const updateData: any = {
        updated_at: new Date().toISOString(),
      };

      if (preferences.ageRangeMin !== undefined) updateData.age_range_min = preferences.ageRangeMin;
      if (preferences.ageRangeMax !== undefined) updateData.age_range_max = preferences.ageRangeMax;
      if (preferences.maxDistanceKm !== undefined) updateData.max_distance_km = preferences.maxDistanceKm;
      if (preferences.priceRangeMin !== undefined) updateData.price_range_min = preferences.priceRangeMin;
      if (preferences.priceRangeMax !== undefined) updateData.price_range_max = preferences.priceRangeMax;
      if (preferences.preferredLanguages !== undefined) updateData.preferred_languages = preferences.preferredLanguages;
      if (preferences.preferredInterests !== undefined) updateData.preferred_interests = preferences.preferredInterests;
      if (preferences.preferredLocations !== undefined) updateData.preferred_locations = preferences.preferredLocations;
      if (preferences.notificationPreferences !== undefined) updateData.notification_preferences = preferences.notificationPreferences;
      if (preferences.matchingPreferences !== undefined) updateData.matching_preferences = preferences.matchingPreferences;
      if (preferences.privacySettings !== undefined) updateData.privacy_settings = preferences.privacySettings;

      const { error } = await this.supabase
        .from('user_preferences')
        .update(updateData)
        .eq('user_id', userId);

      if (error) throw error;
    } catch (error) {
      console.error('Error updating user preferences:', error);
      throw error;
    }
  }

  // Favorites System
  async addToFavorites(
    userId: string,
    type: 'companion' | 'location' | 'search',
    data: { companionId?: string; locationData?: any; searchCriteria?: any; notes?: string }
  ): Promise<UserFavorite> {
    try {
      const { data: favorite, error } = await this.supabase
        .from('user_favorites')
        .insert({
          user_id: userId,
          favorite_type: type,
          companion_id: data.companionId,
          location_data: data.locationData,
          search_criteria: data.searchCriteria,
          notes: data.notes,
        })
        .select()
        .single();

      if (error) throw error;

      return {
        id: favorite.id,
        type: favorite.favorite_type,
        companionId: favorite.companion_id,
        locationData: favorite.location_data,
        searchCriteria: favorite.search_criteria,
        notes: favorite.notes,
        createdAt: new Date(favorite.created_at),
      };
    } catch (error) {
      console.error('Error adding to favorites:', error);
      throw error;
    }
  }

  async removeFromFavorites(userId: string, favoriteId: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('user_favorites')
        .delete()
        .eq('id', favoriteId)
        .eq('user_id', userId);

      if (error) throw error;
    } catch (error) {
      console.error('Error removing from favorites:', error);
      throw error;
    }
  }

  async getFavorites(userId: string, type?: 'companion' | 'location' | 'search'): Promise<UserFavorite[]> {
    try {
      let query = this.supabase
        .from('user_favorites')
        .select(`
          *,
          companions(
            *,
            profiles(*)
          )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (type) {
        query = query.eq('favorite_type', type);
      }

      const { data, error } = await query;
      if (error) throw error;

      return data?.map((item: any) => ({
        id: item.id,
        type: item.favorite_type,
        companionId: item.companion_id,
        locationData: item.location_data,
        searchCriteria: item.search_criteria,
        notes: item.notes,
        createdAt: new Date(item.created_at),
        companion: item.companions,
      })) || [];
    } catch (error) {
      console.error('Error getting favorites:', error);
      return [];
    }
  }

  async isFavorite(userId: string, companionId: string): Promise<boolean> {
    try {
      const { data, error } = await this.supabase
        .from('user_favorites')
        .select('id')
        .eq('user_id', userId)
        .eq('favorite_type', 'companion')
        .eq('companion_id', companionId)
        .maybeSingle();

      if (error && error.code !== 'PGRST116') throw error;
      return !!data;
    } catch (error) {
      console.error('Error checking if favorite:', error);
      return false;
    }
  }

  // Recently Viewed
  async trackRecentlyViewed(userId: string, companionId: string, viewDuration = 0): Promise<void> {
    try {
      await this.supabase.rpc('track_recently_viewed', {
        p_user_id: userId,
        p_companion_id: companionId,
      });

      // Update view duration if provided
      if (viewDuration > 0) {
        await this.supabase
          .from('recently_viewed')
          .update({ view_duration_seconds: viewDuration })
          .eq('user_id', userId)
          .eq('companion_id', companionId);
      }
    } catch (error) {
      console.error('Error tracking recently viewed:', error);
    }
  }

  async getRecentlyViewed(userId: string, limit = 20): Promise<RecentlyViewed[]> {
    try {
      const { data, error } = await this.supabase
        .from('recently_viewed')
        .select(`
          *,
          companions(
            *,
            profiles(*)
          )
        `)
        .eq('user_id', userId)
        .order('viewed_at', { ascending: false })
        .limit(limit);

      if (error) throw error;

      return data?.map((item: any) => ({
        id: item.id,
        companionId: item.companion_id,
        viewDuration: item.view_duration_seconds,
        viewedAt: new Date(item.viewed_at),
        companion: item.companions,
      })) || [];
    } catch (error) {
      console.error('Error getting recently viewed:', error);
      return [];
    }
  }

  // Advanced Search
  async searchCompanions(
    filters: SearchFilters,
    sort: SortOption,
    userLocation?: { latitude: number; longitude: number },
    limit = 20,
    offset = 0
  ): Promise<any[]> {
    try {
      let query = this.supabase
        .from('companions')
        .select(`
          *,
          profiles(*),
          companion_photos(photo_url, is_primary),
          user_reviews(overall_rating)
        `)
        .eq('is_active', true);

      // Apply filters
      if (filters.ageMin) query = query.gte('age', filters.ageMin);
      if (filters.ageMax) query = query.lte('age', filters.ageMax);
      if (filters.priceMin) query = query.gte('hourly_rate', filters.priceMin);
      if (filters.priceMax) query = query.lte('hourly_rate', filters.priceMax);
      if (filters.languages?.length) query = query.overlaps('languages', filters.languages);
      if (filters.interests?.length) query = query.overlaps('interests_array', filters.interests);
      if (filters.isVerified) query = query.eq('is_verified', true);
      if (filters.rating) query = query.gte('rating', filters.rating);

      // Apply sorting
      switch (sort.field) {
        case 'price':
          query = query.order('hourly_rate', { ascending: sort.direction === 'asc' });
          break;
        case 'rating':
          query = query.order('rating', { ascending: sort.direction === 'asc' });
          break;
        case 'created_at':
          query = query.order('created_at', { ascending: sort.direction === 'asc' });
          break;
        default:
          query = query.order('created_at', { ascending: false });
      }

      query = query.range(offset, offset + limit - 1);

      const { data, error } = await query;
      if (error) throw error;

      let companions = data || [];

      // Apply distance filtering and sorting if user location provided
      if (userLocation && filters.maxDistance) {
        companions = companions.filter((companion: any) => {
          if (!companion.current_latitude || !companion.current_longitude) return false;
          
          const distance = this.calculateDistance(
            userLocation.latitude,
            userLocation.longitude,
            parseFloat(companion.current_latitude),
            parseFloat(companion.current_longitude)
          );
          
          return distance <= filters.maxDistance!;
        });

        if (sort.field === 'distance') {
          companions.sort((a: any, b: any) => {
            const distanceA = this.calculateDistance(
              userLocation.latitude,
              userLocation.longitude,
              parseFloat(a.current_latitude),
              parseFloat(a.current_longitude)
            );
            const distanceB = this.calculateDistance(
              userLocation.latitude,
              userLocation.longitude,
              parseFloat(b.current_latitude),
              parseFloat(b.current_longitude)
            );
            
            return sort.direction === 'asc' ? distanceA - distanceB : distanceB - distanceA;
          });
        }
      }

      return companions;
    } catch (error) {
      console.error('Error searching companions:', error);
      return [];
    }
  }

  // Advanced Matching Algorithm with Personalized Recommendations
  async getPersonalizedRecommendations(userId: string, limit = 10): Promise<any[]> {
    try {
      // Get comprehensive user data for advanced matching
      const [preferences, bookingHistory, userProfile, recentlyViewed, favorites] = await Promise.all([
        this.getUserPreferences(userId),
        this.getBookingHistory(userId),
        this.getUserProfile(userId),
        this.getRecentlyViewed(userId, 20),
        this.getFavorites(userId, 'companion')
      ]);

      if (!preferences) return [];

      const bookedCompanionIds = bookingHistory?.map((b: any) => b.companion_id) || [];
      const viewedCompanionIds = recentlyViewed?.map((v: any) => v.companionId) || [];
      const favoriteCompanionIds = favorites?.map((f: any) => f.companionId) || [];

      // Get companions matching basic preferences with additional data for scoring
      let query = this.supabase
        .from('companions')
        .select(`
          *,
          profiles(*),
          companion_photos(photo_url, is_primary),
          user_reviews(overall_rating, communication_rating, punctuality_rating, appearance_rating, experience_rating, created_at),
          bookings(id, status, created_at, user_id)
        `)
        .eq('is_active', true)
        .gte('age', preferences.ageRangeMin)
        .lte('age', preferences.ageRangeMax)
        .gte('hourly_rate', preferences.priceRangeMin)
        .lte('hourly_rate', preferences.priceRangeMax);

      if (preferences.preferredLanguages.length > 0) {
        query = query.overlaps('languages', preferences.preferredLanguages);
      }

      if (preferences.preferredInterests.length > 0) {
        query = query.overlaps('interests_array', preferences.preferredInterests);
      }

      const { data: companions, error } = await query.limit(limit * 3); // Get more for better scoring

      if (error) throw error;

      // Advanced compatibility scoring
      const scoredCompanions = companions?.map((companion: any) => ({
        ...companion,
        compatibilityScore: this.calculateAdvancedCompatibilityScore(
          companion,
          preferences,
          bookingHistory,
          userProfile,
          viewedCompanionIds,
          favoriteCompanionIds
        )
      })) || [];

      // Sort by compatibility score and return top results
      return scoredCompanions
        .sort((a, b) => b.compatibilityScore - a.compatibilityScore)
        .slice(0, limit);

    } catch (error) {
      console.error('Error getting personalized recommendations:', error);
      return [];
    }
  }

  // Advanced Compatibility Scoring Algorithm
  private calculateAdvancedCompatibilityScore(
    companion: any,
    preferences: UserPreferences,
    bookingHistory: any[],
    userProfile: any,
    viewedCompanionIds: string[],
    favoriteCompanionIds: string[]
  ): number {
    let score = 0;

    // Base compatibility (40% weight)
    score += this.calculateBaseCompatibility(companion, preferences) * 0.4;

    // Behavioral patterns (25% weight)
    score += this.calculateBehavioralScore(companion, bookingHistory, viewedCompanionIds, favoriteCompanionIds) * 0.25;

    // Quality metrics (20% weight)
    score += this.calculateQualityScore(companion) * 0.2;

    // Availability and responsiveness (10% weight)
    score += this.calculateAvailabilityScore(companion) * 0.1;

    // Novelty factor (5% weight) - boost for new companions
    score += this.calculateNoveltyScore(companion, bookingHistory, viewedCompanionIds) * 0.05;

    return Math.min(100, Math.max(0, score));
  }

  private calculateBaseCompatibility(companion: any, preferences: UserPreferences): number {
    let score = 0;

    // Age preference match
    const ageMatch = this.calculateAgeCompatibility(companion.age, preferences.ageRangeMin, preferences.ageRangeMax);
    score += ageMatch * 25;

    // Interest overlap
    const interestMatch = this.calculateInterestOverlap(companion.interests_array || [], preferences.preferredInterests);
    score += interestMatch * 30;

    // Language compatibility
    const languageMatch = this.calculateLanguageCompatibility(companion.languages || [], preferences.preferredLanguages);
    score += languageMatch * 20;

    // Price range fit
    const priceMatch = this.calculatePriceCompatibility(companion.hourly_rate, preferences.priceRangeMin, preferences.priceRangeMax);
    score += priceMatch * 25;

    return score;
  }

  private calculateBehavioralScore(
    companion: any,
    bookingHistory: any[],
    viewedCompanionIds: string[],
    favoriteCompanionIds: string[]
  ): number {
    let score = 0;

    // Favorite boost
    if (favoriteCompanionIds.includes(companion.id)) {
      score += 40;
    }

    // Recently viewed boost (shows interest)
    if (viewedCompanionIds.includes(companion.id)) {
      const viewIndex = viewedCompanionIds.indexOf(companion.id);
      score += Math.max(0, 20 - viewIndex * 2); // More recent views get higher scores
    }

    // Similar companion booking patterns
    const similarCompanions = this.findSimilarCompanions(companion, bookingHistory);
    score += Math.min(30, similarCompanions.length * 5);

    // Booking frequency compatibility
    const userBookingFrequency = this.calculateUserBookingFrequency(bookingHistory);
    const companionPopularity = this.calculateCompanionPopularity(companion);
    score += this.calculateFrequencyCompatibility(userBookingFrequency, companionPopularity) * 10;

    return score;
  }

  private calculateQualityScore(companion: any): number {
    let score = 0;

    // Overall rating
    if (companion.rating) {
      score += (companion.rating / 5) * 40;
    }

    // Review quality and quantity
    const reviews = companion.user_reviews || [];
    if (reviews.length > 0) {
      const avgRating = reviews.reduce((sum: number, review: any) => sum + review.overall_rating, 0) / reviews.length;
      score += (avgRating / 5) * 30;

      // Bonus for having multiple reviews
      score += Math.min(15, reviews.length * 2);
    }

    // Verification status
    if (companion.is_verified) {
      score += 15;
    }

    return score;
  }

  private calculateAvailabilityScore(companion: any): number {
    let score = 50; // Base score

    // Response time (if available)
    if (companion.avg_response_time_minutes) {
      if (companion.avg_response_time_minutes <= 30) score += 25;
      else if (companion.avg_response_time_minutes <= 60) score += 15;
      else if (companion.avg_response_time_minutes <= 120) score += 5;
    }

    // Recent activity
    if (companion.last_active) {
      const daysSinceActive = (Date.now() - new Date(companion.last_active).getTime()) / (1000 * 60 * 60 * 24);
      if (daysSinceActive <= 1) score += 25;
      else if (daysSinceActive <= 3) score += 15;
      else if (daysSinceActive <= 7) score += 5;
    }

    return score;
  }

  private calculateNoveltyScore(companion: any, bookingHistory: any[], viewedCompanionIds: string[]): number {
    // Boost new companions that haven't been booked or viewed
    const hasBooked = bookingHistory.some(booking => booking.companion_id === companion.id);
    const hasViewed = viewedCompanionIds.includes(companion.id);

    if (!hasBooked && !hasViewed) {
      return 100; // Full novelty bonus
    } else if (!hasBooked) {
      return 50; // Partial bonus for not booked but viewed
    }

    return 0;
  }

  // Helper methods for compatibility calculations
  private calculateAgeCompatibility(age: number, minAge: number, maxAge: number): number {
    if (age >= minAge && age <= maxAge) {
      const range = maxAge - minAge;
      const optimal = (minAge + maxAge) / 2;
      const distance = Math.abs(age - optimal);
      return Math.max(0, 1 - (distance / (range / 2)));
    }
    return 0;
  }

  private calculateInterestOverlap(companionInterests: string[], userInterests: string[]): number {
    if (userInterests.length === 0) return 0.5; // Neutral if no preferences

    const overlap = companionInterests.filter(interest =>
      userInterests.some(userInterest =>
        userInterest.toLowerCase().includes(interest.toLowerCase()) ||
        interest.toLowerCase().includes(userInterest.toLowerCase())
      )
    ).length;

    return Math.min(1, overlap / Math.max(userInterests.length, 1));
  }

  private calculateLanguageCompatibility(companionLanguages: string[], userLanguages: string[]): number {
    if (userLanguages.length === 0) return 0.5; // Neutral if no preferences

    const overlap = companionLanguages.filter(lang => userLanguages.includes(lang)).length;
    return Math.min(1, overlap / userLanguages.length);
  }

  private calculatePriceCompatibility(rate: number, minPrice: number, maxPrice: number): number {
    if (rate >= minPrice && rate <= maxPrice) {
      // Prefer rates closer to the middle of the range
      const range = maxPrice - minPrice;
      const optimal = minPrice + (range * 0.4); // Slightly prefer lower end
      const distance = Math.abs(rate - optimal);
      return Math.max(0, 1 - (distance / (range / 2)));
    }
    return 0;
  }

  private findSimilarCompanions(companion: any, bookingHistory: any[]): any[] {
    // Find companions with similar attributes that user has booked
    return bookingHistory.filter(booking => {
      // This would need more sophisticated similarity matching
      // For now, simple implementation
      return booking.companion_id !== companion.id;
    });
  }

  private calculateUserBookingFrequency(bookingHistory: any[]): number {
    if (bookingHistory.length === 0) return 0;

    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const recentBookings = bookingHistory.filter(booking =>
      new Date(booking.created_at) >= thirtyDaysAgo
    );

    return recentBookings.length / 30; // Bookings per day
  }

  private calculateCompanionPopularity(companion: any): number {
    const bookings = companion.bookings || [];
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const recentBookings = bookings.filter((booking: any) =>
      new Date(booking.created_at) >= thirtyDaysAgo
    );

    return recentBookings.length / 30; // Bookings per day
  }

  private calculateFrequencyCompatibility(userFreq: number, companionPopularity: number): number {
    // Match users with companions of similar booking frequency
    if (userFreq === 0 && companionPopularity === 0) return 1;
    if (userFreq === 0 || companionPopularity === 0) return 0.5;

    const ratio = Math.min(userFreq, companionPopularity) / Math.max(userFreq, companionPopularity);
    return ratio;
  }

  // Onboarding
  async getOnboardingProgress(userId: string): Promise<Record<string, boolean>> {
    try {
      const { data, error } = await this.supabase
        .from('onboarding_progress')
        .select('step_name, completed')
        .eq('user_id', userId);

      if (error) throw error;

      const progress: Record<string, boolean> = {};
      data?.forEach((item: any) => {
        progress[item.step_name] = item.completed;
      });

      return progress;
    } catch (error) {
      console.error('Error getting onboarding progress:', error);
      return {};
    }
  }

  async updateOnboardingStep(userId: string, stepName: string, completed = true, data?: any): Promise<void> {
    try {
      await this.supabase
        .from('onboarding_progress')
        .upsert({
          user_id: userId,
          step_name: stepName,
          completed,
          completed_at: completed ? new Date().toISOString() : null,
          data: data || {},
        });
    } catch (error) {
      console.error('Error updating onboarding step:', error);
    }
  }

  // Referral System
  async createReferral(referrerId: string, email?: string, phoneNumber?: string): Promise<string> {
    try {
      const { data, error } = await this.supabase
        .from('user_referrals')
        .insert({
          referrer_id: referrerId,
          email,
          phone_number: phoneNumber,
          referral_code: await this.generateReferralCode(),
          reward_amount: 25, // $25 credit
          reward_type: 'credit',
        })
        .select('referral_code')
        .single();

      if (error) throw error;
      return data.referral_code;
    } catch (error) {
      console.error('Error creating referral:', error);
      throw error;
    }
  }

  async processReferral(referralCode: string, newUserId: string): Promise<boolean> {
    try {
      const { data: referral, error: fetchError } = await this.supabase
        .from('user_referrals')
        .select('*')
        .eq('referral_code', referralCode)
        .eq('status', 'pending')
        .gt('expires_at', new Date().toISOString())
        .single();

      if (fetchError || !referral) return false;

      // Update referral status
      const { error: updateError } = await this.supabase
        .from('user_referrals')
        .update({
          referred_id: newUserId,
          status: 'signed_up',
        })
        .eq('id', referral.id);

      if (updateError) throw updateError;

      return true;
    } catch (error) {
      console.error('Error processing referral:', error);
      return false;
    }
  }

  async getUserReferrals(userId: string): Promise<any[]> {
    try {
      const { data, error } = await this.supabase
        .from('user_referrals')
        .select(`
          *,
          referred:profiles!referred_id(full_name, email)
        `)
        .eq('referrer_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting user referrals:', error);
      return [];
    }
  }

  // Utility functions
  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) *
        Math.cos(this.toRadians(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  private async generateReferralCode(): Promise<string> {
    const { data } = await this.supabase.rpc('generate_referral_code');
    return data;
  }
}