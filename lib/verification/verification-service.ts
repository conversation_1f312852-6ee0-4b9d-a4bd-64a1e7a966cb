import { Platform } from 'react-native';
import { createClient } from '@supabase/supabase-js';

export interface VerificationRequest {
  id: string;
  companionId: string;
  status: 'pending' | 'in_review' | 'approved' | 'rejected' | 'requires_resubmission';
  verificationLevel: 'basic' | 'enhanced' | 'premium';
  documentsRequired: string[];
  documentsSubmitted: string[];
  verificationScore: number;
  backgroundCheckStatus: string;
}

export interface IdentityDocument {
  id: string;
  documentType: string;
  fileUrl: string;
  fileName: string;
  verificationStatus: 'pending' | 'verified' | 'rejected';
  verificationNotes?: string;
  aiAnalysisResult?: any;
}

export interface BackgroundCheck {
  id: string;
  checkType: string;
  provider: string;
  status: string;
  result?: 'pass' | 'fail' | 'review_required' | 'consider';
  details?: any;
}

export interface SafetyReport {
  id: string;
  reportType: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  evidenceUrls: string[];
  status: string;
}

export class VerificationService {
  private static instance: VerificationService;
  private supabase: any;

  static getInstance(): VerificationService {
    if (!VerificationService.instance) {
      VerificationService.instance = new VerificationService();
    }
    return VerificationService.instance;
  }

  initialize(supabaseClient: any): void {
    this.supabase = supabaseClient;
  }

  // Verification Requests
  async createVerificationRequest(
    companionId: string,
    verificationLevel: 'basic' | 'enhanced' | 'premium' = 'basic'
  ): Promise<VerificationRequest> {
    try {
      const documentsRequired = this.getRequiredDocuments(verificationLevel);

      const { data, error } = await this.supabase
        .from('verification_requests')
        .insert({
          companion_id: companionId,
          verification_level: verificationLevel,
          documents_required: documentsRequired,
        })
        .select()
        .single();

      if (error) throw error;

      return {
        id: data.id,
        companionId: data.companion_id,
        status: data.status,
        verificationLevel: data.verification_level,
        documentsRequired: data.documents_required,
        documentsSubmitted: data.documents_submitted,
        verificationScore: data.verification_score,
        backgroundCheckStatus: data.background_check_status,
      };
    } catch (error) {
      console.error('Error creating verification request:', error);
      throw error;
    }
  }

  async uploadDocument(
    verificationRequestId: string,
    documentType: string,
    fileUri: string,
    fileName: string
  ): Promise<IdentityDocument> {
    try {
      // Upload file to Supabase Storage
      const fileExt = fileName.split('.').pop();
      const filePath = `verification-documents/${verificationRequestId}-${documentType}-${Date.now()}.${fileExt}`;
      
      const response = await fetch(fileUri);
      const blob = await response.blob();
      
      const { error: uploadError } = await this.supabase.storage
        .from('verification-documents')
        .upload(filePath, blob);
        
      if (uploadError) throw uploadError;
      
      // Get public URL
      const { data: publicUrl } = this.supabase.storage
        .from('verification-documents')
        .getPublicUrl(filePath);
        
      // Save document record
      const { data, error } = await this.supabase
        .from('identity_documents')
        .insert({
          verification_request_id: verificationRequestId,
          document_type: documentType,
          file_url: publicUrl.publicUrl,
          file_name: fileName,
          mime_type: blob.type,
          file_size: blob.size,
        })
        .select()
        .single();
        
      if (error) throw error;

      // Update verification request
      await this.updateDocumentsSubmitted(verificationRequestId, documentType);

      return {
        id: data.id,
        documentType: data.document_type,
        fileUrl: data.file_url,
        fileName: data.file_name,
        verificationStatus: data.verification_status,
        verificationNotes: data.verification_notes,
        aiAnalysisResult: data.ai_analysis_result,
      };
    } catch (error) {
      console.error('Error uploading document:', error);
      throw error;
    }
  }

  private async updateDocumentsSubmitted(verificationRequestId: string, documentType: string): Promise<void> {
    const { error } = await this.supabase.rpc('add_submitted_document', {
      request_id: verificationRequestId,
      doc_type: documentType,
    });

    if (error) {
      console.error('Error updating submitted documents:', error);
    }
  }

  async initiateBackgroundCheck(
    verificationRequestId: string,
    checkTypes: string[] = ['criminal_history', 'identity_verification']
  ): Promise<BackgroundCheck[]> {
    try {
      const checks: BackgroundCheck[] = [];

      for (const checkType of checkTypes) {
        const { data, error } = await this.supabase
          .from('background_checks')
          .insert({
            verification_request_id: verificationRequestId,
            check_type: checkType,
            provider: 'checkr', // Default provider
            status: 'pending',
          })
          .select()
          .single();

        if (error) throw error;

        checks.push({
          id: data.id,
          checkType: data.check_type,
          provider: data.provider,
          status: data.status,
          result: data.result,
          details: data.details,
        });

        // In production, you would integrate with actual background check providers
        // For now, we'll simulate the process
        await this.simulateBackgroundCheck(data.id);
      }

      return checks;
    } catch (error) {
      console.error('Error initiating background check:', error);
      throw error;
    }
  }

  private async simulateBackgroundCheck(checkId: string): Promise<void> {
    // Simulate background check processing
    setTimeout(async () => {
      try {
        await this.supabase
          .from('background_checks')
          .update({
            status: 'completed',
            result: 'pass', // Simulate passing result
            completed_at: new Date().toISOString(),
            details: {
              criminal_history: 'clear',
              identity_match: 'verified',
              address_verified: true,
            },
          })
          .eq('id', checkId);
      } catch (error) {
        console.error('Error updating background check:', error);
      }
    }, 5000); // 5 second delay to simulate processing
  }

  // Safety Reporting
  async createSafetyReport(
    reporterId: string,
    reportedUserId: string,
    reportType: string,
    severity: 'low' | 'medium' | 'high' | 'critical',
    description: string,
    evidenceUrls: string[] = [],
    bookingId?: string
  ): Promise<SafetyReport> {
    try {
      const { data, error } = await this.supabase
        .from('safety_reports')
        .insert({
          reporter_id: reporterId,
          reported_user_id: reportedUserId,
          booking_id: bookingId,
          report_type: reportType,
          severity,
          description,
          evidence_urls: evidenceUrls,
        })
        .select()
        .single();

      if (error) throw error;

      // Auto-escalate critical reports
      if (severity === 'critical') {
        await this.escalateSafetyReport(data.id);
      }

      return {
        id: data.id,
        reportType: data.report_type,
        severity: data.severity,
        description: data.description,
        evidenceUrls: data.evidence_urls,
        status: data.status,
      };
    } catch (error) {
      console.error('Error creating safety report:', error);
      throw error;
    }
  }

  private async escalateSafetyReport(reportId: string): Promise<void> {
    try {
      await this.supabase
        .from('safety_reports')
        .update({
          status: 'escalated',
          updated_at: new Date().toISOString(),
        })
        .eq('id', reportId);

      // In production, this would trigger immediate admin notification
      console.log('Critical safety report escalated:', reportId);
    } catch (error) {
      console.error('Error escalating safety report:', error);
    }
  }

  // Verification Status
  async getVerificationStatus(companionId: string): Promise<any> {
    try {
      const { data, error } = await this.supabase
        .from('verification_requests')
        .select(`
          *,
          identity_documents(*),
          background_checks(*)
        `)
        .eq('companion_id', companionId)
        .order('created_at', { ascending: false })
        .limit(1)
        .maybeSingle();

      if (error && error.code !== 'PGRST116') throw error;
      return data;
    } catch (error) {
      console.error('Error getting verification status:', error);
      return null;
    }
  }

  async calculateSafetyScore(companionId: string): Promise<number> {
    try {
      const { data, error } = await this.supabase.rpc('calculate_companion_safety_score', {
        companion_id_param: companionId,
      });

      if (error) throw error;
      return data || 0;
    } catch (error) {
      console.error('Error calculating safety score:', error);
      return 0;
    }
  }

  // Safety Education
  async getSafetyEducationContent(category?: string): Promise<any[]> {
    try {
      let query = this.supabase
        .from('safety_education')
        .select('*')
        .eq('is_published', true)
        .order('created_at', { ascending: false });

      if (category) {
        query = query.eq('category', category);
      }

      const { data, error } = await query;
      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('Error getting safety education content:', error);
      return [];
    }
  }

  async markSafetyContentViewed(contentId: string, userId: string): Promise<void> {
    try {
      // Track content view for analytics
      await this.supabase
        .from('analytics_events')
        .insert({
          user_id: userId,
          event_name: 'safety_content_viewed',
          event_category: 'education',
          event_properties: {
            content_id: contentId,
          },
        });

      // Update view count
      await this.supabase
        .from('safety_education')
        .update({
          view_count: this.supabase.raw('view_count + 1'),
          updated_at: new Date().toISOString(),
        })
        .eq('id', contentId);
    } catch (error) {
      console.error('Error marking safety content viewed:', error);
    }
  }

  private getRequiredDocuments(verificationLevel: string): string[] {
    switch (verificationLevel) {
      case 'basic':
        return ['government_id', 'selfie_with_id'];
      case 'enhanced':
        return ['government_id', 'selfie_with_id', 'proof_of_address'];
      case 'premium':
        return ['government_id', 'selfie_with_id', 'proof_of_address', 'professional_license'];
      default:
        return ['government_id', 'selfie_with_id'];
    }
  }
}