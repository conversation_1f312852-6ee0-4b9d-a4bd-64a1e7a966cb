import { createClient } from '@supabase/supabase-js';

export interface WalletBalance {
  balance: number;
  pendingBalance: number;
  totalEarned: number;
  totalSpent: number;
  currency: string;
}

export interface WalletTransaction {
  id: string;
  type: 'credit' | 'debit' | 'refund' | 'bonus' | 'referral_reward';
  amount: number;
  balanceAfter: number;
  description: string;
  referenceId?: string;
  referenceType?: string;
  createdAt: Date;
}

export interface PromoCode {
  id: string;
  code: string;
  name: string;
  description: string;
  discountType: 'percentage' | 'fixed_amount' | 'free_booking';
  discountValue: number;
  minimumOrderAmount: number;
  maximumDiscountAmount?: number;
  usageLimit?: number;
  usageCount: number;
  userUsageLimit: number;
  isActive: boolean;
  expiresAt?: Date;
}

export interface PromoCodeResult {
  valid: boolean;
  discountAmount?: number;
  finalAmount?: number;
  promoId?: string;
  error?: string;
}

export class WalletService {
  private static instance: WalletService;
  private supabase: any;

  static getInstance(): WalletService {
    if (!WalletService.instance) {
      WalletService.instance = new WalletService();
    }
    return WalletService.instance;
  }

  initialize(supabaseClient: any): void {
    this.supabase = supabaseClient;
  }

  // Wallet Management
  async getWalletBalance(userId: string): Promise<WalletBalance | null> {
    try {
      const { data, error } = await this.supabase
        .from('user_wallets')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') throw error;

      if (!data) {
        // Create wallet if it doesn't exist
        return await this.createWallet(userId);
      }

      return {
        balance: parseFloat(data.balance),
        pendingBalance: parseFloat(data.pending_balance),
        totalEarned: parseFloat(data.total_earned),
        totalSpent: parseFloat(data.total_spent),
        currency: data.currency,
      };
    } catch (error) {
      console.error('Error getting wallet balance:', error);
      return null;
    }
  }

  private async createWallet(userId: string): Promise<WalletBalance> {
    try {
      const { data, error } = await this.supabase
        .from('user_wallets')
        .insert({
          user_id: userId,
          balance: 0,
          pending_balance: 0,
          total_earned: 0,
          total_spent: 0,
          currency: 'USD',
        })
        .select()
        .single();

      if (error) throw error;

      return {
        balance: 0,
        pendingBalance: 0,
        totalEarned: 0,
        totalSpent: 0,
        currency: 'USD',
      };
    } catch (error) {
      console.error('Error creating wallet:', error);
      throw error;
    }
  }

  async addWalletCredit(
    userId: string,
    amount: number,
    description: string,
    referenceId?: string,
    referenceType?: string
  ): Promise<WalletTransaction> {
    try {
      const wallet = await this.getWalletBalance(userId);
      if (!wallet) throw new Error('Wallet not found');

      const { data, error } = await this.supabase
        .from('wallet_transactions')
        .insert({
          wallet_id: (await this.getWalletId(userId)),
          transaction_type: 'credit',
          amount,
          description,
          reference_id: referenceId,
          reference_type: referenceType,
        })
        .select()
        .single();

      if (error) throw error;

      return {
        id: data.id,
        type: data.transaction_type,
        amount: parseFloat(data.amount),
        balanceAfter: parseFloat(data.balance_after),
        description: data.description,
        referenceId: data.reference_id,
        referenceType: data.reference_type,
        createdAt: new Date(data.created_at),
      };
    } catch (error) {
      console.error('Error adding wallet credit:', error);
      throw error;
    }
  }

  async deductWalletBalance(
    userId: string,
    amount: number,
    description: string,
    referenceId?: string,
    referenceType?: string
  ): Promise<WalletTransaction> {
    try {
      const wallet = await this.getWalletBalance(userId);
      if (!wallet) throw new Error('Wallet not found');
      if (wallet.balance < amount) throw new Error('Insufficient balance');

      const { data, error } = await this.supabase
        .from('wallet_transactions')
        .insert({
          wallet_id: (await this.getWalletId(userId)),
          transaction_type: 'debit',
          amount,
          description,
          reference_id: referenceId,
          reference_type: referenceType,
        })
        .select()
        .single();

      if (error) throw error;

      return {
        id: data.id,
        type: data.transaction_type,
        amount: parseFloat(data.amount),
        balanceAfter: parseFloat(data.balance_after),
        description: data.description,
        referenceId: data.reference_id,
        referenceType: data.reference_type,
        createdAt: new Date(data.created_at),
      };
    } catch (error) {
      console.error('Error deducting wallet balance:', error);
      throw error;
    }
  }

  async getWalletTransactions(userId: string, limit = 50): Promise<WalletTransaction[]> {
    try {
      const walletId = await this.getWalletId(userId);
      
      const { data, error } = await this.supabase
        .from('wallet_transactions')
        .select('*')
        .eq('wallet_id', walletId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;

      return data?.map((transaction: any) => ({
        id: transaction.id,
        type: transaction.transaction_type,
        amount: parseFloat(transaction.amount),
        balanceAfter: parseFloat(transaction.balance_after),
        description: transaction.description,
        referenceId: transaction.reference_id,
        referenceType: transaction.reference_type,
        createdAt: new Date(transaction.created_at),
      })) || [];
    } catch (error) {
      console.error('Error getting wallet transactions:', error);
      return [];
    }
  }

  private async getWalletId(userId: string): Promise<string> {
    const { data, error } = await this.supabase
      .from('user_wallets')
      .select('id')
      .eq('user_id', userId)
      .single();

    if (error) throw error;
    return data.id;
  }

  // Promotional Codes
  async validatePromoCode(
    code: string,
    userId: string,
    bookingAmount: number
  ): Promise<PromoCodeResult> {
    try {
      const { data, error } = await this.supabase.rpc('apply_promotional_code', {
        code_param: code,
        user_id_param: userId,
        booking_amount: bookingAmount,
      });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error validating promo code:', error);
      return { valid: false, error: 'Failed to validate promotional code' };
    }
  }

  async applyPromoCode(
    promoId: string,
    userId: string,
    bookingId: string,
    discountAmount: number
  ): Promise<void> {
    try {
      // Record promo code usage
      await this.supabase
        .from('promo_code_usage')
        .insert({
          promo_code_id: promoId,
          user_id: userId,
          booking_id: bookingId,
          discount_amount: discountAmount,
        });

      // Update promo code usage count
      await this.supabase
        .from('promotional_codes')
        .update({
          usage_count: this.supabase.raw('usage_count + 1'),
          updated_at: new Date().toISOString(),
        })
        .eq('id', promoId);
    } catch (error) {
      console.error('Error applying promo code:', error);
      throw error;
    }
  }

  async getAvailablePromoCodes(userId: string): Promise<PromoCode[]> {
    try {
      const { data, error } = await this.supabase
        .from('promotional_codes')
        .select('*')
        .eq('is_active', true)
        .or('expires_at.is.null,expires_at.gt.now()')
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Filter out codes user has already used (if user usage limit = 1)
      const { data: usedCodes } = await this.supabase
        .from('promo_code_usage')
        .select('promo_code_id')
        .eq('user_id', userId);

      const usedCodeIds = usedCodes?.map((usage: any) => usage.promo_code_id) || [];

      return data?.filter((code: any) => 
        !usedCodeIds.includes(code.id) || code.user_usage_limit > 1
      ).map((code: any) => ({
        id: code.id,
        code: code.code,
        name: code.name,
        description: code.description,
        discountType: code.discount_type,
        discountValue: parseFloat(code.discount_value),
        minimumOrderAmount: parseFloat(code.minimum_order_amount),
        maximumDiscountAmount: code.maximum_discount_amount ? parseFloat(code.maximum_discount_amount) : undefined,
        usageLimit: code.usage_limit,
        usageCount: code.usage_count,
        userUsageLimit: code.user_usage_limit,
        isActive: code.is_active,
        expiresAt: code.expires_at ? new Date(code.expires_at) : undefined,
      })) || [];
    } catch (error) {
      console.error('Error getting available promo codes:', error);
      return [];
    }
  }

  // Expense Tracking
  async getExpenseAnalytics(userId: string, months = 12): Promise<any[]> {
    try {
      const { data, error } = await this.supabase
        .from('expense_tracking')
        .select('*')
        .eq('user_id', userId)
        .order('month_year', { ascending: false })
        .limit(months);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting expense analytics:', error);
      return [];
    }
  }

  async setBudgetLimit(userId: string, monthYear: string, budgetLimit: number): Promise<void> {
    try {
      await this.supabase
        .from('expense_tracking')
        .upsert({
          user_id: userId,
          month_year: monthYear,
          budget_limit: budgetLimit,
          budget_alerts_enabled: true,
          updated_at: new Date().toISOString(),
        });
    } catch (error) {
      console.error('Error setting budget limit:', error);
      throw error;
    }
  }
}