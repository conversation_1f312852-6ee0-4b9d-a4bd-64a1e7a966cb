import { Platform } from 'react-native';

// RevenueCat configuration
export const REVENUECAT_CONFIG = {
  apiKey: {
    ios: process.env.EXPO_PUBLIC_REVENUECAT_IOS_API_KEY || '',
    android: process.env.EXPO_PUBLIC_REVENUECAT_ANDROID_API_KEY || '',
  },
  entitlements: {
    premium: 'premium_access',
    vip: 'vip_access',
  },
  products: {
    basic_monthly: 'basic_monthly',
    premium_monthly: 'premium_monthly',
    vip_monthly: 'vip_monthly',
    basic_yearly: 'basic_yearly',
    premium_yearly: 'premium_yearly',
    vip_yearly: 'vip_yearly',
  },
};

// RevenueCat service class
export class RevenueCatService {
  private static instance: RevenueCatService;
  private isConfigured = false;

  static getInstance(): RevenueCatService {
    if (!RevenueCatService.instance) {
      RevenueCatService.instance = new RevenueCatService();
    }
    return RevenueCatService.instance;
  }

  async configure(userId: string): Promise<void> {
    if (Platform.OS === 'web') {
      console.warn('RevenueCat is not available on web platform');
      return;
    }

    try {
      // Note: This requires the actual RevenueCat SDK to be installed
      // For now, we'll simulate the configuration
      console.log('Configuring RevenueCat for user:', userId);
      this.isConfigured = true;
    } catch (error) {
      console.error('Failed to configure RevenueCat:', error);
      throw error;
    }
  }

  async getOfferings(): Promise<any> {
    if (Platform.OS === 'web') {
      // Return mock offerings for web
      return this.getMockOfferings();
    }

    if (!this.isConfigured) {
      throw new Error('RevenueCat not configured');
    }

    try {
      // This would use the actual RevenueCat SDK
      console.log('Fetching RevenueCat offerings');
      return this.getMockOfferings();
    } catch (error) {
      console.error('Failed to get offerings:', error);
      throw error;
    }
  }

  async purchaseProduct(productId: string): Promise<any> {
    if (Platform.OS === 'web') {
      throw new Error('In-app purchases not available on web');
    }

    if (!this.isConfigured) {
      throw new Error('RevenueCat not configured');
    }

    try {
      // This would use the actual RevenueCat SDK
      console.log('Purchasing product:', productId);
      return { success: true, productId };
    } catch (error) {
      console.error('Failed to purchase product:', error);
      throw error;
    }
  }

  async restorePurchases(): Promise<any> {
    if (Platform.OS === 'web') {
      return { success: true, entitlements: {} };
    }

    if (!this.isConfigured) {
      throw new Error('RevenueCat not configured');
    }

    try {
      // This would use the actual RevenueCat SDK
      console.log('Restoring purchases');
      return { success: true, entitlements: {} };
    } catch (error) {
      console.error('Failed to restore purchases:', error);
      throw error;
    }
  }

  async getCustomerInfo(): Promise<any> {
    if (Platform.OS === 'web') {
      return { entitlements: {}, activeSubscriptions: [] };
    }

    if (!this.isConfigured) {
      throw new Error('RevenueCat not configured');
    }

    try {
      // This would use the actual RevenueCat SDK
      console.log('Getting customer info');
      return { entitlements: {}, activeSubscriptions: [] };
    } catch (error) {
      console.error('Failed to get customer info:', error);
      throw error;
    }
  }

  private getMockOfferings() {
    return {
      current: {
        monthly: {
          basic: {
            identifier: 'basic_monthly',
            price: '$9.99',
            priceString: '$9.99/month',
          },
          premium: {
            identifier: 'premium_monthly',
            price: '$19.99',
            priceString: '$19.99/month',
          },
          vip: {
            identifier: 'vip_monthly',
            price: '$39.99',
            priceString: '$39.99/month',
          },
        },
        yearly: {
          basic: {
            identifier: 'basic_yearly',
            price: '$99.99',
            priceString: '$99.99/year',
          },
          premium: {
            identifier: 'premium_yearly',
            price: '$199.99',
            priceString: '$199.99/year',
          },
          vip: {
            identifier: 'vip_yearly',
            price: '$399.99',
            priceString: '$399.99/year',
          },
        },
      },
    };
  }
}