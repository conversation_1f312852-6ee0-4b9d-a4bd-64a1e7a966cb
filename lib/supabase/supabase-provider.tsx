import { createContext, useContext, useEffect, useState } from 'react';
import { useRouter, useSegments } from 'expo-router';
import 'react-native-url-polyfill/auto';
import { EzyBackendClient, AuthUser } from '@/lib/ezybackend/ezybackend-client';
import { SupabaseAdapter } from '@/lib/ezybackend/supabase-adapter';

// EzyBackend configuration
const ezyBackendConfig = {
  serverUrl: process.env.EXPO_PUBLIC_EZYBACKEND_URL || 'http://localhost:4730',
  appId: process.env.EXPO_PUBLIC_EZYBACKEND_APP_ID || 'hourlyGF',
  appKey: process.env.EXPO_PUBLIC_EZYBACKEND_APP_KEY || 'your-app-key'
};

type EzyBackendContextType = {
  ezyBackend: EzyBackendClient;
  supabase: SupabaseAdapter; // Compatibility layer
  user: AuthUser | null;
  loading: boolean;
};

const EzyBackendContext = createContext<EzyBackendContextType | undefined>(undefined);

export function EzyBackendProvider({ children }: { children: React.ReactNode }) {
  const [ezyBackend] = useState(() => new EzyBackendClient(ezyBackendConfig));
  const [supabase] = useState(() => new SupabaseAdapter(ezyBackend));
  const [user, setUser] = useState<AuthUser | null>(null);
  const [loading, setLoading] = useState(true);
  const segments = useSegments();
  const router = useRouter();

  useEffect(() => {
    // Check auth state
    const checkAuthState = async () => {
      try {
        const currentUser = await ezyBackend.getCurrentUser();
        setUser(currentUser);
      } catch (error) {
        console.error('Auth check error:', error);
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    checkAuthState();
  }, []);

  // Handle routing based on auth state
  useEffect(() => {
    if (loading) return; // Don't redirect while loading

    const inAuthGroup = segments[0] === '(auth)';

    if (!user && !inAuthGroup) {
      // Redirect to sign-in if not authenticated
      router.replace('/(auth)/sign-in');
    } else if (user && inAuthGroup) {
      // Redirect to home if authenticated
      router.replace('/(tabs)');
    }
  }, [user, segments, loading]);

  return (
    <EzyBackendContext.Provider value={{ ezyBackend, supabase, user, loading }}>
      {children}
    </EzyBackendContext.Provider>
  );
}

export function useEzyBackend() {
  const context = useContext(EzyBackendContext);
  if (context === undefined) {
    throw new Error('useEzyBackend must be used within an EzyBackendProvider');
  }
  return context;
}

// Backward compatibility - alias for existing code
export const useSupabase = useEzyBackend;
export const SupabaseProvider = EzyBackendProvider;