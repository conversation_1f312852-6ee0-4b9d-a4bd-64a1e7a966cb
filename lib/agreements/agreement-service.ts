import { createClient } from '@supabase/supabase-js';

export interface Agreement {
  id: string;
  type: 'companionship' | 'privacy' | 'safety' | 'payment' | 'cancellation' | 'community_guidelines';
  title: string;
  content: string;
  version: string;
  isRequired: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserAgreement {
  id: string;
  userId: string;
  agreementId: string;
  bookingId?: string;
  companionId?: string;
  agreedAt: Date;
  ipAddress: string;
  userAgent: string;
  isActive: boolean;
}

export interface CommunityGuideline {
  id: string;
  category: 'behavior' | 'communication' | 'safety' | 'respect' | 'privacy' | 'payment';
  title: string;
  description: string;
  examples: string[];
  consequences: string[];
  severity: 'minor' | 'moderate' | 'serious' | 'severe';
  isActive: boolean;
}

export interface GuidelineViolation {
  id: string;
  reporterId: string;
  violatorId: string;
  guidelineId: string;
  bookingId?: string;
  description: string;
  evidence: string[];
  status: 'reported' | 'under_review' | 'resolved' | 'dismissed';
  severity: 'minor' | 'moderate' | 'serious' | 'severe';
  actionTaken?: string;
  reviewedBy?: string;
  reviewedAt?: Date;
  createdAt: Date;
}

export interface ConsentRecord {
  id: string;
  userId: string;
  companionId: string;
  bookingId: string;
  consentType: 'meeting' | 'activities' | 'location_sharing' | 'photo_sharing' | 'contact_info';
  isGranted: boolean;
  grantedAt: Date;
  revokedAt?: Date;
  ipAddress: string;
}

export class AgreementService {
  private static instance: AgreementService;
  private supabase: any;

  static getInstance(): AgreementService {
    if (!AgreementService.instance) {
      AgreementService.instance = new AgreementService();
    }
    return AgreementService.instance;
  }

  initialize(supabaseClient: any): void {
    this.supabase = supabaseClient;
  }

  // Agreement Management
  async getActiveAgreements(type?: Agreement['type']): Promise<Agreement[]> {
    try {
      let query = this.supabase
        .from('agreements')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (type) {
        query = query.eq('type', type);
      }

      const { data, error } = await query;
      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('Error fetching agreements:', error);
      return [];
    }
  }

  async getRequiredAgreements(userId: string): Promise<Agreement[]> {
    try {
      // Get all required agreements
      const { data: agreements, error: agreementsError } = await this.supabase
        .from('agreements')
        .select('*')
        .eq('is_active', true)
        .eq('is_required', true);

      if (agreementsError) throw agreementsError;

      // Get user's signed agreements
      const { data: userAgreements, error: userAgreementsError } = await this.supabase
        .from('user_agreements')
        .select('agreement_id')
        .eq('user_id', userId)
        .eq('is_active', true);

      if (userAgreementsError) throw userAgreementsError;

      const signedAgreementIds = userAgreements?.map(ua => ua.agreement_id) || [];

      // Return agreements that haven't been signed
      return agreements?.filter(agreement => !signedAgreementIds.includes(agreement.id)) || [];
    } catch (error) {
      console.error('Error fetching required agreements:', error);
      return [];
    }
  }

  async signAgreement(
    userId: string,
    agreementId: string,
    ipAddress: string,
    userAgent: string,
    bookingId?: string,
    companionId?: string
  ): Promise<UserAgreement> {
    try {
      const { data, error } = await this.supabase
        .from('user_agreements')
        .insert({
          user_id: userId,
          agreement_id: agreementId,
          booking_id: bookingId,
          companion_id: companionId,
          agreed_at: new Date().toISOString(),
          ip_address: ipAddress,
          user_agent: userAgent,
          is_active: true
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error signing agreement:', error);
      throw error;
    }
  }

  async getUserAgreements(userId: string): Promise<UserAgreement[]> {
    try {
      const { data, error } = await this.supabase
        .from('user_agreements')
        .select(`
          *,
          agreements(*)
        `)
        .eq('user_id', userId)
        .eq('is_active', true)
        .order('agreed_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching user agreements:', error);
      return [];
    }
  }

  // Community Guidelines
  async getCommunityGuidelines(): Promise<CommunityGuideline[]> {
    try {
      const { data, error } = await this.supabase
        .from('community_guidelines')
        .select('*')
        .eq('is_active', true)
        .order('category')
        .order('severity');

      if (error) throw error;
      return data || this.getDefaultGuidelines();
    } catch (error) {
      console.error('Error fetching community guidelines:', error);
      return this.getDefaultGuidelines();
    }
  }

  private getDefaultGuidelines(): CommunityGuideline[] {
    return [
      {
        id: '1',
        category: 'respect',
        title: 'Treat Everyone with Respect',
        description: 'All interactions must be respectful and courteous',
        examples: ['Use polite language', 'Respect boundaries', 'Be punctual'],
        consequences: ['Warning', 'Temporary suspension', 'Account termination'],
        severity: 'serious',
        isActive: true
      },
      {
        id: '2',
        category: 'safety',
        title: 'Prioritize Safety',
        description: 'Safety is our top priority for all users',
        examples: ['Meet in public places', 'Share location with trusted contacts', 'Report unsafe behavior'],
        consequences: ['Immediate investigation', 'Account suspension', 'Law enforcement contact'],
        severity: 'severe',
        isActive: true
      },
      {
        id: '3',
        category: 'communication',
        title: 'Appropriate Communication',
        description: 'Keep all communication appropriate and professional',
        examples: ['No inappropriate content', 'Respectful messaging', 'Clear communication'],
        consequences: ['Content removal', 'Communication restrictions', 'Account suspension'],
        severity: 'moderate',
        isActive: true
      }
    ];
  }

  async reportGuidelineViolation(
    reporterId: string,
    violatorId: string,
    guidelineId: string,
    description: string,
    evidence: string[] = [],
    bookingId?: string
  ): Promise<GuidelineViolation> {
    try {
      // Get guideline to determine severity
      const { data: guideline } = await this.supabase
        .from('community_guidelines')
        .select('severity')
        .eq('id', guidelineId)
        .single();

      const { data, error } = await this.supabase
        .from('guideline_violations')
        .insert({
          reporter_id: reporterId,
          violator_id: violatorId,
          guideline_id: guidelineId,
          booking_id: bookingId,
          description,
          evidence,
          status: 'reported',
          severity: guideline?.severity || 'moderate',
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      // Auto-escalate severe violations
      if (guideline?.severity === 'severe') {
        await this.escalateViolation(data.id);
      }

      return data;
    } catch (error) {
      console.error('Error reporting guideline violation:', error);
      throw error;
    }
  }

  private async escalateViolation(violationId: string): Promise<void> {
    try {
      await this.supabase
        .from('guideline_violations')
        .update({
          status: 'under_review',
          // In a real app, this would notify moderators
        })
        .eq('id', violationId);
    } catch (error) {
      console.error('Error escalating violation:', error);
    }
  }

  // Consent Management
  async recordConsent(
    userId: string,
    companionId: string,
    bookingId: string,
    consentType: ConsentRecord['consentType'],
    isGranted: boolean,
    ipAddress: string
  ): Promise<ConsentRecord> {
    try {
      const { data, error } = await this.supabase
        .from('consent_records')
        .insert({
          user_id: userId,
          companion_id: companionId,
          booking_id: bookingId,
          consent_type: consentType,
          is_granted: isGranted,
          granted_at: new Date().toISOString(),
          ip_address: ipAddress
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error recording consent:', error);
      throw error;
    }
  }

  async revokeConsent(consentId: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('consent_records')
        .update({
          revoked_at: new Date().toISOString()
        })
        .eq('id', consentId);

      if (error) throw error;
    } catch (error) {
      console.error('Error revoking consent:', error);
      throw error;
    }
  }

  async getConsentRecords(userId: string, bookingId?: string): Promise<ConsentRecord[]> {
    try {
      let query = this.supabase
        .from('consent_records')
        .select('*')
        .eq('user_id', userId)
        .is('revoked_at', null);

      if (bookingId) {
        query = query.eq('booking_id', bookingId);
      }

      const { data, error } = await query.order('granted_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching consent records:', error);
      return [];
    }
  }

  // Booking-specific agreements
  async createBookingAgreement(
    userId: string,
    companionId: string,
    bookingId: string,
    terms: string[],
    ipAddress: string,
    userAgent: string
  ): Promise<void> {
    try {
      // Create a custom agreement for this booking
      const { data: agreement, error: agreementError } = await this.supabase
        .from('agreements')
        .insert({
          type: 'companionship',
          title: `Booking Agreement - ${bookingId}`,
          content: terms.join('\n'),
          version: '1.0',
          is_required: true,
          is_active: true,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (agreementError) throw agreementError;

      // Sign the agreement
      await this.signAgreement(userId, agreement.id, ipAddress, userAgent, bookingId, companionId);
    } catch (error) {
      console.error('Error creating booking agreement:', error);
      throw error;
    }
  }

  async hasValidAgreements(userId: string, bookingId?: string): Promise<boolean> {
    try {
      const requiredAgreements = await this.getRequiredAgreements(userId);
      
      if (bookingId) {
        // Check for booking-specific agreements
        const { data: bookingAgreements } = await this.supabase
          .from('user_agreements')
          .select('*')
          .eq('user_id', userId)
          .eq('booking_id', bookingId)
          .eq('is_active', true);

        return requiredAgreements.length === 0 && (bookingAgreements?.length || 0) > 0;
      }

      return requiredAgreements.length === 0;
    } catch (error) {
      console.error('Error checking agreement validity:', error);
      return false;
    }
  }
}
