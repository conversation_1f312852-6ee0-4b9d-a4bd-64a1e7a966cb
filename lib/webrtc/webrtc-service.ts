import { Platform } from 'react-native';

// WebRTC configuration for production
export const WEBRTC_CONFIG = {
  iceServers: [
    { urls: 'stun:stun.l.google.com:19302' },
    { urls: 'stun:stun1.l.google.com:19302' },
    { urls: 'stun:stun2.l.google.com:19302' },
    // Add TURN servers for production
    // {
    //   urls: 'turn:your-turn-server.com:3478',
    //   username: 'your-username',
    //   credential: 'your-password'
    // }
  ],
  iceCandidatePoolSize: 10,
};

export interface CallParticipant {
  id: string;
  userId: string;
  role: 'host' | 'participant' | 'moderator';
  isVideoEnabled: boolean;
  isAudioEnabled: boolean;
  connectionQuality: 'poor' | 'fair' | 'good' | 'excellent';
}

export interface CallSession {
  id: string;
  roomId: string;
  type: 'support' | 'booking' | 'consultation';
  status: 'scheduled' | 'active' | 'ended';
  participants: CallParticipant[];
  recordingEnabled: boolean;
  screenSharingEnabled: boolean;
}

export class WebRTCService {
  private static instance: WebRTCService;
  private peerConnection: RTCPeerConnection | null = null;
  private localStream: MediaStream | null = null;
  private remoteStream: MediaStream | null = null;
  private isInitialized = false;
  private callSession: CallSession | null = null;

  static getInstance(): WebRTCService {
    if (!WebRTCService.instance) {
      WebRTCService.instance = new WebRTCService();
    }
    return WebRTCService.instance;
  }

  async initialize(): Promise<void> {
    if (Platform.OS === 'web' && typeof window !== 'undefined') {
      try {
        // Check for WebRTC support
        if (!window.RTCPeerConnection) {
          throw new Error('WebRTC is not supported in this browser');
        }

        this.isInitialized = true;
        console.log('WebRTC service initialized');
      } catch (error) {
        console.error('Failed to initialize WebRTC:', error);
        throw error;
      }
    } else {
      // For mobile platforms, we would use react-native-webrtc
      console.warn('WebRTC not available on this platform');
    }
  }

  async getUserMedia(constraints: MediaStreamConstraints = { video: true, audio: true }): Promise<MediaStream> {
    if (Platform.OS !== 'web') {
      throw new Error('getUserMedia not available on this platform');
    }

    try {
      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      this.localStream = stream;
      return stream;
    } catch (error) {
      console.error('Error accessing media devices:', error);
      throw error;
    }
  }

  async createPeerConnection(): Promise<RTCPeerConnection> {
    if (Platform.OS !== 'web') {
      throw new Error('RTCPeerConnection not available on this platform');
    }

    try {
      this.peerConnection = new RTCPeerConnection(WEBRTC_CONFIG);

      // Handle ICE candidates
      this.peerConnection.onicecandidate = (event) => {
        if (event.candidate) {
          this.handleIceCandidate(event.candidate);
        }
      };

      // Handle remote stream
      this.peerConnection.ontrack = (event) => {
        this.remoteStream = event.streams[0];
        this.handleRemoteStream(event.streams[0]);
      };

      // Handle connection state changes
      this.peerConnection.onconnectionstatechange = () => {
        this.handleConnectionStateChange(this.peerConnection!.connectionState);
      };

      // Add local stream tracks
      if (this.localStream) {
        this.localStream.getTracks().forEach(track => {
          this.peerConnection!.addTrack(track, this.localStream!);
        });
      }

      return this.peerConnection;
    } catch (error) {
      console.error('Error creating peer connection:', error);
      throw error;
    }
  }

  async createOffer(): Promise<RTCSessionDescriptionInit> {
    if (!this.peerConnection) {
      throw new Error('Peer connection not initialized');
    }

    try {
      const offer = await this.peerConnection.createOffer();
      await this.peerConnection.setLocalDescription(offer);
      return offer;
    } catch (error) {
      console.error('Error creating offer:', error);
      throw error;
    }
  }

  async createAnswer(offer: RTCSessionDescriptionInit): Promise<RTCSessionDescriptionInit> {
    if (!this.peerConnection) {
      throw new Error('Peer connection not initialized');
    }

    try {
      await this.peerConnection.setRemoteDescription(offer);
      const answer = await this.peerConnection.createAnswer();
      await this.peerConnection.setLocalDescription(answer);
      return answer;
    } catch (error) {
      console.error('Error creating answer:', error);
      throw error;
    }
  }

  async handleAnswer(answer: RTCSessionDescriptionInit): Promise<void> {
    if (!this.peerConnection) {
      throw new Error('Peer connection not initialized');
    }

    try {
      await this.peerConnection.setRemoteDescription(answer);
    } catch (error) {
      console.error('Error handling answer:', error);
      throw error;
    }
  }

  async addIceCandidate(candidate: RTCIceCandidateInit): Promise<void> {
    if (!this.peerConnection) {
      throw new Error('Peer connection not initialized');
    }

    try {
      await this.peerConnection.addIceCandidate(candidate);
    } catch (error) {
      console.error('Error adding ICE candidate:', error);
      throw error;
    }
  }

  toggleVideo(): boolean {
    if (!this.localStream) return false;

    const videoTrack = this.localStream.getVideoTracks()[0];
    if (videoTrack) {
      videoTrack.enabled = !videoTrack.enabled;
      return videoTrack.enabled;
    }
    return false;
  }

  toggleAudio(): boolean {
    if (!this.localStream) return false;

    const audioTrack = this.localStream.getAudioTracks()[0];
    if (audioTrack) {
      audioTrack.enabled = !audioTrack.enabled;
      return audioTrack.enabled;
    }
    return false;
  }

  async startScreenShare(): Promise<MediaStream | null> {
    if (Platform.OS !== 'web' || !navigator.mediaDevices.getDisplayMedia) {
      throw new Error('Screen sharing not supported');
    }

    try {
      const screenStream = await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: true
      });

      // Replace video track with screen share
      if (this.peerConnection && this.localStream) {
        const videoTrack = screenStream.getVideoTracks()[0];
        const sender = this.peerConnection.getSenders().find(s => 
          s.track && s.track.kind === 'video'
        );

        if (sender) {
          await sender.replaceTrack(videoTrack);
        }
      }

      return screenStream;
    } catch (error) {
      console.error('Error starting screen share:', error);
      throw error;
    }
  }

  async stopScreenShare(): Promise<void> {
    if (!this.localStream || !this.peerConnection) return;

    try {
      // Get camera stream again
      const cameraStream = await this.getUserMedia({ video: true, audio: true });
      const videoTrack = cameraStream.getVideoTracks()[0];
      
      const sender = this.peerConnection.getSenders().find(s => 
        s.track && s.track.kind === 'video'
      );

      if (sender) {
        await sender.replaceTrack(videoTrack);
      }
    } catch (error) {
      console.error('Error stopping screen share:', error);
      throw error;
    }
  }

  getConnectionQuality(): 'poor' | 'fair' | 'good' | 'excellent' {
    if (!this.peerConnection) return 'poor';

    // This is a simplified quality assessment
    // In production, you would analyze RTCStats
    const connectionState = this.peerConnection.connectionState;
    
    switch (connectionState) {
      case 'connected':
        return 'excellent';
      case 'connecting':
        return 'good';
      case 'new':
        return 'fair';
      default:
        return 'poor';
    }
  }

  async getStats(): Promise<RTCStatsReport | null> {
    if (!this.peerConnection) return null;

    try {
      return await this.peerConnection.getStats();
    } catch (error) {
      console.error('Error getting stats:', error);
      return null;
    }
  }

  endCall(): void {
    // Stop all tracks
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }

    if (this.remoteStream) {
      this.remoteStream.getTracks().forEach(track => track.stop());
      this.remoteStream = null;
    }

    // Close peer connection
    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    this.callSession = null;
  }

  // Event handlers (to be overridden by implementing classes)
  protected handleIceCandidate(candidate: RTCIceCandidate): void {
    console.log('ICE candidate:', candidate);
    // Send candidate to remote peer via signaling server
  }

  protected handleRemoteStream(stream: MediaStream): void {
    console.log('Remote stream received:', stream);
    // Handle remote stream (e.g., attach to video element)
  }

  protected handleConnectionStateChange(state: RTCPeerConnectionState): void {
    console.log('Connection state changed:', state);
    // Handle connection state changes
  }

  // Getters
  get localMediaStream(): MediaStream | null {
    return this.localStream;
  }

  get remoteMediaStream(): MediaStream | null {
    return this.remoteStream;
  }

  get connectionState(): RTCPeerConnectionState | null {
    return this.peerConnection?.connectionState || null;
  }

  get isConnected(): boolean {
    return this.peerConnection?.connectionState === 'connected';
  }
}

// Signaling service for WebRTC
export class SignalingService {
  private static instance: SignalingService;
  private websocket: WebSocket | null = null;
  private callbacks: Map<string, Function> = new Map();

  static getInstance(): SignalingService {
    if (!SignalingService.instance) {
      SignalingService.instance = new SignalingService();
    }
    return SignalingService.instance;
  }

  connect(url: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.websocket = new WebSocket(url);

        this.websocket.onopen = () => {
          console.log('Signaling server connected');
          resolve();
        };

        this.websocket.onmessage = (event) => {
          const message = JSON.parse(event.data);
          this.handleMessage(message);
        };

        this.websocket.onerror = (error) => {
          console.error('Signaling error:', error);
          reject(error);
        };

        this.websocket.onclose = () => {
          console.log('Signaling server disconnected');
        };
      } catch (error) {
        reject(error);
      }
    });
  }

  send(message: any): void {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify(message));
    }
  }

  on(event: string, callback: Function): void {
    this.callbacks.set(event, callback);
  }

  off(event: string): void {
    this.callbacks.delete(event);
  }

  private handleMessage(message: any): void {
    const callback = this.callbacks.get(message.type);
    if (callback) {
      callback(message.data);
    }
  }

  disconnect(): void {
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }
    this.callbacks.clear();
  }
}