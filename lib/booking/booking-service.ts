import { createClient } from '@supabase/supabase-js';
import { addDays, addWeeks, addMonths, format, parseISO } from 'date-fns';

export interface RecurringBookingPattern {
  type: 'weekly' | 'biweekly' | 'monthly' | 'custom';
  frequency: number;
  daysOfWeek: number[];
  endDate?: Date;
  maxOccurrences?: number;
}

export interface GroupBookingData {
  companionId: string;
  organizerId: string;
  bookingTime: Date;
  durationHours: number;
  maxParticipants: number;
  costPerPerson: number;
  description?: string;
  requirements?: string;
  location?: string;
}

export interface WaitlistEntry {
  userId: string;
  companionId: string;
  preferredDate?: Date;
  preferredTimeStart?: string;
  preferredTimeEnd?: string;
  durationHours: number;
  flexibleDates: boolean;
  dateRangeStart?: Date;
  dateRangeEnd?: Date;
  maxPrice?: number;
  autoBook: boolean;
}

export interface BookingModification {
  bookingId: string;
  modificationType: 'time_change' | 'duration_change' | 'location_change' | 'cancellation';
  originalData: any;
  requestedData: any;
  reason?: string;
}

export interface CancellationPolicy {
  id: string;
  name: string;
  description: string;
  rules: Array<{
    hours_before: number;
    fee_percentage: number;
    description: string;
  }>;
}

export class BookingService {
  private static instance: BookingService;
  private supabase: any;

  static getInstance(): BookingService {
    if (!BookingService.instance) {
      BookingService.instance = new BookingService();
    }
    return BookingService.instance;
  }

  initialize(supabaseClient: any): void {
    this.supabase = supabaseClient;
  }

  // Recurring Bookings
  async createRecurringBooking(
    userId: string,
    companionId: string,
    pattern: RecurringBookingPattern,
    timeOfDay: string,
    durationHours: number,
    startDate: Date
  ): Promise<any> {
    try {
      const { data: companion } = await this.supabase
        .from('companions')
        .select('hourly_rate')
        .eq('id', companionId)
        .single();

      const totalAmount = companion.hourly_rate * durationHours;

      const { data, error } = await this.supabase
        .from('recurring_bookings')
        .insert({
          user_id: userId,
          companion_id: companionId,
          pattern_type: pattern.type,
          frequency: pattern.frequency,
          days_of_week: pattern.daysOfWeek,
          time_of_day: timeOfDay,
          duration_hours: durationHours,
          start_date: format(startDate, 'yyyy-MM-dd'),
          end_date: pattern.endDate ? format(pattern.endDate, 'yyyy-MM-dd') : null,
          max_occurrences: pattern.maxOccurrences,
          total_amount: totalAmount,
          next_booking_date: format(startDate, 'yyyy-MM-dd'),
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating recurring booking:', error);
      throw error;
    }
  }

  async updateRecurringBooking(
    recurringBookingId: string,
    updates: Partial<RecurringBookingPattern>
  ): Promise<any> {
    try {
      const { data, error } = await this.supabase
        .from('recurring_bookings')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', recurringBookingId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating recurring booking:', error);
      throw error;
    }
  }

  async pauseRecurringBooking(recurringBookingId: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('recurring_bookings')
        .update({ status: 'paused', updated_at: new Date().toISOString() })
        .eq('id', recurringBookingId);

      if (error) throw error;
    } catch (error) {
      console.error('Error pausing recurring booking:', error);
      throw error;
    }
  }

  async resumeRecurringBooking(recurringBookingId: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('recurring_bookings')
        .update({ status: 'active', updated_at: new Date().toISOString() })
        .eq('id', recurringBookingId);

      if (error) throw error;
    } catch (error) {
      console.error('Error resuming recurring booking:', error);
      throw error;
    }
  }

  // Group Bookings
  async createGroupBooking(groupBookingData: GroupBookingData): Promise<any> {
    try {
      const totalCost = groupBookingData.costPerPerson * groupBookingData.maxParticipants;

      const { data, error } = await this.supabase
        .from('group_bookings')
        .insert({
          companion_id: groupBookingData.companionId,
          organizer_id: groupBookingData.organizerId,
          booking_time: groupBookingData.bookingTime.toISOString(),
          duration_hours: groupBookingData.durationHours,
          max_participants: groupBookingData.maxParticipants,
          cost_per_person: groupBookingData.costPerPerson,
          total_cost: totalCost,
          description: groupBookingData.description,
          requirements: groupBookingData.requirements,
          location: groupBookingData.location,
          booking_deadline: addDays(new Date(), 3).toISOString(),
          cancellation_deadline: addDays(groupBookingData.bookingTime, -1).toISOString(),
        })
        .select()
        .single();

      if (error) throw error;

      // Add organizer as first participant
      await this.joinGroupBooking(data.id, groupBookingData.organizerId);

      return data;
    } catch (error) {
      console.error('Error creating group booking:', error);
      throw error;
    }
  }

  async joinGroupBooking(groupBookingId: string, userId: string): Promise<any> {
    try {
      // Get group booking details
      const { data: groupBooking } = await this.supabase
        .from('group_bookings')
        .select('*')
        .eq('id', groupBookingId)
        .single();

      if (!groupBooking) {
        throw new Error('Group booking not found');
      }

      if (groupBooking.status === 'full') {
        throw new Error('Group booking is full');
      }

      const { data, error } = await this.supabase
        .from('group_booking_participants')
        .insert({
          group_booking_id: groupBookingId,
          user_id: userId,
          payment_amount: groupBooking.cost_per_person,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error joining group booking:', error);
      throw error;
    }
  }

  async leaveGroupBooking(groupBookingId: string, userId: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('group_booking_participants')
        .update({
          status: 'cancelled',
          cancelled_at: new Date().toISOString(),
        })
        .eq('group_booking_id', groupBookingId)
        .eq('user_id', userId);

      if (error) throw error;
    } catch (error) {
      console.error('Error leaving group booking:', error);
      throw error;
    }
  }

  // Waitlist System
  async joinWaitlist(waitlistEntry: WaitlistEntry): Promise<any> {
    try {
      const expiresAt = addDays(new Date(), 30); // Expire after 30 days

      const { data, error } = await this.supabase
        .from('booking_waitlist')
        .insert({
          user_id: waitlistEntry.userId,
          companion_id: waitlistEntry.companionId,
          preferred_date: waitlistEntry.preferredDate ? format(waitlistEntry.preferredDate, 'yyyy-MM-dd') : null,
          preferred_time_start: waitlistEntry.preferredTimeStart,
          preferred_time_end: waitlistEntry.preferredTimeEnd,
          duration_hours: waitlistEntry.durationHours,
          flexible_dates: waitlistEntry.flexibleDates,
          date_range_start: waitlistEntry.dateRangeStart ? format(waitlistEntry.dateRangeStart, 'yyyy-MM-dd') : null,
          date_range_end: waitlistEntry.dateRangeEnd ? format(waitlistEntry.dateRangeEnd, 'yyyy-MM-dd') : null,
          max_price: waitlistEntry.maxPrice,
          auto_book: waitlistEntry.autoBook,
          expires_at: expiresAt.toISOString(),
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error joining waitlist:', error);
      throw error;
    }
  }

  async leaveWaitlist(waitlistId: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('booking_waitlist')
        .update({
          status: 'cancelled',
          updated_at: new Date().toISOString(),
        })
        .eq('id', waitlistId);

      if (error) throw error;
    } catch (error) {
      console.error('Error leaving waitlist:', error);
      throw error;
    }
  }

  async getWaitlistPosition(waitlistId: string): Promise<number> {
    try {
      const { data: waitlistEntry } = await this.supabase
        .from('booking_waitlist')
        .select('companion_id, created_at')
        .eq('id', waitlistId)
        .single();

      const { data: earlierEntries } = await this.supabase
        .from('booking_waitlist')
        .select('id')
        .eq('companion_id', waitlistEntry.companion_id)
        .eq('status', 'active')
        .lt('created_at', waitlistEntry.created_at);

      return (earlierEntries?.length || 0) + 1;
    } catch (error) {
      console.error('Error getting waitlist position:', error);
      return 0;
    }
  }

  // Booking Modifications
  async requestBookingModification(modification: BookingModification): Promise<any> {
    try {
      // Get original booking data
      const { data: booking } = await this.supabase
        .from('bookings')
        .select('*')
        .eq('id', modification.bookingId)
        .single();

      if (!booking) {
        throw new Error('Booking not found');
      }

      // Determine if approval is required
      const approvalRequired = this.requiresApproval(modification, booking);

      const { data, error } = await this.supabase
        .from('booking_modifications')
        .insert({
          booking_id: modification.bookingId,
          requested_by: booking.user_id,
          modification_type: modification.modificationType,
          original_data: modification.originalData,
          requested_data: modification.requestedData,
          reason: modification.reason,
          approval_required: approvalRequired,
          status: approvalRequired ? 'pending' : 'auto_approved',
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error requesting booking modification:', error);
      throw error;
    }
  }

  private requiresApproval(modification: BookingModification, booking: any): boolean {
    const bookingTime = parseISO(booking.booking_time);
    const hoursUntilBooking = (bookingTime.getTime() - Date.now()) / (1000 * 60 * 60);

    // Require approval if booking is within 24 hours
    if (hoursUntilBooking < 24) return true;

    // Require approval for significant changes
    if (modification.modificationType === 'duration_change') {
      const originalDuration = booking.duration_hours;
      const newDuration = modification.requestedData.duration_hours;
      if (Math.abs(newDuration - originalDuration) > 1) return true;
    }

    return false;
  }

  async approveModification(modificationId: string, approverId: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('booking_modifications')
        .update({
          status: 'approved',
          approved_by: approverId,
          approved_at: new Date().toISOString(),
        })
        .eq('id', modificationId);

      if (error) throw error;
    } catch (error) {
      console.error('Error approving modification:', error);
      throw error;
    }
  }

  async rejectModification(modificationId: string, reason: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('booking_modifications')
        .update({
          status: 'rejected',
          rejected_reason: reason,
        })
        .eq('id', modificationId);

      if (error) throw error;
    } catch (error) {
      console.error('Error rejecting modification:', error);
      throw error;
    }
  }

  // Cancellation Policies
  async getCancellationPolicies(): Promise<CancellationPolicy[]> {
    try {
      const { data, error } = await this.supabase
        .from('cancellation_policies')
        .select('*')
        .order('is_default', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting cancellation policies:', error);
      return [];
    }
  }

  async calculateCancellationFee(bookingId: string): Promise<number> {
    try {
      const { data, error } = await this.supabase
        .rpc('calculate_cancellation_fee', {
          booking_id_param: bookingId,
          cancellation_time: new Date().toISOString(),
        });

      if (error) throw error;
      return data || 0;
    } catch (error) {
      console.error('Error calculating cancellation fee:', error);
      return 0;
    }
  }

  async cancelBooking(bookingId: string, reason?: string): Promise<any> {
    try {
      // Calculate cancellation fee
      const cancellationFee = await this.calculateCancellationFee(bookingId);

      // Create cancellation modification
      const { data: booking } = await this.supabase
        .from('bookings')
        .select('*')
        .eq('id', bookingId)
        .single();

      const modification = await this.requestBookingModification({
        bookingId,
        modificationType: 'cancellation',
        originalData: booking,
        requestedData: { status: 'cancelled' },
        reason,
      });

      // Update modification with fee
      await this.supabase
        .from('booking_modifications')
        .update({
          fee_amount: cancellationFee,
          fee_reason: 'Cancellation fee per policy',
        })
        .eq('id', modification.id);

      return { modification, cancellationFee };
    } catch (error) {
      console.error('Error canceling booking:', error);
      throw error;
    }
  }

  // Calendar Integration
  async syncBookingToCalendar(bookingId: string, userId: string): Promise<void> {
    try {
      const { data: booking } = await this.supabase
        .from('bookings')
        .select(`
          *,
          companions(
            profiles(full_name)
          )
        `)
        .eq('id', bookingId)
        .single();

      if (!booking) return;

      const companionName = booking.companions.profiles.full_name;
      const startTime = parseISO(booking.booking_time);
      const endTime = addHours(startTime, booking.duration_hours);

      const { error } = await this.supabase
        .from('calendar_events')
        .insert({
          booking_id: bookingId,
          user_id: userId,
          title: `Booking with ${companionName}`,
          description: `Companion booking - ${booking.duration_hours} hours`,
          start_time: startTime.toISOString(),
          end_time: endTime.toISOString(),
          location: booking.location,
        });

      if (error) throw error;
    } catch (error) {
      console.error('Error syncing booking to calendar:', error);
      throw error;
    }
  }

  // Utility functions
  async getBookingsByType(userId: string, type: string): Promise<any[]> {
    try {
      const { data, error } = await this.supabase
        .from('bookings')
        .select(`
          *,
          companions(
            *,
            profiles(*)
          )
        `)
        .eq('user_id', userId)
        .eq('booking_type', type)
        .order('booking_time', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting bookings by type:', error);
      return [];
    }
  }

  async getGroupBookings(userId: string): Promise<any[]> {
    try {
      const { data, error } = await this.supabase
        .from('group_bookings')
        .select(`
          *,
          companions(
            *,
            profiles(*)
          ),
          group_booking_participants(
            *,
            profiles(full_name, avatar_url)
          )
        `)
        .or(`organizer_id.eq.${userId},id.in.(${
          await this.getUserGroupBookingIds(userId)
        })`)
        .order('booking_time', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting group bookings:', error);
      return [];
    }
  }

  private async getUserGroupBookingIds(userId: string): Promise<string> {
    const { data } = await this.supabase
      .from('group_booking_participants')
      .select('group_booking_id')
      .eq('user_id', userId);

    return data?.map(p => p.group_booking_id).join(',') || '';
  }
}

function addHours(date: Date, hours: number): Date {
  return new Date(date.getTime() + hours * 60 * 60 * 1000);
}