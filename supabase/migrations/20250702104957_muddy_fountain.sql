/*
  # Real-time Features and Notifications System

  1. New Tables
    - user_online_status
      - Track user online/offline status
      - Last seen timestamps
    - push_notification_tokens
      - Store device push notification tokens
      - Platform-specific token management
    - notification_preferences
      - User notification preferences
      - Granular control over notification types
    - message_read_receipts
      - Track message read status
      - Real-time read indicators

  2. Security
    - Enable RLS on all new tables
    - Secure access to notification data
    - Privacy controls for online status

  3. Functions
    - Auto-update online status
    - Clean up old tokens
    - Notification delivery tracking
*/

-- Create user_online_status table
CREATE TABLE IF NOT EXISTS user_online_status (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL UNIQUE,
  is_online boolean DEFAULT false,
  last_seen timestamptz DEFAULT now(),
  last_activity timestamptz DEFAULT now(),
  device_info jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE user_online_status ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read all online status"
  ON user_online_status
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Users can update own online status"
  ON user_online_status
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

-- Create push_notification_tokens table
CREATE TABLE IF NOT EXISTS push_notification_tokens (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  token text NOT NULL,
  platform text NOT NULL CHECK (platform IN ('ios', 'android', 'web')),
  device_id text,
  device_name text,
  app_version text,
  is_active boolean DEFAULT true,
  last_used_at timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(user_id, token, platform)
);

ALTER TABLE push_notification_tokens ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own push tokens"
  ON push_notification_tokens
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

-- Create notification_preferences table
CREATE TABLE IF NOT EXISTS notification_preferences (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL UNIQUE,
  push_notifications boolean DEFAULT true,
  email_notifications boolean DEFAULT true,
  sms_notifications boolean DEFAULT false,
  message_notifications boolean DEFAULT true,
  booking_notifications boolean DEFAULT true,
  emergency_notifications boolean DEFAULT true,
  marketing_notifications boolean DEFAULT false,
  quiet_hours_start time,
  quiet_hours_end time,
  timezone text DEFAULT 'UTC',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE notification_preferences ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own notification preferences"
  ON notification_preferences
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

-- Create message_read_receipts table
CREATE TABLE IF NOT EXISTS message_read_receipts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  message_id uuid REFERENCES messages(id) ON DELETE CASCADE NOT NULL,
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  read_at timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now(),
  UNIQUE(message_id, user_id)
);

ALTER TABLE message_read_receipts ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read receipts for their conversations"
  ON message_read_receipts
  FOR SELECT
  TO authenticated
  USING (
    message_id IN (
      SELECT m.id FROM messages m
      JOIN conversations c ON m.conversation_id = c.id
      WHERE c.user_id = auth.uid() 
      OR c.companion_id IN (SELECT id FROM companions WHERE profile_id = auth.uid())
    )
  );

CREATE POLICY "Users can create read receipts"
  ON message_read_receipts
  FOR INSERT
  TO authenticated
  WITH CHECK (user_id = auth.uid());

-- Create notification_logs table for tracking
CREATE TABLE IF NOT EXISTS notification_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  notification_type text NOT NULL,
  title text NOT NULL,
  body text NOT NULL,
  data jsonb DEFAULT '{}',
  delivery_status text DEFAULT 'pending' CHECK (delivery_status IN ('pending', 'sent', 'delivered', 'failed')),
  platform text CHECK (platform IN ('ios', 'android', 'web', 'email', 'sms')),
  external_id text,
  error_message text,
  sent_at timestamptz,
  delivered_at timestamptz,
  created_at timestamptz DEFAULT now()
);

ALTER TABLE notification_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own notification logs"
  ON notification_logs
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS user_online_status_user_id_idx ON user_online_status (user_id);
CREATE INDEX IF NOT EXISTS user_online_status_is_online_idx ON user_online_status (is_online, last_seen DESC);
CREATE INDEX IF NOT EXISTS push_tokens_user_active_idx ON push_notification_tokens (user_id, is_active);
CREATE INDEX IF NOT EXISTS push_tokens_platform_idx ON push_notification_tokens (platform, is_active);
CREATE INDEX IF NOT EXISTS message_read_receipts_message_idx ON message_read_receipts (message_id);
CREATE INDEX IF NOT EXISTS notification_logs_user_type_idx ON notification_logs (user_id, notification_type, created_at DESC);

-- Create function to auto-create notification preferences
CREATE OR REPLACE FUNCTION create_default_notification_preferences()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO notification_preferences (user_id)
  VALUES (NEW.id)
  ON CONFLICT (user_id) DO NOTHING;
  
  INSERT INTO user_online_status (user_id)
  VALUES (NEW.id)
  ON CONFLICT (user_id) DO NOTHING;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER create_default_notification_preferences_trigger
AFTER INSERT ON profiles
FOR EACH ROW
EXECUTE FUNCTION create_default_notification_preferences();

-- Create function to update online status timestamp
CREATE OR REPLACE FUNCTION update_online_status_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  IF NEW.is_online = true THEN
    NEW.last_activity = now();
  ELSE
    NEW.last_seen = now();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_online_status_timestamp_trigger
BEFORE UPDATE ON user_online_status
FOR EACH ROW
EXECUTE FUNCTION update_online_status_timestamp();

-- Create function to clean up old push tokens
CREATE OR REPLACE FUNCTION cleanup_old_push_tokens()
RETURNS void AS $$
BEGIN
  -- Mark tokens as inactive if not used for 30 days
  UPDATE push_notification_tokens
  SET is_active = false,
      updated_at = now()
  WHERE last_used_at < now() - INTERVAL '30 days'
    AND is_active = true;
    
  -- Delete very old inactive tokens (90 days)
  DELETE FROM push_notification_tokens
  WHERE is_active = false
    AND updated_at < now() - INTERVAL '90 days';
END;
$$ LANGUAGE plpgsql;

-- Create function to mark users offline after inactivity
CREATE OR REPLACE FUNCTION mark_inactive_users_offline()
RETURNS void AS $$
BEGIN
  UPDATE user_online_status
  SET is_online = false,
      last_seen = last_activity,
      updated_at = now()
  WHERE is_online = true
    AND last_activity < now() - INTERVAL '5 minutes';
END;
$$ LANGUAGE plpgsql;

-- Create function to get unread message count
CREATE OR REPLACE FUNCTION get_unread_message_count(user_id_param uuid)
RETURNS integer AS $$
DECLARE
  unread_count integer;
BEGIN
  SELECT COUNT(*)::integer INTO unread_count
  FROM messages m
  JOIN conversations c ON m.conversation_id = c.id
  WHERE (c.user_id = user_id_param OR c.companion_id IN (
    SELECT id FROM companions WHERE profile_id = user_id_param
  ))
  AND m.sender_id != user_id_param
  AND NOT EXISTS (
    SELECT 1 FROM message_read_receipts mrr
    WHERE mrr.message_id = m.id AND mrr.user_id = user_id_param
  );
  
  RETURN COALESCE(unread_count, 0);
END;
$$ LANGUAGE plpgsql;

-- Create function to mark conversation messages as read
CREATE OR REPLACE FUNCTION mark_conversation_messages_read(
  conversation_id_param uuid,
  user_id_param uuid
)
RETURNS void AS $$
BEGIN
  INSERT INTO message_read_receipts (message_id, user_id)
  SELECT m.id, user_id_param
  FROM messages m
  WHERE m.conversation_id = conversation_id_param
    AND m.sender_id != user_id_param
    AND NOT EXISTS (
      SELECT 1 FROM message_read_receipts mrr
      WHERE mrr.message_id = m.id AND mrr.user_id = user_id_param
    )
  ON CONFLICT (message_id, user_id) DO NOTHING;
END;
$$ LANGUAGE plpgsql;

-- Insert default notification preferences for existing users
INSERT INTO notification_preferences (user_id)
SELECT id FROM profiles
WHERE id NOT IN (SELECT user_id FROM notification_preferences)
ON CONFLICT (user_id) DO NOTHING;

-- Insert default online status for existing users
INSERT INTO user_online_status (user_id)
SELECT id FROM profiles
WHERE id NOT IN (SELECT user_id FROM user_online_status)
ON CONFLICT (user_id) DO NOTHING;