/*
  # Advanced Booking Features Implementation

  1. New Tables
    - recurring_bookings
      - Weekly/monthly recurring companion appointments
      - Flexible scheduling patterns and end dates
    - group_bookings
      - Multiple users booking same companion
      - Split payments and group management
    - booking_waitlist
      - Join waitlist when companion unavailable
      - Automatic booking when slots open
    - booking_modifications
      - Track all booking changes and history
      - Approval workflow for modifications
    - cancellation_policies
      - Flexible cancellation rules and fees
      - Time-based cancellation penalties
    - calendar_integrations
      - Sync with device calendars
      - External calendar connections

  2. Security
    - Enable RLS on all new tables
    - Secure access to booking data
    - Privacy controls for group bookings

  3. Functions
    - Auto-process recurring bookings
    - Handle waitlist notifications
    - Calculate cancellation fees
    - Sync calendar events
*/

-- Create recurring_bookings table
CREATE TABLE IF NOT EXISTS recurring_bookings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  companion_id uuid REFERENCES companions(id) ON DELETE CASCADE NOT NULL,
  pattern_type text NOT NULL CHECK (pattern_type IN ('weekly', 'biweekly', 'monthly', 'custom')),
  frequency integer DEFAULT 1, -- Every N weeks/months
  days_of_week integer[] DEFAULT '{}', -- 0=Sunday, 1=Monday, etc.
  time_of_day time NOT NULL,
  duration_hours integer NOT NULL,
  start_date date NOT NULL,
  end_date date,
  max_occurrences integer,
  current_occurrences integer DEFAULT 0,
  status text DEFAULT 'active' CHECK (status IN ('active', 'paused', 'cancelled', 'completed')),
  next_booking_date date,
  total_amount decimal NOT NULL,
  payment_method_id uuid REFERENCES payment_methods(id),
  auto_pay boolean DEFAULT true,
  created_bookings uuid[] DEFAULT '{}', -- Array of created booking IDs
  failed_attempts integer DEFAULT 0,
  last_processed_at timestamptz,
  notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE recurring_bookings ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own recurring bookings"
  ON recurring_bookings
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid() OR companion_id IN (
    SELECT id FROM companions WHERE profile_id = auth.uid()
  ));

-- Create group_bookings table
CREATE TABLE IF NOT EXISTS group_bookings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  companion_id uuid REFERENCES companions(id) ON DELETE CASCADE NOT NULL,
  organizer_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  booking_time timestamptz NOT NULL,
  duration_hours integer NOT NULL,
  max_participants integer NOT NULL,
  current_participants integer DEFAULT 1,
  cost_per_person decimal NOT NULL,
  total_cost decimal NOT NULL,
  status text DEFAULT 'open' CHECK (status IN ('open', 'full', 'confirmed', 'completed', 'cancelled')),
  split_payment_method text DEFAULT 'equal' CHECK (split_payment_method IN ('equal', 'custom', 'organizer_pays')),
  location text,
  description text,
  requirements text,
  age_restriction integer,
  gender_preference text CHECK (gender_preference IN ('any', 'male', 'female', 'non_binary')),
  booking_deadline timestamptz,
  cancellation_deadline timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE group_bookings ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can read open group bookings"
  ON group_bookings
  FOR SELECT
  TO authenticated
  USING (status IN ('open', 'full', 'confirmed'));

CREATE POLICY "Organizers can manage own group bookings"
  ON group_bookings
  FOR ALL
  TO authenticated
  USING (organizer_id = auth.uid());

-- Create group_booking_participants table
CREATE TABLE IF NOT EXISTS group_booking_participants (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  group_booking_id uuid REFERENCES group_bookings(id) ON DELETE CASCADE NOT NULL,
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  status text DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'paid', 'cancelled')),
  payment_amount decimal NOT NULL,
  payment_status text DEFAULT 'pending' CHECK (payment_status IN ('pending', 'processing', 'paid', 'failed', 'refunded')),
  payment_intent_id text,
  joined_at timestamptz DEFAULT now(),
  confirmed_at timestamptz,
  cancelled_at timestamptz,
  special_requests text,
  UNIQUE(group_booking_id, user_id)
);

ALTER TABLE group_booking_participants ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read group booking participants"
  ON group_booking_participants
  FOR SELECT
  TO authenticated
  USING (
    user_id = auth.uid() 
    OR group_booking_id IN (SELECT id FROM group_bookings WHERE organizer_id = auth.uid())
  );

CREATE POLICY "Users can manage own participation"
  ON group_booking_participants
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

-- Create booking_waitlist table
CREATE TABLE IF NOT EXISTS booking_waitlist (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  companion_id uuid REFERENCES companions(id) ON DELETE CASCADE NOT NULL,
  preferred_date date,
  preferred_time_start time,
  preferred_time_end time,
  duration_hours integer NOT NULL,
  flexible_dates boolean DEFAULT false,
  date_range_start date,
  date_range_end date,
  max_price decimal,
  status text DEFAULT 'active' CHECK (status IN ('active', 'notified', 'booked', 'expired', 'cancelled')),
  priority_score integer DEFAULT 0,
  notification_sent_at timestamptz,
  expires_at timestamptz,
  auto_book boolean DEFAULT false,
  notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE booking_waitlist ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own waitlist entries"
  ON booking_waitlist
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Companions can read their waitlist"
  ON booking_waitlist
  FOR SELECT
  TO authenticated
  USING (companion_id IN (SELECT id FROM companions WHERE profile_id = auth.uid()));

-- Create booking_modifications table
CREATE TABLE IF NOT EXISTS booking_modifications (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  booking_id uuid REFERENCES bookings(id) ON DELETE CASCADE NOT NULL,
  requested_by uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  modification_type text NOT NULL CHECK (modification_type IN ('time_change', 'duration_change', 'location_change', 'cancellation')),
  original_data jsonb NOT NULL,
  requested_data jsonb NOT NULL,
  status text DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'auto_approved')),
  reason text,
  approval_required boolean DEFAULT true,
  approved_by uuid REFERENCES profiles(id),
  approved_at timestamptz,
  rejected_reason text,
  fee_amount decimal DEFAULT 0,
  fee_reason text,
  processed_at timestamptz,
  created_at timestamptz DEFAULT now()
);

ALTER TABLE booking_modifications ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read modifications for their bookings"
  ON booking_modifications
  FOR SELECT
  TO authenticated
  USING (
    booking_id IN (
      SELECT id FROM bookings 
      WHERE user_id = auth.uid() 
      OR companion_id IN (SELECT id FROM companions WHERE profile_id = auth.uid())
    )
  );

CREATE POLICY "Users can create modifications for their bookings"
  ON booking_modifications
  FOR INSERT
  TO authenticated
  WITH CHECK (
    requested_by = auth.uid()
    AND booking_id IN (
      SELECT id FROM bookings 
      WHERE user_id = auth.uid() 
      OR companion_id IN (SELECT id FROM companions WHERE profile_id = auth.uid())
    )
  );

-- Create cancellation_policies table
CREATE TABLE IF NOT EXISTS cancellation_policies (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  companion_id uuid REFERENCES companions(id) ON DELETE CASCADE,
  policy_name text NOT NULL,
  description text,
  is_default boolean DEFAULT false,
  rules jsonb NOT NULL, -- Array of time-based rules
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE cancellation_policies ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can read cancellation policies"
  ON cancellation_policies
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Companions can manage own policies"
  ON cancellation_policies
  FOR ALL
  TO authenticated
  USING (companion_id IN (SELECT id FROM companions WHERE profile_id = auth.uid()) OR companion_id IS NULL);

-- Create calendar_integrations table
CREATE TABLE IF NOT EXISTS calendar_integrations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  calendar_type text NOT NULL CHECK (calendar_type IN ('google', 'apple', 'outlook', 'device')),
  calendar_id text,
  calendar_name text,
  access_token text,
  refresh_token text,
  sync_enabled boolean DEFAULT true,
  sync_direction text DEFAULT 'both' CHECK (sync_direction IN ('import', 'export', 'both')),
  last_sync_at timestamptz,
  sync_errors jsonb DEFAULT '[]',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE calendar_integrations ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own calendar integrations"
  ON calendar_integrations
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

-- Create calendar_events table
CREATE TABLE IF NOT EXISTS calendar_events (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  booking_id uuid REFERENCES bookings(id) ON DELETE CASCADE,
  group_booking_id uuid REFERENCES group_bookings(id) ON DELETE CASCADE,
  recurring_booking_id uuid REFERENCES recurring_bookings(id) ON DELETE CASCADE,
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  calendar_integration_id uuid REFERENCES calendar_integrations(id) ON DELETE CASCADE,
  external_event_id text,
  title text NOT NULL,
  description text,
  start_time timestamptz NOT NULL,
  end_time timestamptz NOT NULL,
  location text,
  is_synced boolean DEFAULT false,
  sync_status text DEFAULT 'pending' CHECK (sync_status IN ('pending', 'synced', 'failed', 'deleted')),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE calendar_events ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own calendar events"
  ON calendar_events
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

-- Add new fields to existing bookings table
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'bookings' AND column_name = 'booking_type'
  ) THEN
    ALTER TABLE bookings ADD COLUMN booking_type text DEFAULT 'standard' CHECK (booking_type IN ('standard', 'recurring', 'group', 'waitlist'));
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'bookings' AND column_name = 'recurring_booking_id'
  ) THEN
    ALTER TABLE bookings ADD COLUMN recurring_booking_id uuid REFERENCES recurring_bookings(id);
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'bookings' AND column_name = 'group_booking_id'
  ) THEN
    ALTER TABLE bookings ADD COLUMN group_booking_id uuid REFERENCES group_bookings(id);
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'bookings' AND column_name = 'waitlist_id'
  ) THEN
    ALTER TABLE bookings ADD COLUMN waitlist_id uuid REFERENCES booking_waitlist(id);
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'bookings' AND column_name = 'cancellation_policy_id'
  ) THEN
    ALTER TABLE bookings ADD COLUMN cancellation_policy_id uuid REFERENCES cancellation_policies(id);
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'bookings' AND column_name = 'cancellation_fee'
  ) THEN
    ALTER TABLE bookings ADD COLUMN cancellation_fee decimal DEFAULT 0;
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'bookings' AND column_name = 'modification_count'
  ) THEN
    ALTER TABLE bookings ADD COLUMN modification_count integer DEFAULT 0;
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'bookings' AND column_name = 'is_flexible'
  ) THEN
    ALTER TABLE bookings ADD COLUMN is_flexible boolean DEFAULT false;
  END IF;
END $$;

-- Insert default cancellation policies
INSERT INTO cancellation_policies (policy_name, description, is_default, rules) VALUES
('Standard Policy', 'Standard cancellation policy for most companions', true, '[
  {"hours_before": 24, "fee_percentage": 0, "description": "Free cancellation 24+ hours before"},
  {"hours_before": 12, "fee_percentage": 25, "description": "25% fee for cancellation 12-24 hours before"},
  {"hours_before": 2, "fee_percentage": 50, "description": "50% fee for cancellation 2-12 hours before"},
  {"hours_before": 0, "fee_percentage": 100, "description": "No refund for cancellation less than 2 hours before"}
]'),
('Flexible Policy', 'More lenient cancellation policy', false, '[
  {"hours_before": 12, "fee_percentage": 0, "description": "Free cancellation 12+ hours before"},
  {"hours_before": 2, "fee_percentage": 25, "description": "25% fee for cancellation 2-12 hours before"},
  {"hours_before": 0, "fee_percentage": 50, "description": "50% fee for cancellation less than 2 hours before"}
]'),
('Strict Policy', 'Strict cancellation policy for premium companions', false, '[
  {"hours_before": 48, "fee_percentage": 0, "description": "Free cancellation 48+ hours before"},
  {"hours_before": 24, "fee_percentage": 50, "description": "50% fee for cancellation 24-48 hours before"},
  {"hours_before": 0, "fee_percentage": 100, "description": "No refund for cancellation less than 24 hours before"}
]')
ON CONFLICT DO NOTHING;

-- Create function to process recurring bookings
CREATE OR REPLACE FUNCTION process_recurring_bookings()
RETURNS void AS $$
DECLARE
  recurring_record RECORD;
  new_booking_date date;
  new_booking_time timestamptz;
  booking_id uuid;
BEGIN
  FOR recurring_record IN 
    SELECT * FROM recurring_bookings 
    WHERE status = 'active' 
    AND (next_booking_date IS NULL OR next_booking_date <= CURRENT_DATE)
    AND (end_date IS NULL OR CURRENT_DATE <= end_date)
    AND (max_occurrences IS NULL OR current_occurrences < max_occurrences)
  LOOP
    -- Calculate next booking date
    IF recurring_record.pattern_type = 'weekly' THEN
      new_booking_date := CURRENT_DATE + (recurring_record.frequency * 7);
    ELSIF recurring_record.pattern_type = 'biweekly' THEN
      new_booking_date := CURRENT_DATE + 14;
    ELSIF recurring_record.pattern_type = 'monthly' THEN
      new_booking_date := CURRENT_DATE + (recurring_record.frequency * 30);
    END IF;
    
    new_booking_time := new_booking_date + recurring_record.time_of_day;
    
    -- Create the booking
    INSERT INTO bookings (
      user_id, companion_id, booking_time, duration_hours, 
      total_amount, booking_type, recurring_booking_id, status
    ) VALUES (
      recurring_record.user_id, recurring_record.companion_id, 
      new_booking_time, recurring_record.duration_hours,
      recurring_record.total_amount, 'recurring', recurring_record.id, 'confirmed'
    ) RETURNING id INTO booking_id;
    
    -- Update recurring booking
    UPDATE recurring_bookings 
    SET 
      current_occurrences = current_occurrences + 1,
      next_booking_date = new_booking_date + (CASE 
        WHEN pattern_type = 'weekly' THEN frequency * 7
        WHEN pattern_type = 'biweekly' THEN 14
        WHEN pattern_type = 'monthly' THEN frequency * 30
        ELSE 7
      END),
      created_bookings = array_append(created_bookings, booking_id),
      last_processed_at = now(),
      updated_at = now()
    WHERE id = recurring_record.id;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Create function to calculate cancellation fee
CREATE OR REPLACE FUNCTION calculate_cancellation_fee(
  booking_id_param uuid,
  cancellation_time timestamptz DEFAULT now()
) RETURNS decimal AS $$
DECLARE
  booking_record RECORD;
  policy_record RECORD;
  hours_before decimal;
  rule jsonb;
  fee_percentage decimal := 100;
  cancellation_fee decimal;
BEGIN
  -- Get booking details
  SELECT b.*, cp.rules 
  INTO booking_record
  FROM bookings b
  LEFT JOIN cancellation_policies cp ON b.cancellation_policy_id = cp.id
  WHERE b.id = booking_id_param;
  
  IF NOT FOUND THEN
    RETURN 0;
  END IF;
  
  -- Calculate hours before booking
  hours_before := EXTRACT(EPOCH FROM (booking_record.booking_time - cancellation_time)) / 3600;
  
  -- If no policy, use default
  IF booking_record.rules IS NULL THEN
    SELECT rules INTO booking_record.rules 
    FROM cancellation_policies 
    WHERE is_default = true 
    LIMIT 1;
  END IF;
  
  -- Find applicable rule
  FOR rule IN SELECT * FROM jsonb_array_elements(booking_record.rules)
  LOOP
    IF hours_before >= (rule->>'hours_before')::decimal THEN
      fee_percentage := (rule->>'fee_percentage')::decimal;
      EXIT;
    END IF;
  END LOOP;
  
  cancellation_fee := booking_record.total_amount * (fee_percentage / 100);
  
  RETURN cancellation_fee;
END;
$$ LANGUAGE plpgsql;

-- Create function to process waitlist notifications
CREATE OR REPLACE FUNCTION process_waitlist_notifications()
RETURNS void AS $$
DECLARE
  waitlist_record RECORD;
  available_slot RECORD;
BEGIN
  FOR waitlist_record IN 
    SELECT * FROM booking_waitlist 
    WHERE status = 'active' 
    AND (expires_at IS NULL OR expires_at > now())
  LOOP
    -- Check for available slots (simplified logic)
    -- In production, this would check companion availability more thoroughly
    IF waitlist_record.auto_book = true THEN
      -- Auto-create booking if auto_book is enabled
      INSERT INTO bookings (
        user_id, companion_id, booking_time, duration_hours,
        total_amount, booking_type, waitlist_id, status
      ) VALUES (
        waitlist_record.user_id, waitlist_record.companion_id,
        waitlist_record.preferred_date + waitlist_record.preferred_time_start,
        waitlist_record.duration_hours,
        (SELECT hourly_rate FROM companions WHERE id = waitlist_record.companion_id) * waitlist_record.duration_hours,
        'waitlist', waitlist_record.id, 'pending'
      );
      
      -- Update waitlist status
      UPDATE booking_waitlist 
      SET status = 'booked', updated_at = now()
      WHERE id = waitlist_record.id;
    ELSE
      -- Send notification
      UPDATE booking_waitlist 
      SET status = 'notified', notification_sent_at = now(), updated_at = now()
      WHERE id = waitlist_record.id;
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Create function to update group booking status
CREATE OR REPLACE FUNCTION update_group_booking_status()
RETURNS TRIGGER AS $$
DECLARE
  participant_count integer;
  max_participants integer;
BEGIN
  -- Get current participant count and max
  SELECT 
    COUNT(*) FILTER (WHERE status IN ('confirmed', 'paid')),
    gb.max_participants
  INTO participant_count, max_participants
  FROM group_booking_participants gbp
  JOIN group_bookings gb ON gbp.group_booking_id = gb.id
  WHERE gbp.group_booking_id = COALESCE(NEW.group_booking_id, OLD.group_booking_id)
  GROUP BY gb.max_participants;
  
  -- Update group booking status
  UPDATE group_bookings
  SET 
    current_participants = participant_count,
    status = CASE 
      WHEN participant_count >= max_participants THEN 'full'
      WHEN participant_count > 0 THEN 'open'
      ELSE 'open'
    END,
    updated_at = now()
  WHERE id = COALESCE(NEW.group_booking_id, OLD.group_booking_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_group_booking_status_trigger
AFTER INSERT OR UPDATE OR DELETE ON group_booking_participants
FOR EACH ROW
EXECUTE FUNCTION update_group_booking_status();

-- Create function to handle booking modifications
CREATE OR REPLACE FUNCTION process_booking_modification()
RETURNS TRIGGER AS $$
DECLARE
  booking_record RECORD;
  modification_fee decimal := 0;
BEGIN
  IF NEW.status = 'approved' AND OLD.status = 'pending' THEN
    -- Get booking details
    SELECT * INTO booking_record FROM bookings WHERE id = NEW.booking_id;
    
    -- Apply modifications based on type
    CASE NEW.modification_type
      WHEN 'time_change' THEN
        UPDATE bookings 
        SET 
          booking_time = (NEW.requested_data->>'booking_time')::timestamptz,
          modification_count = modification_count + 1,
          updated_at = now()
        WHERE id = NEW.booking_id;
        
      WHEN 'duration_change' THEN
        UPDATE bookings 
        SET 
          duration_hours = (NEW.requested_data->>'duration_hours')::integer,
          total_amount = (NEW.requested_data->>'total_amount')::decimal,
          modification_count = modification_count + 1,
          updated_at = now()
        WHERE id = NEW.booking_id;
        
      WHEN 'location_change' THEN
        UPDATE bookings 
        SET 
          location = NEW.requested_data->>'location',
          modification_count = modification_count + 1,
          updated_at = now()
        WHERE id = NEW.booking_id;
        
      WHEN 'cancellation' THEN
        UPDATE bookings 
        SET 
          status = 'cancelled',
          cancellation_fee = NEW.fee_amount,
          updated_at = now()
        WHERE id = NEW.booking_id;
    END CASE;
    
    -- Mark modification as processed
    NEW.processed_at := now();
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER process_booking_modification_trigger
BEFORE UPDATE ON booking_modifications
FOR EACH ROW
EXECUTE FUNCTION process_booking_modification();

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS recurring_bookings_next_date_idx ON recurring_bookings (next_booking_date, status);
CREATE INDEX IF NOT EXISTS group_bookings_status_time_idx ON group_bookings (status, booking_time);
CREATE INDEX IF NOT EXISTS booking_waitlist_companion_date_idx ON booking_waitlist (companion_id, preferred_date, status);
CREATE INDEX IF NOT EXISTS booking_modifications_booking_status_idx ON booking_modifications (booking_id, status);
CREATE INDEX IF NOT EXISTS calendar_events_user_time_idx ON calendar_events (user_id, start_time);