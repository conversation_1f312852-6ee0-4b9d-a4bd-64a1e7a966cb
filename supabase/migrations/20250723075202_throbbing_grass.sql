/*
  # Enhanced UX, Social Features & Business Intelligence

  1. New Tables
    - `user_preferences` - Detailed user preference settings and matching criteria
    - `user_favorites` - Save favorite companions and locations
    - `recently_viewed` - Track recently viewed companion profiles
    - `user_reviews` - Detailed review and rating system with photos
    - `companion_photos` - Multiple photos for companion profiles
    - `user_referrals` - Referral program tracking and rewards
    - `onboarding_progress` - Track user onboarding completion
    - `analytics_events` - User behavior tracking for analytics
    - `ab_test_variants` - A/B testing framework
    - `user_segments` - User segmentation for targeted features

  2. Security
    - Enable RLS on all new tables
    - Add appropriate policies for data access
    - Secure analytics data collection

  3. Features
    - Advanced search and filtering
    - Social proof and verification
    - Comprehensive analytics tracking
    - User segmentation and personalization
*/

-- User Preferences Table
CREATE TABLE IF NOT EXISTS user_preferences (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  age_range_min integer DEFAULT 18,
  age_range_max integer DEFAULT 65,
  max_distance_km integer DEFAULT 25,
  price_range_min numeric DEFAULT 0,
  price_range_max numeric DEFAULT 1000,
  preferred_languages text[] DEFAULT ARRAY['English'],
  preferred_interests text[] DEFAULT '{}',
  preferred_locations text[] DEFAULT '{}',
  notification_preferences jsonb DEFAULT '{}',
  matching_preferences jsonb DEFAULT '{}',
  privacy_settings jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own preferences"
  ON user_preferences
  FOR ALL
  TO authenticated
  USING (user_id = uid())
  WITH CHECK (user_id = uid());

-- User Favorites Table
CREATE TABLE IF NOT EXISTS user_favorites (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  favorite_type text NOT NULL CHECK (favorite_type IN ('companion', 'location', 'search')),
  companion_id uuid REFERENCES companions(id) ON DELETE CASCADE,
  location_data jsonb,
  search_criteria jsonb,
  notes text,
  created_at timestamptz DEFAULT now()
);

ALTER TABLE user_favorites ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own favorites"
  ON user_favorites
  FOR ALL
  TO authenticated
  USING (user_id = uid())
  WITH CHECK (user_id = uid());

CREATE INDEX user_favorites_user_type_idx ON user_favorites(user_id, favorite_type);

-- Recently Viewed Table
CREATE TABLE IF NOT EXISTS recently_viewed (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  companion_id uuid NOT NULL REFERENCES companions(id) ON DELETE CASCADE,
  view_duration_seconds integer DEFAULT 0,
  viewed_at timestamptz DEFAULT now()
);

ALTER TABLE recently_viewed ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own recently viewed"
  ON recently_viewed
  FOR ALL
  TO authenticated
  USING (user_id = uid())
  WITH CHECK (user_id = uid());

CREATE INDEX recently_viewed_user_date_idx ON recently_viewed(user_id, viewed_at DESC);

-- Enhanced Reviews Table (replacing existing reviews)
DROP TABLE IF EXISTS reviews CASCADE;

CREATE TABLE IF NOT EXISTS user_reviews (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  booking_id uuid NOT NULL REFERENCES bookings(id) ON DELETE CASCADE,
  reviewer_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  companion_id uuid NOT NULL REFERENCES companions(id) ON DELETE CASCADE,
  overall_rating integer NOT NULL CHECK (overall_rating >= 1 AND overall_rating <= 5),
  communication_rating integer CHECK (communication_rating >= 1 AND communication_rating <= 5),
  punctuality_rating integer CHECK (punctuality_rating >= 1 AND punctuality_rating <= 5),
  appearance_rating integer CHECK (appearance_rating >= 1 AND appearance_rating <= 5),
  experience_rating integer CHECK (experience_rating >= 1 AND experience_rating <= 5),
  title text,
  content text,
  photos text[] DEFAULT '{}',
  is_verified boolean DEFAULT false,
  is_featured boolean DEFAULT false,
  helpful_count integer DEFAULT 0,
  not_helpful_count integer DEFAULT 0,
  response_from_companion text,
  response_date timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE user_reviews ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can read published reviews"
  ON user_reviews
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Users can create reviews for completed bookings"
  ON user_reviews
  FOR INSERT
  TO authenticated
  WITH CHECK (
    reviewer_id = uid() AND 
    booking_id IN (
      SELECT id FROM bookings 
      WHERE user_id = uid() AND status = 'completed'
    )
  );

CREATE UNIQUE INDEX user_reviews_booking_id_key ON user_reviews(booking_id);

-- Companion Photos Table
CREATE TABLE IF NOT EXISTS companion_photos (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  companion_id uuid NOT NULL REFERENCES companions(id) ON DELETE CASCADE,
  photo_url text NOT NULL,
  photo_order integer DEFAULT 0,
  caption text,
  is_primary boolean DEFAULT false,
  is_verified boolean DEFAULT false,
  uploaded_at timestamptz DEFAULT now()
);

ALTER TABLE companion_photos ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can read companion photos"
  ON companion_photos
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Companions can manage own photos"
  ON companion_photos
  FOR ALL
  TO authenticated
  USING (companion_id IN (
    SELECT id FROM companions WHERE profile_id = uid()
  ))
  WITH CHECK (companion_id IN (
    SELECT id FROM companions WHERE profile_id = uid()
  ));

CREATE INDEX companion_photos_companion_order_idx ON companion_photos(companion_id, photo_order);

-- User Referrals Table
CREATE TABLE IF NOT EXISTS user_referrals (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  referrer_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  referred_id uuid REFERENCES profiles(id) ON DELETE SET NULL,
  referral_code text UNIQUE NOT NULL,
  email text,
  phone_number text,
  status text DEFAULT 'pending' CHECK (status IN ('pending', 'signed_up', 'completed', 'expired')),
  reward_amount numeric DEFAULT 0,
  reward_type text DEFAULT 'credit' CHECK (reward_type IN ('credit', 'discount', 'free_booking')),
  reward_claimed boolean DEFAULT false,
  reward_claimed_at timestamptz,
  expires_at timestamptz DEFAULT (now() + interval '30 days'),
  created_at timestamptz DEFAULT now()
);

ALTER TABLE user_referrals ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own referrals"
  ON user_referrals
  FOR ALL
  TO authenticated
  USING (referrer_id = uid() OR referred_id = uid())
  WITH CHECK (referrer_id = uid());

CREATE INDEX user_referrals_code_idx ON user_referrals(referral_code);
CREATE INDEX user_referrals_status_idx ON user_referrals(status, expires_at);

-- Onboarding Progress Table
CREATE TABLE IF NOT EXISTS onboarding_progress (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  step_name text NOT NULL,
  completed boolean DEFAULT false,
  completed_at timestamptz,
  data jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now()
);

ALTER TABLE onboarding_progress ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own onboarding progress"
  ON onboarding_progress
  FOR ALL
  TO authenticated
  USING (user_id = uid())
  WITH CHECK (user_id = uid());

CREATE UNIQUE INDEX onboarding_progress_user_step_idx ON onboarding_progress(user_id, step_name);

-- Analytics Events Table
CREATE TABLE IF NOT EXISTS analytics_events (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE SET NULL,
  session_id text,
  event_name text NOT NULL,
  event_category text NOT NULL,
  event_properties jsonb DEFAULT '{}',
  user_properties jsonb DEFAULT '{}',
  device_info jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now()
);

ALTER TABLE analytics_events ENABLE ROW LEVEL SECURITY;

CREATE POLICY "System can insert analytics events"
  ON analytics_events
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE INDEX analytics_events_user_date_idx ON analytics_events(user_id, created_at DESC);
CREATE INDEX analytics_events_name_date_idx ON analytics_events(event_name, created_at DESC);
CREATE INDEX analytics_events_category_date_idx ON analytics_events(event_category, created_at DESC);

-- A/B Test Variants Table
CREATE TABLE IF NOT EXISTS ab_test_variants (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  test_name text NOT NULL,
  variant_name text NOT NULL,
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  assigned_at timestamptz DEFAULT now(),
  converted boolean DEFAULT false,
  converted_at timestamptz,
  conversion_value numeric DEFAULT 0
);

ALTER TABLE ab_test_variants ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own test variants"
  ON ab_test_variants
  FOR SELECT
  TO authenticated
  USING (user_id = uid());

CREATE UNIQUE INDEX ab_test_variants_test_user_idx ON ab_test_variants(test_name, user_id);

-- User Segments Table
CREATE TABLE IF NOT EXISTS user_segments (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  segment_name text NOT NULL,
  segment_value text NOT NULL,
  confidence_score numeric DEFAULT 1.0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE user_segments ENABLE ROW LEVEL SECURITY;

CREATE POLICY "System can manage user segments"
  ON user_segments
  FOR ALL
  TO authenticated
  USING (true);

CREATE INDEX user_segments_user_segment_idx ON user_segments(user_id, segment_name);

-- Review Helpfulness Table
CREATE TABLE IF NOT EXISTS review_helpfulness (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  review_id uuid NOT NULL REFERENCES user_reviews(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  is_helpful boolean NOT NULL,
  created_at timestamptz DEFAULT now()
);

ALTER TABLE review_helpfulness ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can rate review helpfulness"
  ON review_helpfulness
  FOR ALL
  TO authenticated
  USING (user_id = uid())
  WITH CHECK (user_id = uid());

CREATE UNIQUE INDEX review_helpfulness_review_user_idx ON review_helpfulness(review_id, user_id);

-- Functions for analytics and automation
CREATE OR REPLACE FUNCTION update_companion_rating_from_reviews()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE companions 
  SET 
    rating = (
      SELECT AVG(overall_rating) 
      FROM user_reviews 
      WHERE companion_id = NEW.companion_id
    ),
    reviews_count = (
      SELECT COUNT(*) 
      FROM user_reviews 
      WHERE companion_id = NEW.companion_id
    ),
    updated_at = now()
  WHERE id = NEW.companion_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_companion_rating_from_reviews_trigger
  AFTER INSERT OR UPDATE ON user_reviews
  FOR EACH ROW
  EXECUTE FUNCTION update_companion_rating_from_reviews();

-- Function to update review helpfulness counts
CREATE OR REPLACE FUNCTION update_review_helpfulness_counts()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE user_reviews 
  SET 
    helpful_count = (
      SELECT COUNT(*) 
      FROM review_helpfulness 
      WHERE review_id = COALESCE(NEW.review_id, OLD.review_id) AND is_helpful = true
    ),
    not_helpful_count = (
      SELECT COUNT(*) 
      FROM review_helpfulness 
      WHERE review_id = COALESCE(NEW.review_id, OLD.review_id) AND is_helpful = false
    ),
    updated_at = now()
  WHERE id = COALESCE(NEW.review_id, OLD.review_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_review_helpfulness_counts_trigger
  AFTER INSERT OR UPDATE OR DELETE ON review_helpfulness
  FOR EACH ROW
  EXECUTE FUNCTION update_review_helpfulness_counts();

-- Function to generate referral codes
CREATE OR REPLACE FUNCTION generate_referral_code()
RETURNS text AS $$
BEGIN
  RETURN upper(substring(md5(random()::text) from 1 for 8));
END;
$$ LANGUAGE plpgsql;

-- Function to track recently viewed
CREATE OR REPLACE FUNCTION track_recently_viewed(p_user_id uuid, p_companion_id uuid)
RETURNS void AS $$
BEGIN
  -- Remove old entry if exists
  DELETE FROM recently_viewed 
  WHERE user_id = p_user_id AND companion_id = p_companion_id;
  
  -- Insert new entry
  INSERT INTO recently_viewed (user_id, companion_id)
  VALUES (p_user_id, p_companion_id);
  
  -- Keep only last 50 viewed items per user
  DELETE FROM recently_viewed 
  WHERE user_id = p_user_id 
  AND id NOT IN (
    SELECT id FROM recently_viewed 
    WHERE user_id = p_user_id 
    ORDER BY viewed_at DESC 
    LIMIT 50
  );
END;
$$ LANGUAGE plpgsql;

-- Create default user preferences for new users
CREATE OR REPLACE FUNCTION create_default_user_preferences()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO user_preferences (user_id)
  VALUES (NEW.id);
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER create_default_user_preferences_trigger
  AFTER INSERT ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION create_default_user_preferences();