/*
  # Payment System Implementation

  1. New Tables
    - payment_methods
      - Stores user payment methods (cards, digital wallets)
    - transactions
      - Records all payment transactions
    - subscription_plans
      - Available subscription plans for users
    - user_subscriptions
      - User subscription status and details
    - companion_earnings
      - Tracks companion earnings and payouts
    - payment_disputes
      - Handles payment disputes and refunds

  2. Security
    - Enable RLS on all payment tables
    - Secure access to financial information
    - Audit trail for all transactions
*/

-- Create subscription_plans table
CREATE TABLE IF NOT EXISTS subscription_plans (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text,
  price_monthly decimal NOT NULL,
  price_yearly decimal,
  features jsonb DEFAULT '[]',
  is_active boolean DEFAULT true,
  revenuecat_product_id text,
  stripe_price_id text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE subscription_plans ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can read active subscription plans"
  ON subscription_plans
  FOR SELECT
  TO authenticated
  USING (is_active = true);

-- Create user_subscriptions table
CREATE TABLE IF NOT EXISTS user_subscriptions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  plan_id uuid REFERENCES subscription_plans(id) NOT NULL,
  status text DEFAULT 'active' CHECK (status IN ('active', 'cancelled', 'expired', 'past_due')),
  current_period_start timestamptz NOT NULL,
  current_period_end timestamptz NOT NULL,
  cancel_at_period_end boolean DEFAULT false,
  revenuecat_customer_id text,
  stripe_subscription_id text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE user_subscriptions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own subscriptions"
  ON user_subscriptions
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Users can update own subscriptions"
  ON user_subscriptions
  FOR UPDATE
  TO authenticated
  USING (user_id = auth.uid());

-- Create payment_methods table
CREATE TABLE IF NOT EXISTS payment_methods (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  type text NOT NULL CHECK (type IN ('card', 'apple_pay', 'google_pay', 'paypal')),
  provider text NOT NULL CHECK (provider IN ('stripe', 'revenuecat', 'apple', 'google')),
  provider_payment_method_id text NOT NULL,
  last_four text,
  brand text,
  exp_month integer,
  exp_year integer,
  is_default boolean DEFAULT false,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE payment_methods ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own payment methods"
  ON payment_methods
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

-- Create transactions table
CREATE TABLE IF NOT EXISTS transactions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  booking_id uuid REFERENCES bookings(id) ON DELETE SET NULL,
  companion_id uuid REFERENCES companions(id) ON DELETE SET NULL,
  type text NOT NULL CHECK (type IN ('booking_payment', 'subscription_payment', 'refund', 'payout', 'fee')),
  status text DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded')),
  amount decimal NOT NULL,
  currency text DEFAULT 'USD',
  fee_amount decimal DEFAULT 0,
  net_amount decimal NOT NULL,
  payment_method_id uuid REFERENCES payment_methods(id),
  provider text NOT NULL CHECK (provider IN ('stripe', 'revenuecat', 'apple', 'google')),
  provider_transaction_id text,
  provider_payment_intent_id text,
  description text,
  metadata jsonb DEFAULT '{}',
  processed_at timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own transactions"
  ON transactions
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid() OR companion_id IN (
    SELECT id FROM companions WHERE profile_id = auth.uid()
  ));

-- Create companion_earnings table
CREATE TABLE IF NOT EXISTS companion_earnings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  companion_id uuid REFERENCES companions(id) ON DELETE CASCADE NOT NULL,
  transaction_id uuid REFERENCES transactions(id) ON DELETE CASCADE NOT NULL,
  booking_id uuid REFERENCES bookings(id) ON DELETE CASCADE NOT NULL,
  gross_amount decimal NOT NULL,
  platform_fee decimal NOT NULL,
  net_amount decimal NOT NULL,
  status text DEFAULT 'pending' CHECK (status IN ('pending', 'available', 'paid', 'on_hold')),
  available_at timestamptz,
  paid_at timestamptz,
  payout_transaction_id uuid REFERENCES transactions(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE companion_earnings ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Companions can read own earnings"
  ON companion_earnings
  FOR SELECT
  TO authenticated
  USING (companion_id IN (SELECT id FROM companions WHERE profile_id = auth.uid()));

-- Create payment_disputes table
CREATE TABLE IF NOT EXISTS payment_disputes (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  transaction_id uuid REFERENCES transactions(id) ON DELETE CASCADE NOT NULL,
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  companion_id uuid REFERENCES companions(id) ON DELETE CASCADE,
  type text NOT NULL CHECK (type IN ('chargeback', 'refund_request', 'dispute')),
  status text DEFAULT 'open' CHECK (status IN ('open', 'under_review', 'resolved', 'closed')),
  reason text NOT NULL,
  description text,
  amount decimal NOT NULL,
  evidence jsonb DEFAULT '{}',
  resolution text,
  resolved_at timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE payment_disputes ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own disputes"
  ON payment_disputes
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid() OR companion_id IN (
    SELECT id FROM companions WHERE profile_id = auth.uid()
  ));

CREATE POLICY "Users can create disputes"
  ON payment_disputes
  FOR INSERT
  TO authenticated
  WITH CHECK (user_id = auth.uid());

-- Add payment fields to bookings table
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'bookings' AND column_name = 'payment_status'
  ) THEN
    ALTER TABLE bookings ADD COLUMN payment_status text DEFAULT 'pending' CHECK (payment_status IN ('pending', 'processing', 'paid', 'failed', 'refunded'));
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'bookings' AND column_name = 'payment_intent_id'
  ) THEN
    ALTER TABLE bookings ADD COLUMN payment_intent_id text;
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'bookings' AND column_name = 'service_fee'
  ) THEN
    ALTER TABLE bookings ADD COLUMN service_fee decimal DEFAULT 0;
  END IF;
END $$;

-- Insert default subscription plans
INSERT INTO subscription_plans (name, description, price_monthly, price_yearly, features, revenuecat_product_id, stripe_price_id) VALUES
('Basic', 'Essential features for casual users', 9.99, 99.99, '["Unlimited messaging", "Basic search filters", "Customer support"]', 'basic_monthly', 'price_basic_monthly'),
('Premium', 'Advanced features for serious daters', 19.99, 199.99, '["All Basic features", "Advanced filters", "Priority support", "Verified badge", "Unlimited bookings"]', 'premium_monthly', 'price_premium_monthly'),
('VIP', 'Exclusive access and premium experience', 39.99, 399.99, '["All Premium features", "VIP support", "Exclusive companions", "Concierge service", "Priority booking"]', 'vip_monthly', 'price_vip_monthly')
ON CONFLICT DO NOTHING;

-- Create function to calculate companion earnings
CREATE OR REPLACE FUNCTION calculate_companion_earnings()
RETURNS TRIGGER AS $$
DECLARE
  platform_fee_rate DECIMAL := 0.20; -- 20% platform fee
  gross_amount DECIMAL;
  fee_amount DECIMAL;
  net_amount DECIMAL;
BEGIN
  IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
    gross_amount := NEW.total_amount - COALESCE(NEW.service_fee, 0);
    fee_amount := gross_amount * platform_fee_rate;
    net_amount := gross_amount - fee_amount;
    
    INSERT INTO companion_earnings (
      companion_id,
      booking_id,
      gross_amount,
      platform_fee,
      net_amount,
      status,
      available_at
    ) VALUES (
      NEW.companion_id,
      NEW.id,
      gross_amount,
      fee_amount,
      net_amount,
      'pending',
      NOW() + INTERVAL '7 days' -- Available for payout after 7 days
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER calculate_companion_earnings_trigger
AFTER UPDATE ON bookings
FOR EACH ROW
EXECUTE FUNCTION calculate_companion_earnings();

-- Create function to update payment method default status
CREATE OR REPLACE FUNCTION update_default_payment_method()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.is_default = true THEN
    -- Set all other payment methods for this user to not default
    UPDATE payment_methods
    SET is_default = false
    WHERE user_id = NEW.user_id AND id != NEW.id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_default_payment_method_trigger
BEFORE INSERT OR UPDATE ON payment_methods
FOR EACH ROW
EXECUTE FUNCTION update_default_payment_method();