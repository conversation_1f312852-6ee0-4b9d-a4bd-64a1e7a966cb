/*
  # Advanced Safety, Mobile Features & Content Management

  1. New Tables
    - verification_requests
      - Enhanced identity verification process
      - Multi-step verification workflow
    - identity_documents
      - Secure document storage and verification
      - Multiple document types support
    - background_checks
      - Integration with background check services
      - Comprehensive safety screening
    - safety_reports
      - Incident reporting and tracking
      - Safety violation management
    - emergency_contacts
      - Multiple emergency contact management
      - Relationship-based contact organization
    - emergency_incidents
      - SOS incidents and emergency tracking
      - Real-time emergency response
    - safety_education
      - In-app safety tips and education
      - Interactive safety content
    - app_shortcuts
      - Mobile app shortcuts and quick actions
      - Personalized shortcut management
    - deep_links
      - Deep linking for sharing profiles
      - Analytics tracking for shared links
    - content_articles
      - Blog/news content management
      - Rich content with media support
    - faq_items
      - Comprehensive FAQ system
      - Categorized help documentation
    - legal_documents
      - Terms, policies, and legal content
      - Version control and acceptance tracking
    - promotional_codes
      - Discount and promo code system
      - Usage tracking and limitations
    - user_wallets
      - In-app credit/balance management
      - Transaction history and top-ups
    - expense_tracking
      - User spending analytics
      - Budget management and insights
    - companion_availability
      - Detailed availability calendar
      - Time slot management
    - companion_earnings
      - Earnings tracking and analytics
      - Payout management system
    - payout_requests
      - Withdrawal and payout processing
      - Banking information management

  2. Security
    - Enable RLS on all tables
    - Role-based access control
    - Secure financial data handling
    - Privacy controls for safety data

  3. Functions
    - Auto-process verification requests
    - Calculate safety scores
    - Manage promotional code usage
    - Process payout requests
    - Track user engagement
*/

-- Enhanced Verification System
CREATE TABLE IF NOT EXISTS verification_requests (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  companion_id uuid REFERENCES companions(id) ON DELETE CASCADE NOT NULL,
  status text DEFAULT 'pending' CHECK (status IN ('pending', 'in_review', 'approved', 'rejected', 'requires_resubmission')),
  verification_level text DEFAULT 'basic' CHECK (verification_level IN ('basic', 'enhanced', 'premium')),
  submission_date timestamptz DEFAULT now(),
  review_date timestamptz,
  reviewer_id uuid REFERENCES profiles(id),
  reviewer_notes text,
  rejection_reason text,
  verification_score integer DEFAULT 0,
  documents_required text[] DEFAULT ARRAY['government_id', 'selfie_with_id'],
  documents_submitted text[] DEFAULT '{}',
  background_check_status text DEFAULT 'pending' CHECK (background_check_status IN ('pending', 'in_progress', 'completed', 'failed')),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE verification_requests ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Companions can manage own verification"
  ON verification_requests
  FOR ALL
  TO authenticated
  USING (companion_id IN (SELECT id FROM companions WHERE profile_id = auth.uid()));

-- Identity Documents
CREATE TABLE IF NOT EXISTS identity_documents (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  verification_request_id uuid REFERENCES verification_requests(id) ON DELETE CASCADE NOT NULL,
  document_type text NOT NULL CHECK (document_type IN ('government_id', 'passport', 'drivers_license', 'selfie_with_id', 'proof_of_address', 'professional_license')),
  file_url text NOT NULL,
  file_name text NOT NULL,
  file_size integer,
  mime_type text,
  verification_status text DEFAULT 'pending' CHECK (verification_status IN ('pending', 'verified', 'rejected')),
  verification_notes text,
  ai_analysis_result jsonb DEFAULT '{}',
  manual_review_required boolean DEFAULT false,
  uploaded_at timestamptz DEFAULT now(),
  verified_at timestamptz,
  verified_by uuid REFERENCES profiles(id)
);

ALTER TABLE identity_documents ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Companions can manage own documents"
  ON identity_documents
  FOR ALL
  TO authenticated
  USING (verification_request_id IN (
    SELECT id FROM verification_requests 
    WHERE companion_id IN (SELECT id FROM companions WHERE profile_id = auth.uid())
  ));

-- Background Checks
CREATE TABLE IF NOT EXISTS background_checks (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  verification_request_id uuid REFERENCES verification_requests(id) ON DELETE CASCADE NOT NULL,
  check_type text NOT NULL CHECK (check_type IN ('criminal_history', 'identity_verification', 'address_verification', 'employment_verification', 'education_verification')),
  provider text NOT NULL, -- 'checkr', 'sterling', 'accurate', etc.
  provider_reference text,
  status text DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'failed', 'cancelled')),
  result text CHECK (result IN ('pass', 'fail', 'review_required', 'consider')),
  details jsonb DEFAULT '{}',
  cost_amount decimal DEFAULT 0,
  initiated_at timestamptz DEFAULT now(),
  completed_at timestamptz,
  expires_at timestamptz
);

ALTER TABLE background_checks ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Companions can read own background checks"
  ON background_checks
  FOR SELECT
  TO authenticated
  USING (verification_request_id IN (
    SELECT id FROM verification_requests 
    WHERE companion_id IN (SELECT id FROM companions WHERE profile_id = auth.uid())
  ));

-- Safety Reports
CREATE TABLE IF NOT EXISTS safety_reports (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  reporter_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  reported_user_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
  booking_id uuid REFERENCES bookings(id) ON DELETE SET NULL,
  report_type text NOT NULL CHECK (report_type IN ('harassment', 'inappropriate_behavior', 'safety_concern', 'fake_profile', 'payment_issue', 'other')),
  severity text DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  description text NOT NULL,
  evidence_urls text[] DEFAULT '{}',
  location_data jsonb,
  status text DEFAULT 'open' CHECK (status IN ('open', 'investigating', 'resolved', 'closed', 'escalated')),
  assigned_to uuid REFERENCES profiles(id),
  resolution_notes text,
  actions_taken text,
  resolved_at timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE safety_reports ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can create safety reports"
  ON safety_reports
  FOR INSERT
  TO authenticated
  WITH CHECK (reporter_id = auth.uid());

CREATE POLICY "Users can read own reports"
  ON safety_reports
  FOR SELECT
  TO authenticated
  USING (reporter_id = auth.uid() OR reported_user_id = auth.uid());

-- Safety Education Content
CREATE TABLE IF NOT EXISTS safety_education (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  content text NOT NULL,
  content_type text DEFAULT 'article' CHECK (content_type IN ('article', 'video', 'infographic', 'checklist')),
  category text NOT NULL CHECK (category IN ('meeting_safety', 'online_safety', 'emergency_procedures', 'red_flags', 'best_practices')),
  target_audience text DEFAULT 'all' CHECK (target_audience IN ('all', 'users', 'companions', 'new_users')),
  media_url text,
  estimated_read_time integer DEFAULT 5,
  is_mandatory boolean DEFAULT false,
  view_count integer DEFAULT 0,
  completion_rate decimal DEFAULT 0,
  is_published boolean DEFAULT true,
  published_at timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE safety_education ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can read published safety content"
  ON safety_education
  FOR SELECT
  TO authenticated
  USING (is_published = true);

-- App Shortcuts for Mobile
CREATE TABLE IF NOT EXISTS app_shortcuts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  shortcut_type text NOT NULL CHECK (shortcut_type IN ('quick_book', 'emergency_contact', 'favorite_companion', 'recent_chat', 'custom')),
  title text NOT NULL,
  subtitle text,
  icon_name text,
  action_data jsonb NOT NULL,
  is_active boolean DEFAULT true,
  usage_count integer DEFAULT 0,
  last_used_at timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE app_shortcuts ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own shortcuts"
  ON app_shortcuts
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

-- Deep Links for Sharing
CREATE TABLE IF NOT EXISTS deep_links (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  link_type text NOT NULL CHECK (link_type IN ('companion_profile', 'booking_invite', 'referral', 'group_booking')),
  target_id uuid NOT NULL,
  short_code text UNIQUE NOT NULL,
  created_by uuid REFERENCES profiles(id) ON DELETE CASCADE,
  click_count integer DEFAULT 0,
  unique_clicks integer DEFAULT 0,
  conversion_count integer DEFAULT 0,
  expires_at timestamptz,
  metadata jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now()
);

ALTER TABLE deep_links ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can read active deep links"
  ON deep_links
  FOR SELECT
  TO authenticated
  USING (expires_at IS NULL OR expires_at > now());

CREATE POLICY "Users can create deep links"
  ON deep_links
  FOR INSERT
  TO authenticated
  WITH CHECK (created_by = auth.uid());

-- Content Articles (Blog/News)
CREATE TABLE IF NOT EXISTS content_articles (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  slug text UNIQUE NOT NULL,
  excerpt text,
  content text NOT NULL,
  featured_image_url text,
  category text NOT NULL CHECK (category IN ('dating_tips', 'safety', 'company_news', 'success_stories', 'guides')),
  tags text[] DEFAULT '{}',
  author_id uuid REFERENCES profiles(id),
  is_published boolean DEFAULT false,
  is_featured boolean DEFAULT false,
  view_count integer DEFAULT 0,
  like_count integer DEFAULT 0,
  share_count integer DEFAULT 0,
  estimated_read_time integer DEFAULT 5,
  seo_title text,
  seo_description text,
  published_at timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE content_articles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can read published articles"
  ON content_articles
  FOR SELECT
  TO authenticated
  USING (is_published = true);

-- FAQ System
CREATE TABLE IF NOT EXISTS faq_items (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  question text NOT NULL,
  answer text NOT NULL,
  category text NOT NULL CHECK (category IN ('getting_started', 'booking', 'payments', 'safety', 'account', 'technical', 'companions')),
  subcategory text,
  tags text[] DEFAULT '{}',
  is_featured boolean DEFAULT false,
  view_count integer DEFAULT 0,
  helpful_count integer DEFAULT 0,
  not_helpful_count integer DEFAULT 0,
  search_keywords text,
  display_order integer DEFAULT 0,
  is_published boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE faq_items ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can read published FAQ items"
  ON faq_items
  FOR SELECT
  TO authenticated
  USING (is_published = true);

-- Legal Documents
CREATE TABLE IF NOT EXISTS legal_documents (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  document_type text NOT NULL CHECK (document_type IN ('terms_of_service', 'privacy_policy', 'community_guidelines', 'cancellation_policy', 'safety_guidelines')),
  title text NOT NULL,
  content text NOT NULL,
  version text NOT NULL,
  is_current boolean DEFAULT false,
  effective_date timestamptz NOT NULL,
  created_by uuid REFERENCES profiles(id),
  requires_acceptance boolean DEFAULT true,
  created_at timestamptz DEFAULT now()
);

ALTER TABLE legal_documents ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can read current legal documents"
  ON legal_documents
  FOR SELECT
  TO authenticated
  USING (is_current = true);

-- User Legal Acceptances
CREATE TABLE IF NOT EXISTS user_legal_acceptances (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  document_id uuid REFERENCES legal_documents(id) ON DELETE CASCADE NOT NULL,
  accepted_at timestamptz DEFAULT now(),
  ip_address text,
  user_agent text,
  UNIQUE(user_id, document_id)
);

ALTER TABLE user_legal_acceptances ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own legal acceptances"
  ON user_legal_acceptances
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

-- Promotional Codes
CREATE TABLE IF NOT EXISTS promotional_codes (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  code text UNIQUE NOT NULL,
  name text NOT NULL,
  description text,
  discount_type text NOT NULL CHECK (discount_type IN ('percentage', 'fixed_amount', 'free_booking')),
  discount_value decimal NOT NULL,
  minimum_order_amount decimal DEFAULT 0,
  maximum_discount_amount decimal,
  usage_limit integer,
  usage_count integer DEFAULT 0,
  user_usage_limit integer DEFAULT 1,
  applicable_to text DEFAULT 'all' CHECK (applicable_to IN ('all', 'first_booking', 'subscriptions', 'specific_companions')),
  companion_ids uuid[] DEFAULT '{}',
  is_active boolean DEFAULT true,
  starts_at timestamptz DEFAULT now(),
  expires_at timestamptz,
  created_by uuid REFERENCES profiles(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE promotional_codes ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can read active promo codes"
  ON promotional_codes
  FOR SELECT
  TO authenticated
  USING (is_active = true AND (starts_at IS NULL OR starts_at <= now()) AND (expires_at IS NULL OR expires_at > now()));

-- Promo Code Usage Tracking
CREATE TABLE IF NOT EXISTS promo_code_usage (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  promo_code_id uuid REFERENCES promotional_codes(id) ON DELETE CASCADE NOT NULL,
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  booking_id uuid REFERENCES bookings(id) ON DELETE SET NULL,
  discount_amount decimal NOT NULL,
  used_at timestamptz DEFAULT now(),
  UNIQUE(promo_code_id, user_id, booking_id)
);

ALTER TABLE promo_code_usage ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own promo usage"
  ON promo_code_usage
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

-- User Wallets
CREATE TABLE IF NOT EXISTS user_wallets (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL UNIQUE,
  balance decimal DEFAULT 0 CHECK (balance >= 0),
  pending_balance decimal DEFAULT 0,
  total_earned decimal DEFAULT 0,
  total_spent decimal DEFAULT 0,
  currency text DEFAULT 'USD',
  last_transaction_at timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE user_wallets ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own wallet"
  ON user_wallets
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

-- Wallet Transactions
CREATE TABLE IF NOT EXISTS wallet_transactions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  wallet_id uuid REFERENCES user_wallets(id) ON DELETE CASCADE NOT NULL,
  transaction_type text NOT NULL CHECK (transaction_type IN ('credit', 'debit', 'refund', 'bonus', 'referral_reward')),
  amount decimal NOT NULL,
  balance_after decimal NOT NULL,
  description text NOT NULL,
  reference_id uuid, -- booking_id, referral_id, etc.
  reference_type text,
  metadata jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now()
);

ALTER TABLE wallet_transactions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own wallet transactions"
  ON wallet_transactions
  FOR SELECT
  TO authenticated
  USING (wallet_id IN (SELECT id FROM user_wallets WHERE user_id = auth.uid()));

-- Expense Tracking
CREATE TABLE IF NOT EXISTS expense_tracking (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  month_year text NOT NULL, -- 'YYYY-MM' format
  total_spent decimal DEFAULT 0,
  booking_count integer DEFAULT 0,
  average_booking_cost decimal DEFAULT 0,
  most_expensive_booking decimal DEFAULT 0,
  favorite_companion_id uuid REFERENCES companions(id),
  spending_category_breakdown jsonb DEFAULT '{}',
  budget_limit decimal,
  budget_alerts_enabled boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(user_id, month_year)
);

ALTER TABLE expense_tracking ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own expense tracking"
  ON expense_tracking
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

-- Companion Availability Calendar
CREATE TABLE IF NOT EXISTS companion_availability (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  companion_id uuid REFERENCES companions(id) ON DELETE CASCADE NOT NULL,
  date date NOT NULL,
  time_slots jsonb NOT NULL, -- Array of time slots with availability
  is_available boolean DEFAULT true,
  special_pricing jsonb DEFAULT '{}', -- Special pricing for specific times
  notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(companion_id, date)
);

ALTER TABLE companion_availability ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can read companion availability"
  ON companion_availability
  FOR SELECT
  TO authenticated
  USING (is_available = true);

CREATE POLICY "Companions can manage own availability"
  ON companion_availability
  FOR ALL
  TO authenticated
  USING (companion_id IN (SELECT id FROM companions WHERE profile_id = auth.uid()));

-- Companion Earnings (Enhanced)
CREATE TABLE IF NOT EXISTS companion_earnings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  companion_id uuid REFERENCES companions(id) ON DELETE CASCADE NOT NULL,
  booking_id uuid REFERENCES bookings(id) ON DELETE CASCADE NOT NULL,
  gross_amount decimal NOT NULL,
  platform_fee_percentage decimal DEFAULT 20,
  platform_fee_amount decimal NOT NULL,
  net_amount decimal NOT NULL,
  status text DEFAULT 'pending' CHECK (status IN ('pending', 'available', 'paid', 'on_hold', 'disputed')),
  available_at timestamptz,
  paid_at timestamptz,
  payout_method text CHECK (payout_method IN ('bank_transfer', 'paypal', 'stripe', 'check')),
  payout_reference text,
  tax_withheld decimal DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE companion_earnings ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Companions can read own earnings"
  ON companion_earnings
  FOR SELECT
  TO authenticated
  USING (companion_id IN (SELECT id FROM companions WHERE profile_id = auth.uid()));

-- Payout Requests
CREATE TABLE IF NOT EXISTS payout_requests (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  companion_id uuid REFERENCES companions(id) ON DELETE CASCADE NOT NULL,
  amount decimal NOT NULL,
  payout_method text NOT NULL CHECK (payout_method IN ('bank_transfer', 'paypal', 'stripe', 'check')),
  bank_account_info jsonb,
  paypal_email text,
  status text DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
  processing_fee decimal DEFAULT 0,
  net_amount decimal NOT NULL,
  processed_at timestamptz,
  processed_by uuid REFERENCES profiles(id),
  failure_reason text,
  reference_number text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE payout_requests ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Companions can manage own payout requests"
  ON payout_requests
  FOR ALL
  TO authenticated
  USING (companion_id IN (SELECT id FROM companions WHERE profile_id = auth.uid()));

-- Biometric Authentication Settings
CREATE TABLE IF NOT EXISTS biometric_settings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL UNIQUE,
  biometric_enabled boolean DEFAULT false,
  biometric_type text CHECK (biometric_type IN ('face_id', 'touch_id', 'fingerprint', 'voice')),
  device_id text,
  last_used_at timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE biometric_settings ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own biometric settings"
  ON biometric_settings
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

-- Multi-language Support
CREATE TABLE IF NOT EXISTS app_translations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  language_code text NOT NULL,
  translation_key text NOT NULL,
  translation_value text NOT NULL,
  context text,
  is_approved boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(language_code, translation_key)
);

ALTER TABLE app_translations ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can read approved translations"
  ON app_translations
  FOR SELECT
  TO authenticated
  USING (is_approved = true);

-- Functions for automation and business logic

-- Function to calculate safety score
CREATE OR REPLACE FUNCTION calculate_companion_safety_score(companion_id_param uuid)
RETURNS integer AS $$
DECLARE
  score integer := 0;
  verification_count integer;
  background_check_count integer;
  safety_report_count integer;
BEGIN
  -- Base score
  score := 50;
  
  -- Add points for verifications
  SELECT COUNT(*) INTO verification_count
  FROM verification_badges
  WHERE companion_id = companion_id_param;
  score := score + (verification_count * 10);
  
  -- Add points for background checks
  SELECT COUNT(*) INTO background_check_count
  FROM background_checks bc
  JOIN verification_requests vr ON bc.verification_request_id = vr.id
  WHERE vr.companion_id = companion_id_param AND bc.result = 'pass';
  score := score + (background_check_count * 15);
  
  -- Subtract points for safety reports
  SELECT COUNT(*) INTO safety_report_count
  FROM safety_reports
  WHERE reported_user_id IN (SELECT profile_id FROM companions WHERE id = companion_id_param)
    AND status IN ('open', 'investigating');
  score := score - (safety_report_count * 20);
  
  -- Ensure score is between 0 and 100
  score := GREATEST(0, LEAST(100, score));
  
  RETURN score;
END;
$$ LANGUAGE plpgsql;

-- Function to process promo code usage
CREATE OR REPLACE FUNCTION apply_promotional_code(
  code_param text,
  user_id_param uuid,
  booking_amount decimal
) RETURNS jsonb AS $$
DECLARE
  promo_record RECORD;
  discount_amount decimal;
  usage_count_user integer;
  result jsonb;
BEGIN
  -- Get promo code details
  SELECT * INTO promo_record
  FROM promotional_codes
  WHERE code = code_param
    AND is_active = true
    AND (starts_at IS NULL OR starts_at <= now())
    AND (expires_at IS NULL OR expires_at > now())
    AND (usage_limit IS NULL OR usage_count < usage_limit);
    
  IF NOT FOUND THEN
    RETURN jsonb_build_object('valid', false, 'error', 'Invalid or expired promotional code');
  END IF;
  
  -- Check user usage limit
  SELECT COUNT(*) INTO usage_count_user
  FROM promo_code_usage
  WHERE promo_code_id = promo_record.id AND user_id = user_id_param;
  
  IF usage_count_user >= promo_record.user_usage_limit THEN
    RETURN jsonb_build_object('valid', false, 'error', 'Promotional code usage limit exceeded');
  END IF;
  
  -- Check minimum order amount
  IF booking_amount < promo_record.minimum_order_amount THEN
    RETURN jsonb_build_object('valid', false, 'error', 'Minimum order amount not met');
  END IF;
  
  -- Calculate discount
  IF promo_record.discount_type = 'percentage' THEN
    discount_amount := booking_amount * (promo_record.discount_value / 100);
    IF promo_record.maximum_discount_amount IS NOT NULL THEN
      discount_amount := LEAST(discount_amount, promo_record.maximum_discount_amount);
    END IF;
  ELSIF promo_record.discount_type = 'fixed_amount' THEN
    discount_amount := promo_record.discount_value;
  ELSE
    discount_amount := booking_amount; -- free_booking
  END IF;
  
  -- Ensure discount doesn't exceed booking amount
  discount_amount := LEAST(discount_amount, booking_amount);
  
  RETURN jsonb_build_object(
    'valid', true,
    'discount_amount', discount_amount,
    'final_amount', booking_amount - discount_amount,
    'promo_id', promo_record.id
  );
END;
$$ LANGUAGE plpgsql;

-- Function to update wallet balance
CREATE OR REPLACE FUNCTION update_wallet_balance()
RETURNS TRIGGER AS $$
DECLARE
  wallet_record RECORD;
  new_balance decimal;
BEGIN
  -- Get current wallet
  SELECT * INTO wallet_record
  FROM user_wallets
  WHERE id = NEW.wallet_id;
  
  -- Calculate new balance
  IF NEW.transaction_type IN ('credit', 'refund', 'bonus', 'referral_reward') THEN
    new_balance := wallet_record.balance + NEW.amount;
  ELSE
    new_balance := wallet_record.balance - NEW.amount;
  END IF;
  
  -- Update wallet
  UPDATE user_wallets
  SET 
    balance = new_balance,
    last_transaction_at = now(),
    updated_at = now()
  WHERE id = NEW.wallet_id;
  
  -- Update balance_after in transaction
  NEW.balance_after := new_balance;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_wallet_balance_trigger
  BEFORE INSERT ON wallet_transactions
  FOR EACH ROW
  EXECUTE FUNCTION update_wallet_balance();

-- Function to auto-create user wallet
CREATE OR REPLACE FUNCTION create_user_wallet()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO user_wallets (user_id)
  VALUES (NEW.id)
  ON CONFLICT (user_id) DO NOTHING;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER create_user_wallet_trigger
  AFTER INSERT ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION create_user_wallet();

-- Function to update expense tracking
CREATE OR REPLACE FUNCTION update_expense_tracking()
RETURNS TRIGGER AS $$
DECLARE
  month_year_str text;
  expense_record RECORD;
BEGIN
  IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
    month_year_str := to_char(NEW.booking_time, 'YYYY-MM');
    
    -- Get or create expense tracking record
    INSERT INTO expense_tracking (user_id, month_year, total_spent, booking_count)
    VALUES (NEW.user_id, month_year_str, NEW.total_amount, 1)
    ON CONFLICT (user_id, month_year)
    DO UPDATE SET
      total_spent = expense_tracking.total_spent + NEW.total_amount,
      booking_count = expense_tracking.booking_count + 1,
      average_booking_cost = (expense_tracking.total_spent + NEW.total_amount) / (expense_tracking.booking_count + 1),
      most_expensive_booking = GREATEST(expense_tracking.most_expensive_booking, NEW.total_amount),
      updated_at = now();
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_expense_tracking_trigger
  AFTER UPDATE ON bookings
  FOR EACH ROW
  EXECUTE FUNCTION update_expense_tracking();

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS verification_requests_companion_status_idx ON verification_requests (companion_id, status);
CREATE INDEX IF NOT EXISTS safety_reports_status_severity_idx ON safety_reports (status, severity, created_at DESC);
CREATE INDEX IF NOT EXISTS promotional_codes_active_expires_idx ON promotional_codes (is_active, expires_at);
CREATE INDEX IF NOT EXISTS wallet_transactions_wallet_date_idx ON wallet_transactions (wallet_id, created_at DESC);
CREATE INDEX IF NOT EXISTS companion_availability_date_idx ON companion_availability (companion_id, date);
CREATE INDEX IF NOT EXISTS companion_earnings_status_available_idx ON companion_earnings (companion_id, status, available_at);
CREATE INDEX IF NOT EXISTS content_articles_published_featured_idx ON content_articles (is_published, is_featured, published_at DESC);
CREATE INDEX IF NOT EXISTS faq_items_category_order_idx ON faq_items (category, display_order);

-- Insert sample data
INSERT INTO promotional_codes (code, name, description, discount_type, discount_value, usage_limit, expires_at) VALUES
('WELCOME25', 'Welcome Bonus', '25% off your first booking', 'percentage', 25, 1000, now() + interval '30 days'),
('FIRST50', 'First Booking Special', '$50 off your first booking', 'fixed_amount', 50, 500, now() + interval '60 days'),
('FRIEND20', 'Friend Referral', '20% off for referred friends', 'percentage', 20, NULL, now() + interval '90 days')
ON CONFLICT DO NOTHING;

INSERT INTO safety_education (title, content, category, target_audience) VALUES
('Meeting Safety Guidelines', 'Essential safety tips for meeting companions...', 'meeting_safety', 'all'),
('Online Safety Best Practices', 'How to stay safe while using dating platforms...', 'online_safety', 'all'),
('Emergency Procedures', 'What to do in case of emergency during a booking...', 'emergency_procedures', 'all'),
('Recognizing Red Flags', 'Warning signs to watch out for...', 'red_flags', 'all')
ON CONFLICT DO NOTHING;

INSERT INTO faq_items (question, answer, category, is_featured) VALUES
('How do I book a companion?', 'To book a companion, browse our verified profiles, select your preferred companion, choose your date and time, and complete the payment process.', 'booking', true),
('What payment methods do you accept?', 'We accept all major credit cards, PayPal, Apple Pay, and Google Pay. You can also use wallet credits.', 'payments', true),
('How does the verification process work?', 'Our verification process includes identity verification, background checks, and photo verification to ensure safety and authenticity.', 'safety', true),
('Can I cancel or modify my booking?', 'Yes, you can cancel or modify bookings through the app. Cancellation fees may apply based on timing and our cancellation policy.', 'booking', true)
ON CONFLICT DO NOTHING;