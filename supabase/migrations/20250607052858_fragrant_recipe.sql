/*
  # Add Verification System

  1. New Tables
    - `verification_requests`
      - Stores verification requests from companions
      - Tracks verification status and documents
    - `background_checks`
      - Stores background check results
      - Links to verification requests
    - `identity_documents`
      - Stores uploaded identity documents
      - Secure document management

  2. Security
    - Enable RLS on all new tables
    - Add policies for secure access
    - Ensure only authorized users can access verification data

  3. Functions
    - Auto-update companion verification status
    - Handle verification workflow
*/

-- Create verification_requests table
CREATE TABLE IF NOT EXISTS verification_requests (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  companion_id uuid REFERENCES companions(id) ON DELETE CASCADE NOT NULL,
  status text DEFAULT 'pending' CHECK (status IN ('pending', 'in_review', 'approved', 'rejected', 'requires_resubmission')),
  submission_date timestamptz DEFAULT now(),
  review_date timestamptz,
  reviewer_notes text,
  rejection_reason text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE verification_requests ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Companions can read own verification requests"
  ON verification_requests
  FOR SELECT
  TO authenticated
  USING (companion_id IN (SELECT id FROM companions WHERE profile_id = auth.uid()));

CREATE POLICY "Companions can create verification requests"
  ON verification_requests
  FOR INSERT
  TO authenticated
  WITH CHECK (companion_id IN (SELECT id FROM companions WHERE profile_id = auth.uid()));

-- Create identity_documents table
CREATE TABLE IF NOT EXISTS identity_documents (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  verification_request_id uuid REFERENCES verification_requests(id) ON DELETE CASCADE NOT NULL,
  document_type text NOT NULL CHECK (document_type IN ('government_id', 'passport', 'drivers_license', 'selfie_with_id', 'proof_of_address')),
  file_url text NOT NULL,
  file_name text NOT NULL,
  file_size integer,
  mime_type text,
  verification_status text DEFAULT 'pending' CHECK (verification_status IN ('pending', 'verified', 'rejected')),
  uploaded_at timestamptz DEFAULT now(),
  verified_at timestamptz
);

ALTER TABLE identity_documents ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Companions can read own documents"
  ON identity_documents
  FOR SELECT
  TO authenticated
  USING (verification_request_id IN (
    SELECT id FROM verification_requests 
    WHERE companion_id IN (SELECT id FROM companions WHERE profile_id = auth.uid())
  ));

CREATE POLICY "Companions can upload documents"
  ON identity_documents
  FOR INSERT
  TO authenticated
  WITH CHECK (verification_request_id IN (
    SELECT id FROM verification_requests 
    WHERE companion_id IN (SELECT id FROM companions WHERE profile_id = auth.uid())
  ));

-- Create background_checks table
CREATE TABLE IF NOT EXISTS background_checks (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  verification_request_id uuid REFERENCES verification_requests(id) ON DELETE CASCADE NOT NULL,
  check_type text NOT NULL CHECK (check_type IN ('criminal_history', 'identity_verification', 'address_verification', 'employment_verification')),
  status text DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'failed')),
  result text CHECK (result IN ('pass', 'fail', 'review_required')),
  provider text, -- Third-party service provider
  provider_reference text, -- Reference ID from provider
  details jsonb, -- Detailed results from provider
  completed_at timestamptz,
  created_at timestamptz DEFAULT now()
);

ALTER TABLE background_checks ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Companions can read own background checks"
  ON background_checks
  FOR SELECT
  TO authenticated
  USING (verification_request_id IN (
    SELECT id FROM verification_requests 
    WHERE companion_id IN (SELECT id FROM companions WHERE profile_id = auth.uid())
  ));

-- Add verification fields to companions table
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'companions' AND column_name = 'verification_status'
  ) THEN
    ALTER TABLE companions ADD COLUMN verification_status text DEFAULT 'unverified' CHECK (verification_status IN ('unverified', 'pending', 'verified', 'rejected'));
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'companions' AND column_name = 'verification_level'
  ) THEN
    ALTER TABLE companions ADD COLUMN verification_level text DEFAULT 'none' CHECK (verification_level IN ('none', 'basic', 'enhanced', 'premium'));
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'companions' AND column_name = 'verified_at'
  ) THEN
    ALTER TABLE companions ADD COLUMN verified_at timestamptz;
  END IF;
END $$;

-- Create function to update companion verification status
CREATE OR REPLACE FUNCTION update_companion_verification_status()
RETURNS TRIGGER AS $$
BEGIN
  -- Update companion verification status based on verification request
  IF NEW.status = 'approved' THEN
    UPDATE companions
    SET 
      verification_status = 'verified',
      is_verified = true,
      verified_at = now(),
      updated_at = now()
    WHERE id = NEW.companion_id;
  ELSIF NEW.status = 'rejected' THEN
    UPDATE companions
    SET 
      verification_status = 'rejected',
      is_verified = false,
      verified_at = null,
      updated_at = now()
    WHERE id = NEW.companion_id;
  ELSIF NEW.status = 'in_review' THEN
    UPDATE companions
    SET 
      verification_status = 'pending',
      updated_at = now()
    WHERE id = NEW.companion_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_companion_verification_trigger
AFTER UPDATE ON verification_requests
FOR EACH ROW
EXECUTE FUNCTION update_companion_verification_status();

-- Create verification badges table for different verification levels
CREATE TABLE IF NOT EXISTS verification_badges (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  companion_id uuid REFERENCES companions(id) ON DELETE CASCADE NOT NULL,
  badge_type text NOT NULL CHECK (badge_type IN ('identity_verified', 'background_checked', 'photo_verified', 'phone_verified', 'email_verified')),
  verified_at timestamptz DEFAULT now(),
  expires_at timestamptz,
  verification_data jsonb,
  created_at timestamptz DEFAULT now()
);

ALTER TABLE verification_badges ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can read verification badges"
  ON verification_badges
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "System can manage verification badges"
  ON verification_badges
  FOR ALL
  TO authenticated
  USING (companion_id IN (SELECT id FROM companions WHERE profile_id = auth.uid()));