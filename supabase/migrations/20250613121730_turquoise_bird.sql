/*
  # Location Services and Emergency Assistance System

  1. New Tables
    - user_locations
      - Real-time location tracking for users and companions
      - Privacy controls and location sharing preferences
    - emergency_contacts
      - User emergency contact information
      - Multiple contacts with relationship types
    - emergency_incidents
      - SOS incidents and emergency reports
      - Real-time tracking and response coordination
    - location_history
      - Historical location data for safety and analytics
      - Geofencing and movement tracking
    - safety_zones
      - Predefined safe areas and danger zones
      - Automatic alerts and notifications
    - proximity_alerts
      - Location-based notifications and matching
      - Distance-based companion suggestions

  2. Security
    - Enable RLS on all location tables
    - Privacy-first location sharing
    - Secure emergency data handling

  3. Functions
    - Real-time location updates
    - Proximity calculations using haversine formula
    - Emergency alert system
    - Geofencing triggers
*/

-- Create user_locations table for real-time location tracking
CREATE TABLE IF NOT EXISTS user_locations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  latitude decimal(10, 8) NOT NULL,
  longitude decimal(11, 8) NOT NULL,
  accuracy decimal,
  altitude decimal,
  heading decimal,
  speed decimal,
  address text,
  city text,
  state text,
  country text,
  postal_code text,
  is_sharing_location boolean DEFAULT false,
  sharing_with text[] DEFAULT '{}', -- Array of user IDs who can see this location
  location_type text DEFAULT 'current' CHECK (location_type IN ('current', 'home', 'work', 'favorite')),
  is_emergency boolean DEFAULT false,
  battery_level integer,
  network_type text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Add indexes for efficient proximity queries
CREATE INDEX IF NOT EXISTS user_locations_lat_lon_idx ON user_locations (latitude, longitude);
CREATE INDEX IF NOT EXISTS user_locations_user_current_idx ON user_locations (user_id, created_at DESC) 
WHERE location_type = 'current';

ALTER TABLE user_locations ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own location"
  ON user_locations
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Users can read shared locations"
  ON user_locations
  FOR SELECT
  TO authenticated
  USING (
    user_id = auth.uid() 
    OR (is_sharing_location = true AND auth.uid()::text = ANY(sharing_with))
    OR is_emergency = true
  );

-- Create emergency_contacts table
CREATE TABLE IF NOT EXISTS emergency_contacts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  name text NOT NULL,
  phone_number text NOT NULL,
  email text,
  relationship text NOT NULL CHECK (relationship IN ('family', 'friend', 'partner', 'colleague', 'medical', 'legal', 'other')),
  is_primary boolean DEFAULT false,
  is_medical boolean DEFAULT false,
  notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE emergency_contacts ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own emergency contacts"
  ON emergency_contacts
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

-- Create emergency_incidents table
CREATE TABLE IF NOT EXISTS emergency_incidents (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  incident_type text NOT NULL CHECK (incident_type IN ('sos', 'medical', 'safety', 'harassment', 'theft', 'accident', 'other')),
  status text DEFAULT 'active' CHECK (status IN ('active', 'resolved', 'false_alarm', 'escalated')),
  priority text DEFAULT 'high' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
  location_id uuid REFERENCES user_locations(id),
  latitude decimal(10, 8),
  longitude decimal(11, 8),
  address text,
  description text,
  audio_recording_url text,
  video_recording_url text,
  photos jsonb DEFAULT '[]',
  contacts_notified text[] DEFAULT '{}',
  authorities_contacted boolean DEFAULT false,
  response_time_seconds integer,
  resolved_at timestamptz,
  resolved_by uuid REFERENCES profiles(id),
  resolution_notes text,
  metadata jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE emergency_incidents ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own incidents"
  ON emergency_incidents
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Support agents can read all incidents"
  ON emergency_incidents
  FOR SELECT
  TO authenticated
  USING (EXISTS (SELECT 1 FROM support_agents WHERE profile_id = auth.uid()));

-- Create location_history table for tracking and analytics
CREATE TABLE IF NOT EXISTS location_history (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  latitude decimal(10, 8) NOT NULL,
  longitude decimal(11, 8) NOT NULL,
  accuracy decimal,
  address text,
  activity_type text CHECK (activity_type IN ('stationary', 'walking', 'running', 'cycling', 'driving', 'transit')),
  duration_minutes integer,
  distance_meters decimal,
  booking_id uuid REFERENCES bookings(id),
  is_safe_location boolean DEFAULT true,
  created_at timestamptz DEFAULT now()
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS location_history_user_date_idx ON location_history (user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS location_history_lat_lon_idx ON location_history (latitude, longitude);

ALTER TABLE location_history ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own location history"
  ON location_history
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

-- Create safety_zones table for geofencing
CREATE TABLE IF NOT EXISTS safety_zones (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text,
  zone_type text NOT NULL CHECK (zone_type IN ('safe', 'caution', 'danger', 'restricted')),
  center_latitude decimal(10, 8) NOT NULL,
  center_longitude decimal(11, 8) NOT NULL,
  radius_meters integer NOT NULL,
  is_active boolean DEFAULT true,
  created_by uuid REFERENCES profiles(id),
  alert_message text,
  auto_notify boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

CREATE INDEX IF NOT EXISTS safety_zones_lat_lon_idx ON safety_zones (center_latitude, center_longitude);

ALTER TABLE safety_zones ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can read active safety zones"
  ON safety_zones
  FOR SELECT
  TO authenticated
  USING (is_active = true);

-- Create proximity_alerts table
CREATE TABLE IF NOT EXISTS proximity_alerts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  target_user_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
  alert_type text NOT NULL CHECK (alert_type IN ('companion_nearby', 'friend_nearby', 'safety_alert', 'meeting_reminder')),
  distance_meters integer NOT NULL,
  message text,
  is_triggered boolean DEFAULT false,
  triggered_at timestamptz,
  expires_at timestamptz,
  created_at timestamptz DEFAULT now()
);

ALTER TABLE proximity_alerts ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own proximity alerts"
  ON proximity_alerts
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

-- Add location fields to companions table for proximity search
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'companions' AND column_name = 'current_latitude'
  ) THEN
    ALTER TABLE companions ADD COLUMN current_latitude decimal(10, 8);
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'companions' AND column_name = 'current_longitude'
  ) THEN
    ALTER TABLE companions ADD COLUMN current_longitude decimal(11, 8);
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'companions' AND column_name = 'location_updated_at'
  ) THEN
    ALTER TABLE companions ADD COLUMN location_updated_at timestamptz;
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'companions' AND column_name = 'search_radius_km'
  ) THEN
    ALTER TABLE companions ADD COLUMN search_radius_km integer DEFAULT 25;
  END IF;
END $$;

-- Add index for companions location
CREATE INDEX IF NOT EXISTS companions_location_idx ON companions (current_latitude, current_longitude) 
WHERE current_latitude IS NOT NULL AND current_longitude IS NOT NULL;

-- Create function to calculate distance between two points using Haversine formula
CREATE OR REPLACE FUNCTION calculate_distance_km(
  lat1 decimal, lon1 decimal, lat2 decimal, lon2 decimal
) RETURNS decimal AS $$
DECLARE
  dlat decimal;
  dlon decimal;
  a decimal;
  c decimal;
  r decimal := 6371; -- Earth's radius in kilometers
BEGIN
  -- Convert degrees to radians
  dlat := radians(lat2 - lat1);
  dlon := radians(lon2 - lon1);
  
  -- Haversine formula
  a := sin(dlat/2) * sin(dlat/2) + cos(radians(lat1)) * cos(radians(lat2)) * sin(dlon/2) * sin(dlon/2);
  c := 2 * atan2(sqrt(a), sqrt(1-a));
  
  RETURN r * c;
END;
$$ LANGUAGE plpgsql;

-- Create function to calculate distance in meters
CREATE OR REPLACE FUNCTION calculate_distance_meters(
  lat1 decimal, lon1 decimal, lat2 decimal, lon2 decimal
) RETURNS decimal AS $$
BEGIN
  RETURN calculate_distance_km(lat1, lon1, lat2, lon2) * 1000;
END;
$$ LANGUAGE plpgsql;

-- Create function to find nearby companions
CREATE OR REPLACE FUNCTION find_nearby_companions(
  user_lat decimal,
  user_lon decimal,
  radius_km integer DEFAULT 25
) RETURNS TABLE (
  companion_id uuid,
  distance_km decimal,
  profile_data jsonb
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.id,
    calculate_distance_km(user_lat, user_lon, c.current_latitude, c.current_longitude) as distance,
    jsonb_build_object(
      'id', p.id,
      'full_name', p.full_name,
      'avatar_url', p.avatar_url,
      'hourly_rate', c.hourly_rate,
      'rating', c.rating,
      'is_verified', c.is_verified,
      'location', c.location
    ) as profile
  FROM companions c
  JOIN profiles p ON c.profile_id = p.id
  WHERE c.is_active = true
    AND c.current_latitude IS NOT NULL
    AND c.current_longitude IS NOT NULL
    AND calculate_distance_km(user_lat, user_lon, c.current_latitude, c.current_longitude) <= radius_km
  ORDER BY distance ASC;
END;
$$ LANGUAGE plpgsql;

-- Create function to trigger emergency alerts
CREATE OR REPLACE FUNCTION trigger_emergency_alert(
  incident_id uuid,
  user_id uuid
) RETURNS void AS $$
DECLARE
  contact_record RECORD;
  incident_data RECORD;
BEGIN
  -- Get incident details
  SELECT * INTO incident_data FROM emergency_incidents WHERE id = incident_id;
  
  -- Notify all emergency contacts
  FOR contact_record IN 
    SELECT * FROM emergency_contacts 
    WHERE emergency_contacts.user_id = trigger_emergency_alert.user_id
    ORDER BY is_primary DESC, is_medical DESC
  LOOP
    -- Update contacts_notified array
    UPDATE emergency_incidents 
    SET contacts_notified = array_append(contacts_notified, contact_record.id::text)
    WHERE id = incident_id;
    
    -- Here you would integrate with notification service
    -- For now, we'll just log the notification
    INSERT INTO support_analytics (date, metric_type, metric_name, metric_value, dimensions)
    VALUES (
      CURRENT_DATE,
      'emergency',
      'contact_notified',
      1,
      jsonb_build_object(
        'incident_id', incident_id,
        'contact_id', contact_record.id,
        'contact_type', contact_record.relationship
      )
    );
  END LOOP;
  
  -- Update incident status
  UPDATE emergency_incidents 
  SET status = 'escalated',
      updated_at = now()
  WHERE id = incident_id;
END;
$$ LANGUAGE plpgsql;

-- Create function to check safety zones
CREATE OR REPLACE FUNCTION check_safety_zones(
  user_id uuid,
  lat decimal,
  lon decimal
) RETURNS TABLE (
  zone_id uuid,
  zone_name text,
  zone_type text,
  distance_meters decimal,
  alert_message text
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    sz.id,
    sz.name,
    sz.zone_type,
    calculate_distance_meters(lat, lon, sz.center_latitude, sz.center_longitude) as distance,
    sz.alert_message
  FROM safety_zones sz
  WHERE sz.is_active = true
    AND calculate_distance_meters(lat, lon, sz.center_latitude, sz.center_longitude) <= sz.radius_meters
  ORDER BY distance ASC;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update companion location
CREATE OR REPLACE FUNCTION update_companion_location()
RETURNS TRIGGER AS $$
BEGIN
  -- Update companion's current location if they are a companion
  UPDATE companions 
  SET 
    current_latitude = NEW.latitude,
    current_longitude = NEW.longitude,
    location_updated_at = NEW.created_at
  WHERE profile_id = NEW.user_id;
  
  -- Archive old location to history
  INSERT INTO location_history (
    user_id, latitude, longitude, accuracy, address, created_at
  ) VALUES (
    NEW.user_id, NEW.latitude, NEW.longitude, NEW.accuracy, NEW.address, NEW.created_at
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_companion_location_trigger
AFTER INSERT ON user_locations
FOR EACH ROW
EXECUTE FUNCTION update_companion_location();

-- Create trigger for emergency incident notifications
CREATE OR REPLACE FUNCTION handle_emergency_incident()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.incident_type = 'sos' AND NEW.status = 'active' THEN
    -- Automatically trigger emergency alerts for SOS incidents
    PERFORM trigger_emergency_alert(NEW.id, NEW.user_id);
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER handle_emergency_incident_trigger
AFTER INSERT ON emergency_incidents
FOR EACH ROW
EXECUTE FUNCTION handle_emergency_incident();

-- Insert sample safety zones for major cities
INSERT INTO safety_zones (name, description, zone_type, center_latitude, center_longitude, radius_meters, alert_message) VALUES
('Times Square Safe Zone', 'Well-lit, high-traffic area with security presence', 'safe', 40.7580, -73.9855, 500, 'You are in a monitored safe zone'),
('Central Park', 'Public park - exercise caution after dark', 'caution', 40.7829, -73.9654, 2000, 'Exercise caution in park areas, especially after dark'),
('Financial District', 'Business district - generally safe during business hours', 'safe', 40.7074, -74.0113, 1000, 'Business district - well monitored during business hours'),
('Hospital District', 'Medical facilities and emergency services nearby', 'safe', 40.7614, -73.9776, 800, 'Emergency medical services nearby')
ON CONFLICT DO NOTHING;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS emergency_incidents_status_idx ON emergency_incidents (status, created_at DESC);
CREATE INDEX IF NOT EXISTS emergency_incidents_user_idx ON emergency_incidents (user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS emergency_contacts_user_primary_idx ON emergency_contacts (user_id, is_primary DESC);
CREATE INDEX IF NOT EXISTS proximity_alerts_user_active_idx ON proximity_alerts (user_id, is_triggered, expires_at);