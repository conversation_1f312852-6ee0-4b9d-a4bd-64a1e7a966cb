/*
  # Comprehensive Video Call and Support System

  1. New Tables
    - video_calls
      - Complete video call session management
      - Support for different call types and recording
    - call_participants
      - Track all participants in video calls
      - Role-based permissions and status tracking
    - support_agents
      - Agent profiles and availability management
      - Skills and department assignments
    - agent_sessions
      - Track agent login/logout and availability
      - Performance metrics and workload management
    - help_articles
      - Knowledge base content management
      - Categories, tags, and analytics
    - article_views
      - Track article engagement and analytics
      - User behavior and content performance
    - support_analytics
      - Comprehensive analytics and reporting
      - Performance metrics and insights
    - live_chat_sessions
      - Enhanced live chat with queue management
      - Agent assignment and escalation
    - live_chat_messages
      - Real-time messaging with rich content
      - File attachments and system messages
    - support_tickets
      - Enhanced ticket system with SLA tracking
      - Priority management and escalation
    - support_messages
      - Threaded messaging within tickets
      - Internal notes and customer communication

  2. Security
    - Enable RLS on all tables
    - Role-based access control
    - Secure agent and customer data separation

  3. Functions
    - Auto-assign agents based on availability
    - Calculate response times and SLA metrics
    - Update analytics in real-time
*/

-- Create support_agents table
CREATE TABLE IF NOT EXISTS support_agents (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  profile_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  agent_code text UNIQUE NOT NULL,
  department text NOT NULL CHECK (department IN ('general', 'technical', 'billing', 'safety', 'escalation')),
  skills text[] DEFAULT '{}',
  languages text[] DEFAULT ARRAY['English'],
  is_active boolean DEFAULT true,
  is_available boolean DEFAULT false,
  max_concurrent_chats integer DEFAULT 3,
  max_concurrent_calls integer DEFAULT 1,
  current_chat_count integer DEFAULT 0,
  current_call_count integer DEFAULT 0,
  total_tickets_resolved integer DEFAULT 0,
  average_response_time interval DEFAULT '0 minutes',
  customer_satisfaction_rating decimal DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE support_agents ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Agents can read own profile"
  ON support_agents
  FOR SELECT
  TO authenticated
  USING (profile_id = auth.uid());

CREATE POLICY "Agents can update own availability"
  ON support_agents
  FOR UPDATE
  TO authenticated
  USING (profile_id = auth.uid());

-- Create agent_sessions table
CREATE TABLE IF NOT EXISTS agent_sessions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  agent_id uuid REFERENCES support_agents(id) ON DELETE CASCADE NOT NULL,
  session_start timestamptz DEFAULT now(),
  session_end timestamptz,
  status text DEFAULT 'active' CHECK (status IN ('active', 'break', 'offline')),
  tickets_handled integer DEFAULT 0,
  chats_handled integer DEFAULT 0,
  calls_handled integer DEFAULT 0,
  total_response_time interval DEFAULT '0 minutes',
  created_at timestamptz DEFAULT now()
);

ALTER TABLE agent_sessions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Agents can manage own sessions"
  ON agent_sessions
  FOR ALL
  TO authenticated
  USING (agent_id IN (SELECT id FROM support_agents WHERE profile_id = auth.uid()));

-- Create enhanced video_calls table
CREATE TABLE IF NOT EXISTS video_calls (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  room_id text UNIQUE NOT NULL,
  call_type text NOT NULL CHECK (call_type IN ('support', 'booking', 'consultation', 'emergency')),
  status text DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'active', 'ended', 'cancelled')),
  host_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
  agent_id uuid REFERENCES support_agents(id) ON DELETE SET NULL,
  recording_enabled boolean DEFAULT false,
  recording_url text,
  screen_sharing_enabled boolean DEFAULT false,
  max_participants integer DEFAULT 2,
  scheduled_at timestamptz,
  started_at timestamptz,
  ended_at timestamptz,
  duration_seconds integer,
  quality_rating integer CHECK (quality_rating >= 1 AND quality_rating <= 5),
  technical_issues jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE video_calls ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own video calls"
  ON video_calls
  FOR SELECT
  TO authenticated
  USING (host_id = auth.uid() OR agent_id IN (
    SELECT id FROM support_agents WHERE profile_id = auth.uid()
  ));

CREATE POLICY "Users can create video calls"
  ON video_calls
  FOR INSERT
  TO authenticated
  WITH CHECK (host_id = auth.uid());

-- Create call_participants table
CREATE TABLE IF NOT EXISTS call_participants (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  call_id uuid REFERENCES video_calls(id) ON DELETE CASCADE NOT NULL,
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  role text NOT NULL CHECK (role IN ('host', 'participant', 'moderator', 'observer')),
  joined_at timestamptz,
  left_at timestamptz,
  is_video_enabled boolean DEFAULT true,
  is_audio_enabled boolean DEFAULT true,
  is_muted boolean DEFAULT false,
  connection_quality text DEFAULT 'good' CHECK (connection_quality IN ('poor', 'fair', 'good', 'excellent')),
  created_at timestamptz DEFAULT now()
);

ALTER TABLE call_participants ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Participants can read own call data"
  ON call_participants
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Participants can update own call status"
  ON call_participants
  FOR UPDATE
  TO authenticated
  USING (user_id = auth.uid());

-- Create enhanced help_articles table
CREATE TABLE IF NOT EXISTS help_articles (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  content text NOT NULL,
  excerpt text,
  category text NOT NULL CHECK (category IN ('getting_started', 'booking', 'payments', 'safety', 'technical', 'account', 'policies')),
  subcategory text,
  tags text[] DEFAULT '{}',
  difficulty_level text DEFAULT 'beginner' CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
  estimated_read_time integer DEFAULT 5, -- minutes
  author_id uuid REFERENCES support_agents(id) ON DELETE SET NULL,
  is_published boolean DEFAULT false,
  is_featured boolean DEFAULT false,
  view_count integer DEFAULT 0,
  helpful_count integer DEFAULT 0,
  not_helpful_count integer DEFAULT 0,
  last_updated_by uuid REFERENCES support_agents(id) ON DELETE SET NULL,
  published_at timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE help_articles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can read published articles"
  ON help_articles
  FOR SELECT
  TO authenticated
  USING (is_published = true);

CREATE POLICY "Agents can manage articles"
  ON help_articles
  FOR ALL
  TO authenticated
  USING (author_id IN (SELECT id FROM support_agents WHERE profile_id = auth.uid()));

-- Create article_views table for analytics
CREATE TABLE IF NOT EXISTS article_views (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  article_id uuid REFERENCES help_articles(id) ON DELETE CASCADE NOT NULL,
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
  session_id text,
  view_duration integer, -- seconds
  scroll_percentage integer DEFAULT 0,
  was_helpful boolean,
  feedback text,
  referrer text,
  user_agent text,
  created_at timestamptz DEFAULT now()
);

ALTER TABLE article_views ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can create article views"
  ON article_views
  FOR INSERT
  TO authenticated
  WITH CHECK (user_id = auth.uid() OR user_id IS NULL);

-- Create enhanced live_chat_sessions table
CREATE TABLE IF NOT EXISTS live_chat_sessions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  agent_id uuid REFERENCES support_agents(id) ON DELETE SET NULL,
  status text DEFAULT 'waiting' CHECK (status IN ('waiting', 'active', 'ended', 'transferred')),
  priority text DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
  queue_position integer,
  category text CHECK (category IN ('general', 'technical', 'billing', 'safety', 'booking')),
  initial_message text,
  wait_time_seconds integer DEFAULT 0,
  response_time_seconds integer,
  resolution_time_seconds integer,
  satisfaction_rating integer CHECK (satisfaction_rating >= 1 AND satisfaction_rating <= 5),
  was_resolved boolean DEFAULT false,
  escalated_to_ticket boolean DEFAULT false,
  ticket_id uuid,
  started_at timestamptz,
  ended_at timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE live_chat_sessions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own chat sessions"
  ON live_chat_sessions
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid() OR agent_id IN (
    SELECT id FROM support_agents WHERE profile_id = auth.uid()
  ));

CREATE POLICY "Users can create chat sessions"
  ON live_chat_sessions
  FOR INSERT
  TO authenticated
  WITH CHECK (user_id = auth.uid());

-- Create live_chat_messages table
CREATE TABLE IF NOT EXISTS live_chat_messages (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id uuid REFERENCES live_chat_sessions(id) ON DELETE CASCADE NOT NULL,
  sender_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  message text NOT NULL,
  message_type text DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'file', 'system', 'quick_reply')),
  file_url text,
  file_name text,
  file_size integer,
  is_internal boolean DEFAULT false, -- for agent notes
  read_at timestamptz,
  created_at timestamptz DEFAULT now()
);

ALTER TABLE live_chat_messages ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read messages in their sessions"
  ON live_chat_messages
  FOR SELECT
  TO authenticated
  USING (session_id IN (
    SELECT id FROM live_chat_sessions
    WHERE user_id = auth.uid() OR agent_id IN (
      SELECT id FROM support_agents WHERE profile_id = auth.uid()
    )
  ) AND (is_internal = false OR sender_id IN (
    SELECT profile_id FROM support_agents WHERE profile_id = auth.uid()
  )));

CREATE POLICY "Users can send messages in their sessions"
  ON live_chat_messages
  FOR INSERT
  TO authenticated
  WITH CHECK (sender_id = auth.uid() AND session_id IN (
    SELECT id FROM live_chat_sessions
    WHERE user_id = auth.uid() OR agent_id IN (
      SELECT id FROM support_agents WHERE profile_id = auth.uid()
    )
  ));

-- Create enhanced support_tickets table
CREATE TABLE IF NOT EXISTS support_tickets (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  agent_id uuid REFERENCES support_agents(id) ON DELETE SET NULL,
  title text NOT NULL,
  description text NOT NULL,
  category text NOT NULL CHECK (category IN ('technical', 'billing', 'safety', 'account', 'booking', 'general')),
  priority text DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
  status text DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'waiting_customer', 'resolved', 'closed')),
  tags text[] DEFAULT '{}',
  sla_due_at timestamptz,
  first_response_at timestamptz,
  resolved_at timestamptz,
  closed_at timestamptz,
  escalation_level integer DEFAULT 0,
  escalated_at timestamptz,
  satisfaction_rating integer CHECK (satisfaction_rating >= 1 AND satisfaction_rating <= 5),
  resolution_feedback text,
  internal_notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE support_tickets ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own tickets"
  ON support_tickets
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid() OR agent_id IN (
    SELECT id FROM support_agents WHERE profile_id = auth.uid()
  ));

CREATE POLICY "Users can create tickets"
  ON support_tickets
  FOR INSERT
  TO authenticated
  WITH CHECK (user_id = auth.uid());

-- Create support_messages table
CREATE TABLE IF NOT EXISTS support_messages (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  ticket_id uuid REFERENCES support_tickets(id) ON DELETE CASCADE NOT NULL,
  sender_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  message text NOT NULL,
  is_internal boolean DEFAULT false,
  attachments jsonb DEFAULT '[]',
  read_at timestamptz,
  created_at timestamptz DEFAULT now()
);

ALTER TABLE support_messages ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read messages in their tickets"
  ON support_messages
  FOR SELECT
  TO authenticated
  USING (ticket_id IN (
    SELECT id FROM support_tickets
    WHERE user_id = auth.uid() OR agent_id IN (
      SELECT id FROM support_agents WHERE profile_id = auth.uid()
    )
  ) AND (is_internal = false OR sender_id IN (
    SELECT profile_id FROM support_agents WHERE profile_id = auth.uid()
  )));

CREATE POLICY "Users can send messages in their tickets"
  ON support_messages
  FOR INSERT
  TO authenticated
  WITH CHECK (sender_id = auth.uid() AND ticket_id IN (
    SELECT id FROM support_tickets
    WHERE user_id = auth.uid() OR agent_id IN (
      SELECT id FROM support_agents WHERE profile_id = auth.uid()
    )
  ));

-- Create support_analytics table
CREATE TABLE IF NOT EXISTS support_analytics (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  date date NOT NULL,
  metric_type text NOT NULL CHECK (metric_type IN ('tickets', 'chats', 'calls', 'articles', 'agents')),
  metric_name text NOT NULL,
  metric_value decimal NOT NULL,
  dimensions jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now()
);

ALTER TABLE support_analytics ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Agents can read analytics"
  ON support_analytics
  FOR SELECT
  TO authenticated
  USING (EXISTS (SELECT 1 FROM support_agents WHERE profile_id = auth.uid()));

-- Insert sample help articles
INSERT INTO help_articles (title, content, excerpt, category, tags, is_published, published_at) VALUES
('How to Book a Companion', 'Step-by-step guide on booking a companion through our platform...', 'Learn how to book a companion in just a few simple steps', 'booking', ARRAY['booking', 'getting-started'], true, now()),
('Payment Methods and Billing', 'Information about accepted payment methods and billing cycles...', 'Everything you need to know about payments and billing', 'payments', ARRAY['payments', 'billing'], true, now()),
('Safety Guidelines', 'Important safety guidelines for users and companions...', 'Stay safe while using our platform', 'safety', ARRAY['safety', 'guidelines'], true, now()),
('Account Verification Process', 'How to verify your account and increase trust...', 'Complete guide to account verification', 'account', ARRAY['verification', 'account'], true, now()),
('Troubleshooting Common Issues', 'Solutions to frequently encountered technical problems...', 'Quick fixes for common technical issues', 'technical', ARRAY['troubleshooting', 'technical'], true, now()),
('Privacy and Data Protection', 'How we protect your personal information...', 'Understanding our privacy and data protection measures', 'policies', ARRAY['privacy', 'data-protection'], true, now())
ON CONFLICT DO NOTHING;

-- Create functions for analytics and automation

-- Function to update article view count
CREATE OR REPLACE FUNCTION update_article_view_count()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE help_articles
  SET view_count = view_count + 1,
      updated_at = now()
  WHERE id = NEW.article_id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_article_view_count_trigger
AFTER INSERT ON article_views
FOR EACH ROW
EXECUTE FUNCTION update_article_view_count();

-- Function to auto-assign agents to chat sessions
CREATE OR REPLACE FUNCTION auto_assign_chat_agent()
RETURNS TRIGGER AS $$
DECLARE
  available_agent_id uuid;
BEGIN
  IF NEW.status = 'waiting' AND NEW.agent_id IS NULL THEN
    -- Find available agent with lowest current chat count
    SELECT id INTO available_agent_id
    FROM support_agents
    WHERE is_active = true
      AND is_available = true
      AND current_chat_count < max_concurrent_chats
      AND (NEW.category IS NULL OR NEW.category = ANY(skills) OR 'general' = ANY(skills))
    ORDER BY current_chat_count ASC, RANDOM()
    LIMIT 1;
    
    IF available_agent_id IS NOT NULL THEN
      NEW.agent_id := available_agent_id;
      NEW.status := 'active';
      NEW.started_at := now();
      
      -- Update agent's current chat count
      UPDATE support_agents
      SET current_chat_count = current_chat_count + 1,
          updated_at = now()
      WHERE id = available_agent_id;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER auto_assign_chat_agent_trigger
BEFORE INSERT OR UPDATE ON live_chat_sessions
FOR EACH ROW
EXECUTE FUNCTION auto_assign_chat_agent();

-- Function to calculate SLA due dates for tickets
CREATE OR REPLACE FUNCTION calculate_ticket_sla()
RETURNS TRIGGER AS $$
DECLARE
  sla_hours integer;
BEGIN
  -- Set SLA based on priority
  CASE NEW.priority
    WHEN 'urgent' THEN sla_hours := 2;
    WHEN 'high' THEN sla_hours := 8;
    WHEN 'medium' THEN sla_hours := 24;
    WHEN 'low' THEN sla_hours := 72;
    ELSE sla_hours := 24;
  END CASE;
  
  NEW.sla_due_at := NEW.created_at + (sla_hours || ' hours')::interval;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER calculate_ticket_sla_trigger
BEFORE INSERT ON support_tickets
FOR EACH ROW
EXECUTE FUNCTION calculate_ticket_sla();

-- Function to update agent statistics
CREATE OR REPLACE FUNCTION update_agent_stats()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_TABLE_NAME = 'support_tickets' AND NEW.status = 'resolved' AND OLD.status != 'resolved' THEN
    UPDATE support_agents
    SET total_tickets_resolved = total_tickets_resolved + 1,
        updated_at = now()
    WHERE id = NEW.agent_id;
  END IF;
  
  IF TG_TABLE_NAME = 'live_chat_sessions' AND NEW.status = 'ended' AND OLD.status != 'ended' THEN
    UPDATE support_agents
    SET current_chat_count = GREATEST(current_chat_count - 1, 0),
        updated_at = now()
    WHERE id = NEW.agent_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_agent_stats_tickets_trigger
AFTER UPDATE ON support_tickets
FOR EACH ROW
EXECUTE FUNCTION update_agent_stats();

CREATE TRIGGER update_agent_stats_chats_trigger
AFTER UPDATE ON live_chat_sessions
FOR EACH ROW
EXECUTE FUNCTION update_agent_stats();